package com.fsl.app.data.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u0000 %2\u00020\u0001:\u0001%B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0015J\u0006\u0010\u0017\u001a\u00020\u0018J\u0006\u0010\u0019\u001a\u00020\u0018J\u0006\u0010\u001a\u001a\u00020\u001bJ\u000e\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u0018J\u000e\u0010\u001f\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u0015J\u0016\u0010!\u001a\u00020\u00152\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u001b\u0010\u000b\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\n\u001a\u0004\b\f\u0010\bR\u001b\u0010\u000e\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0010\u0010\n\u001a\u0004\b\u000f\u0010\b\u00a8\u0006&"}, d2 = {"Lcom/fsl/app/data/utils/FileManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "exportsDir", "Ljava/io/File;", "getExportsDir", "()Ljava/io/File;", "exportsDir$delegate", "Lkotlin/Lazy;", "imagesDir", "getImagesDir", "imagesDir$delegate", "modelsDir", "getModelsDir", "modelsDir$delegate", "cleanupTempFiles", "", "deleteClassFiles", "className", "", "exportModel", "getModelLastModified", "", "getModelSize", "getStorageUsage", "Lcom/fsl/app/data/utils/StorageInfo;", "hasEnoughSpace", "", "requiredBytes", "importModel", "modelPath", "saveBitmap", "bitmap", "Landroid/graphics/Bitmap;", "fileName", "Companion", "app_debug"})
@javax.inject.Singleton
public final class FileManager {
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.utils.FileManager.Companion Companion = null;
    private static final java.lang.String IMAGES_DIR = "images";
    private static final java.lang.String MODELS_DIR = "models";
    private static final java.lang.String EXPORTS_DIR = "exports";
    private final kotlin.Lazy imagesDir$delegate = null;
    private final kotlin.Lazy modelsDir$delegate = null;
    private final kotlin.Lazy exportsDir$delegate = null;
    
    @javax.inject.Inject
    public FileManager(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        super();
    }
    
    private final java.io.File getImagesDir() {
        return null;
    }
    
    private final java.io.File getModelsDir() {
        return null;
    }
    
    private final java.io.File getExportsDir() {
        return null;
    }
    
    /**
     * 保存Bitmap到文件
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String saveBitmap(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull
    java.lang.String fileName) {
        return null;
    }
    
    /**
     * 删除类别相关的所有文件
     */
    public final void deleteClassFiles(@org.jetbrains.annotations.NotNull
    java.lang.String className) {
    }
    
    /**
     * 导出模型
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String exportModel() {
        return null;
    }
    
    /**
     * 导入模型
     */
    public final void importModel(@org.jetbrains.annotations.NotNull
    java.lang.String modelPath) {
    }
    
    /**
     * 获取模型大小
     */
    public final long getModelSize() {
        return 0L;
    }
    
    /**
     * 获取模型最后修改时间
     */
    public final long getModelLastModified() {
        return 0L;
    }
    
    /**
     * 清理临时文件
     */
    public final void cleanupTempFiles() {
    }
    
    /**
     * 获取存储使用情况
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.data.utils.StorageInfo getStorageUsage() {
        return null;
    }
    
    /**
     * 检查存储空间是否足够
     */
    public final boolean hasEnoughSpace(long requiredBytes) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/fsl/app/data/utils/FileManager$Companion;", "", "()V", "EXPORTS_DIR", "", "IMAGES_DIR", "MODELS_DIR", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}