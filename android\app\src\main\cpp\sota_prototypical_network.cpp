/**
 * SOTA原型网络实现文件 - 基于easyfsl实现
 *
 * 高性能原型网络算法的C++实现，使用NEON加速
 * 严格按照easyfsl.methods.prototypical_networks实现
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "include/prototypical_network.h"
#include <cmath>
#include <algorithm>
#include <numeric>
#include <limits>
#include <android/log.h>

#define LOG_TAG "SOTAPrototypicalNetwork"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

namespace fsl {

PrototypicalNetwork::PrototypicalNetwork()
    : featureDim_(0), useSoftmax_(false), featureNormalization_(2.0f) {
    LOGI("SOTA PrototypicalNetwork构造函数");
}

PrototypicalNetwork::~PrototypicalNetwork() {
    LOGI("SOTA PrototypicalNetwork析构函数");
}

bool PrototypicalNetwork::initialize(int featureDim, bool useSoftmax,
                                   const FeatureVector* featureCentering,
                                   float featureNormalization) {
    if (featureDim <= 0) {
        LOGE("无效的特征维度: %d", featureDim);
        return false;
    }

    featureDim_ = featureDim;
    useSoftmax_ = useSoftmax;
    featureNormalization_ = featureNormalization;

    // 设置特征中心化
    if (featureCentering != nullptr) {
        featureCentering_ = *featureCentering;
        LOGI("启用特征中心化，维度: %zu", featureCentering_.size());
    } else {
        featureCentering_.clear();
    }

    fslPrototypes_.clear();
    pretrainedPrototypes_.clear();

    LOGI("SOTA PrototypicalNetwork初始化成功");
    LOGI("特征维度: %d, useSoftmax: %s, featureNormalization: %.2f",
         featureDim_, useSoftmax_ ? "true" : "false", featureNormalization_);
    LOGI("NEON支持: %s", isNeonAvailable() ? "是" : "否");

    return true;
}

bool PrototypicalNetwork::loadPretrainedWeights(const std::vector<std::string>& classNames,
                                               const FeatureMatrix& prototypes) {
    if (classNames.size() != prototypes.size()) {
        LOGE("类别名称数量与原型数量不匹配: %zu vs %zu", classNames.size(), prototypes.size());
        return false;
    }

    pretrainedPrototypes_.clear();

    for (size_t i = 0; i < classNames.size(); ++i) {
        if (!validateFeatureDim(prototypes[i])) {
            LOGE("预训练原型维度无效: %s", classNames[i].c_str());
            return false;
        }

        // 归一化预训练原型
        FeatureVector normalizedPrototype = normalizeL2(prototypes[i]);
        pretrainedPrototypes_[classNames[i]] = normalizedPrototype;
    }

    LOGI("加载预训练权重成功，类别数: %zu", classNames.size());
    return true;
}

bool PrototypicalNetwork::addPrototype(const std::string& className, const FeatureVector& prototype) {
    if (!validateFeatureDim(prototype)) {
        LOGE("FSL原型维度无效: %s", className.c_str());
        return false;
    }

    if (className.empty()) {
        LOGE("类别名称为空");
        return false;
    }

    // 归一化FSL原型
    FeatureVector normalizedPrototype = normalizeL2(prototype);
    fslPrototypes_[className] = normalizedPrototype;

    LOGI("添加FSL原型: %s", className.c_str());
    return true;
}

bool PrototypicalNetwork::updatePrototype(const std::string& className, const FeatureVector& prototype) {
    if (!validateFeatureDim(prototype)) {
        LOGE("更新原型维度无效: %s", className.c_str());
        return false;
    }

    auto it = fslPrototypes_.find(className);
    if (it == fslPrototypes_.end()) {
        LOGE("FSL类别未找到: %s", className.c_str());
        return false;
    }

    // 归一化并更新FSL原型
    FeatureVector normalizedPrototype = normalizeL2(prototype);
    it->second = normalizedPrototype;

    LOGI("更新FSL原型: %s", className.c_str());
    return true;
}

bool PrototypicalNetwork::removePrototype(const std::string& className) {
    auto it = fslPrototypes_.find(className);
    if (it == fslPrototypes_.end()) {
        LOGE("FSL类别未找到: %s", className.c_str());
        return false;
    }

    fslPrototypes_.erase(it);
    LOGI("移除FSL原型: %s", className.c_str());
    return true;
}

// 基于easyfsl.PrototypicalNetworks.forward实现的分类方法
std::pair<std::string, float> PrototypicalNetwork::classify(const FeatureVector& queryFeature) {
    if (!validateFeatureDim(queryFeature)) {
        LOGE("查询特征维度无效");
        return std::make_pair("unknown", 0.0f);
    }

    // 预处理查询特征
    FeatureVector processedQuery = queryFeature;

    // 特征中心化
    if (!featureCentering_.empty()) {
        featureCenteringNeon(processedQuery);
    }

    // L2归一化
    if (featureNormalization_ > 0.0f) {
        if (isNeonAvailable()) {
            normalizeL2Neon(processedQuery);
        } else {
            processedQuery = normalizeL2(processedQuery);
        }
    }

    std::string bestClass;
    float bestScore = -std::numeric_limits<float>::infinity();

    // 优先使用FSL原型
    if (!fslPrototypes_.empty()) {
        for (const auto& pair : fslPrototypes_) {
            float distance;
            if (isNeonAvailable()) {
                distance = -l2DistanceNeon(processedQuery, pair.second);
            } else {
                distance = -euclideanDistance(processedQuery, pair.second);
            }

            if (distance > bestScore) {
                bestScore = distance;
                bestClass = pair.first;
            }
        }
    } else if (!pretrainedPrototypes_.empty()) {
        // 回退到预训练权重
        LOGD("使用预训练权重进行分类");
        for (const auto& pair : pretrainedPrototypes_) {
            float distance;
            if (isNeonAvailable()) {
                distance = -l2DistanceNeon(processedQuery, pair.second);
            } else {
                distance = -euclideanDistance(processedQuery, pair.second);
            }

            if (distance > bestScore) {
                bestScore = distance;
                bestClass = pair.first;
            }
        }
    } else {
        LOGE("没有可用的原型");
        return std::make_pair("unknown", 0.0f);
    }

    // 转换为置信度
    float confidence;
    if (useSoftmax_) {
        // 使用softmax转换
        confidence = 1.0f / (1.0f + std::exp(-bestScore));
    } else {
        // 简单的距离转换
        confidence = std::max(0.0f, std::min(1.0f, (bestScore + 10.0f) / 20.0f));
    }

    return std::make_pair(bestClass, confidence);
}

// 基于easyfsl.l2_distance_to_prototypes的分数计算
std::map<std::string, float> PrototypicalNetwork::computeAllScores(const FeatureVector& queryFeature) {
    std::map<std::string, float> scores;

    if (!validateFeatureDim(queryFeature)) {
        LOGE("查询特征维度无效");
        return scores;
    }

    // 预处理查询特征
    FeatureVector processedQuery = queryFeature;

    // 特征中心化
    if (!featureCentering_.empty()) {
        featureCenteringNeon(processedQuery);
    }

    // L2归一化
    if (featureNormalization_ > 0.0f) {
        if (isNeonAvailable()) {
            normalizeL2Neon(processedQuery);
        } else {
            processedQuery = normalizeL2(processedQuery);
        }
    }

    std::vector<float> distances;
    std::vector<std::string> classNames;

    // 优先使用FSL原型
    if (!fslPrototypes_.empty()) {
        for (const auto& pair : fslPrototypes_) {
            float distance;
            if (isNeonAvailable()) {
                distance = -l2DistanceNeon(processedQuery, pair.second);
            } else {
                distance = -euclideanDistance(processedQuery, pair.second);
            }
            distances.push_back(distance);
            classNames.push_back(pair.first);
        }
    } else if (!pretrainedPrototypes_.empty()) {
        // 回退到预训练权重
        for (const auto& pair : pretrainedPrototypes_) {
            float distance;
            if (isNeonAvailable()) {
                distance = -l2DistanceNeon(processedQuery, pair.second);
            } else {
                distance = -euclideanDistance(processedQuery, pair.second);
            }
            distances.push_back(distance);
            classNames.push_back(pair.first);
        }
    }

    if (distances.empty()) {
        LOGE("没有可用的原型");
        return scores;
    }

    // 应用softmax或直接使用距离
    std::vector<float> finalScores;
    if (useSoftmax_) {
        finalScores = softmax(distances);
    } else {
        finalScores = distances;
    }

    // 构建结果映射
    for (size_t i = 0; i < classNames.size(); ++i) {
        scores[classNames[i]] = finalScores[i];
    }

    return scores;
}

// 基于easyfsl.methods.utils.compute_prototypes的原型计算
FeatureVector PrototypicalNetwork::computePrototype(const FeatureMatrix& supportFeatures) {
    if (supportFeatures.empty()) {
        LOGE("支持集特征为空");
        return FeatureVector();
    }

    if (!validateFeatureDim(supportFeatures[0])) {
        LOGE("支持集特征维度无效");
        return FeatureVector();
    }

    int featureDim = supportFeatures[0].size();
    FeatureVector prototype(featureDim, 0.0f);

    // 计算均值原型
    for (const auto& feature : supportFeatures) {
        if (feature.size() != static_cast<size_t>(featureDim)) {
            LOGE("支持集特征维度不一致");
            return FeatureVector();
        }

        if (isNeonAvailable()) {
            // NEON加速的向量加法
            for (int i = 0; i < featureDim; i += 4) {
                if (i + 3 < featureDim) {
                    float32x4_t proto_vec = vld1q_f32(&prototype[i]);
                    float32x4_t feat_vec = vld1q_f32(&feature[i]);
                    float32x4_t result = vaddq_f32(proto_vec, feat_vec);
                    vst1q_f32(&prototype[i], result);
                } else {
                    // 处理剩余元素
                    for (int j = i; j < featureDim; ++j) {
                        prototype[j] += feature[j];
                    }
                }
            }
        } else {
            for (int i = 0; i < featureDim; ++i) {
                prototype[i] += feature[i];
            }
        }
    }

    // 计算均值
    float numSamples = static_cast<float>(supportFeatures.size());
    if (isNeonAvailable()) {
        float32x4_t num_vec = vdupq_n_f32(numSamples);
        for (int i = 0; i < featureDim; i += 4) {
            if (i + 3 < featureDim) {
                float32x4_t proto_vec = vld1q_f32(&prototype[i]);
                float32x4_t result = vdivq_f32(proto_vec, num_vec);
                vst1q_f32(&prototype[i], result);
            } else {
                for (int j = i; j < featureDim; ++j) {
                    prototype[j] /= numSamples;
                }
            }
        }
    } else {
        for (int i = 0; i < featureDim; ++i) {
            prototype[i] /= numSamples;
        }
    }

    // L2归一化
    if (isNeonAvailable()) {
        normalizeL2Neon(prototype);
    } else {
        prototype = normalizeL2(prototype);
    }

    return prototype;
}

bool PrototypicalNetwork::hasLearnedClasses() const {
    return !fslPrototypes_.empty();
}

bool PrototypicalNetwork::hasPretrainedWeights() const {
    return !pretrainedPrototypes_.empty();
}

const std::map<std::string, FeatureVector>& PrototypicalNetwork::getPrototypes() const {
    // 返回FSL原型，如果为空则返回预训练原型
    if (!fslPrototypes_.empty()) {
        return fslPrototypes_;
    } else {
        return pretrainedPrototypes_;
    }
}

void PrototypicalNetwork::clearFSLPrototypes() {
    fslPrototypes_.clear();
    LOGI("清除FSL原型");
}

void PrototypicalNetwork::clear() {
    fslPrototypes_.clear();
    pretrainedPrototypes_.clear();
    LOGI("清除所有原型");
}

// NEON加速的L2距离计算
float PrototypicalNetwork::l2DistanceNeon(const FeatureVector& a, const FeatureVector& b) {
#ifdef __ARM_NEON
    if (a.size() != b.size()) {
        return std::numeric_limits<float>::max();
    }

    float32x4_t sum_vec = vdupq_n_f32(0.0f);
    size_t size = a.size();
    size_t i = 0;

    // NEON向量化处理
    for (; i + 3 < size; i += 4) {
        float32x4_t a_vec = vld1q_f32(&a[i]);
        float32x4_t b_vec = vld1q_f32(&b[i]);
        float32x4_t diff = vsubq_f32(a_vec, b_vec);
        float32x4_t sq_diff = vmulq_f32(diff, diff);
        sum_vec = vaddq_f32(sum_vec, sq_diff);
    }

    // 累加NEON结果
    float sum = vgetq_lane_f32(sum_vec, 0) + vgetq_lane_f32(sum_vec, 1) +
                vgetq_lane_f32(sum_vec, 2) + vgetq_lane_f32(sum_vec, 3);

    // 处理剩余元素
    for (; i < size; ++i) {
        float diff = a[i] - b[i];
        sum += diff * diff;
    }

    return std::sqrt(sum);
#else
    return euclideanDistance(a, b);
#endif
}

// NEON加速的点积计算
float PrototypicalNetwork::dotProductNeon(const FeatureVector& a, const FeatureVector& b) {
#ifdef __ARM_NEON
    if (a.size() != b.size()) {
        return 0.0f;
    }

    float32x4_t sum_vec = vdupq_n_f32(0.0f);
    size_t size = a.size();
    size_t i = 0;

    // NEON向量化处理
    for (; i + 3 < size; i += 4) {
        float32x4_t a_vec = vld1q_f32(&a[i]);
        float32x4_t b_vec = vld1q_f32(&b[i]);
        float32x4_t prod = vmulq_f32(a_vec, b_vec);
        sum_vec = vaddq_f32(sum_vec, prod);
    }

    // 累加NEON结果
    float sum = vgetq_lane_f32(sum_vec, 0) + vgetq_lane_f32(sum_vec, 1) +
                vgetq_lane_f32(sum_vec, 2) + vgetq_lane_f32(sum_vec, 3);

    // 处理剩余元素
    for (; i < size; ++i) {
        sum += a[i] * b[i];
    }

    return sum;
#else
    return dotProduct(a, b);
#endif
}

// NEON加速的L2归一化
void PrototypicalNetwork::normalizeL2Neon(FeatureVector& vector) {
#ifdef __ARM_NEON
    // 计算L2范数
    float32x4_t sum_vec = vdupq_n_f32(0.0f);
    size_t size = vector.size();
    size_t i = 0;

    for (; i + 3 < size; i += 4) {
        float32x4_t v = vld1q_f32(&vector[i]);
        float32x4_t sq = vmulq_f32(v, v);
        sum_vec = vaddq_f32(sum_vec, sq);
    }

    float norm_sq = vgetq_lane_f32(sum_vec, 0) + vgetq_lane_f32(sum_vec, 1) +
                    vgetq_lane_f32(sum_vec, 2) + vgetq_lane_f32(sum_vec, 3);

    for (; i < size; ++i) {
        norm_sq += vector[i] * vector[i];
    }

    float norm = std::sqrt(norm_sq);
    if (norm == 0.0f) return;

    // 归一化
    float32x4_t norm_vec = vdupq_n_f32(norm);
    i = 0;
    for (; i + 3 < size; i += 4) {
        float32x4_t v = vld1q_f32(&vector[i]);
        float32x4_t normalized = vdivq_f32(v, norm_vec);
        vst1q_f32(&vector[i], normalized);
    }

    for (; i < size; ++i) {
        vector[i] /= norm;
    }
#else
    FeatureVector normalized = normalizeL2(vector);
    vector = normalized;
#endif
}

// NEON加速的特征中心化
void PrototypicalNetwork::featureCenteringNeon(FeatureVector& features) {
#ifdef __ARM_NEON
    if (featureCentering_.empty() || features.size() != featureCentering_.size()) {
        return;
    }

    size_t size = features.size();
    size_t i = 0;

    for (; i + 3 < size; i += 4) {
        float32x4_t feat_vec = vld1q_f32(&features[i]);
        float32x4_t center_vec = vld1q_f32(&featureCentering_[i]);
        float32x4_t result = vsubq_f32(feat_vec, center_vec);
        vst1q_f32(&features[i], result);
    }

    for (; i < size; ++i) {
        features[i] -= featureCentering_[i];
    }
#else
    if (featureCentering_.empty() || features.size() != featureCentering_.size()) {
        return;
    }

    for (size_t i = 0; i < features.size(); ++i) {
        features[i] -= featureCentering_[i];
    }
#endif
}

// 标准算法实现（回退）
float PrototypicalNetwork::euclideanDistance(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return std::numeric_limits<float>::max();
    }

    float distance = 0.0f;
    for (size_t i = 0; i < a.size(); ++i) {
        float diff = a[i] - b[i];
        distance += diff * diff;
    }

    return std::sqrt(distance);
}

float PrototypicalNetwork::cosineSimilarity(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return 0.0f;
    }

    float dotProd = dotProduct(a, b);
    float normA = l2Norm(a);
    float normB = l2Norm(b);

    if (normA == 0.0f || normB == 0.0f) {
        return 0.0f;
    }

    return dotProd / (normA * normB);
}

FeatureVector PrototypicalNetwork::normalizeL2(const FeatureVector& vector) {
    float norm = l2Norm(vector);

    if (norm == 0.0f) {
        return vector;
    }

    FeatureVector normalized(vector.size());
    for (size_t i = 0; i < vector.size(); ++i) {
        normalized[i] = vector[i] / norm;
    }

    return normalized;
}

float PrototypicalNetwork::l2Norm(const FeatureVector& vector) {
    float norm = 0.0f;
    for (float val : vector) {
        norm += val * val;
    }
    return std::sqrt(norm);
}

float PrototypicalNetwork::dotProduct(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return 0.0f;
    }

    float product = 0.0f;
    for (size_t i = 0; i < a.size(); ++i) {
        product += a[i] * b[i];
    }

    return product;
}

std::vector<float> PrototypicalNetwork::softmax(const std::vector<float>& scores) {
    if (scores.empty()) {
        return std::vector<float>();
    }

    // 找到最大值以提高数值稳定性
    float maxScore = *std::max_element(scores.begin(), scores.end());

    // 计算指数
    std::vector<float> expScores(scores.size());
    float sumExp = 0.0f;

    for (size_t i = 0; i < scores.size(); ++i) {
        expScores[i] = std::exp(scores[i] - maxScore);
        sumExp += expScores[i];
    }

    // 归一化
    std::vector<float> probabilities(scores.size());
    for (size_t i = 0; i < scores.size(); ++i) {
        probabilities[i] = expScores[i] / sumExp;
    }

    return probabilities;
}

bool PrototypicalNetwork::validateFeatureDim(const FeatureVector& feature) {
    return featureDim_ > 0 && static_cast<int>(feature.size()) == featureDim_;
}

bool PrototypicalNetwork::isNeonAvailable() const {
#ifdef __ARM_NEON
    return true;
#else
    return false;
#endif
}

} // namespace fsl