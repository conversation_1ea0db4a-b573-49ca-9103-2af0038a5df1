package com.fsl.app.presentation.gallery;

import java.lang.System;

/**
 * 图库状态
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0018\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bm\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u001a\b\u0002\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u001b\u0010\u001e\u001a\u0014\u0012\u0004\u0012\u00020\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\tH\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\nH\u00c6\u0003Jq\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u001a\b\u0002\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\nH\u00c6\u0001J\u0013\u0010#\u001a\u00020\u00032\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\nH\u00d6\u0001R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R#\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0016R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0011R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0013R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006("}, d2 = {"Lcom/fsl/app/presentation/gallery/GalleryState;", "", "isLoading", "", "allImages", "", "Lcom/fsl/app/domain/model/GalleryImage;", "learnedImages", "imagesByClass", "", "", "selectedClass", "stats", "Lcom/fsl/app/presentation/gallery/GalleryStats;", "error", "(ZLjava/util/List;Ljava/util/List;Ljava/util/Map;Ljava/lang/String;Lcom/fsl/app/presentation/gallery/GalleryStats;Ljava/lang/String;)V", "getAllImages", "()Ljava/util/List;", "getError", "()Ljava/lang/String;", "getImagesByClass", "()Ljava/util/Map;", "()Z", "getLearnedImages", "getSelectedClass", "getStats", "()Lcom/fsl/app/presentation/gallery/GalleryStats;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class GalleryState {
    private final boolean isLoading = false;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.fsl.app.domain.model.GalleryImage> allImages = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.fsl.app.domain.model.GalleryImage> learnedImages = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Map<java.lang.String, java.util.List<com.fsl.app.domain.model.GalleryImage>> imagesByClass = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String selectedClass = null;
    @org.jetbrains.annotations.NotNull
    private final com.fsl.app.presentation.gallery.GalleryStats stats = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    
    /**
     * 图库状态
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.gallery.GalleryState copy(boolean isLoading, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.GalleryImage> allImages, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.GalleryImage> learnedImages, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, ? extends java.util.List<com.fsl.app.domain.model.GalleryImage>> imagesByClass, @org.jetbrains.annotations.Nullable
    java.lang.String selectedClass, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.gallery.GalleryStats stats, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        return null;
    }
    
    /**
     * 图库状态
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 图库状态
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 图库状态
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public GalleryState() {
        super();
    }
    
    public GalleryState(boolean isLoading, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.GalleryImage> allImages, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.GalleryImage> learnedImages, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, ? extends java.util.List<com.fsl.app.domain.model.GalleryImage>> imagesByClass, @org.jetbrains.annotations.Nullable
    java.lang.String selectedClass, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.gallery.GalleryStats stats, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.GalleryImage> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.GalleryImage> getAllImages() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.GalleryImage> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.GalleryImage> getLearnedImages() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.util.List<com.fsl.app.domain.model.GalleryImage>> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.util.List<com.fsl.app.domain.model.GalleryImage>> getImagesByClass() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSelectedClass() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.gallery.GalleryStats component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.gallery.GalleryStats getStats() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
}