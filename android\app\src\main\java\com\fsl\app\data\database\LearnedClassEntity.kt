/**
 * 已学习类别实体
 *
 * 数据库中存储的已学习类别信息
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.database

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.fsl.app.domain.model.LearnedClass

@Entity(tableName = "learned_classes")
data class LearnedClassEntity(
    @PrimaryKey val id: String,
    val name: String,
    val sampleCount: Int,
    val accuracy: Float = 0f,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 训练样本实体
 */
@Entity(tableName = "training_samples")
data class TrainingSampleEntity(
    @PrimaryKey val id: String,
    val classId: String,
    val imagePath: String,
    val features: ByteArray?, // 预提取的特征
    val createdAt: Long
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TrainingSampleEntity

        if (id != other.id) return false
        if (classId != other.classId) return false
        if (imagePath != other.imagePath) return false
        if (features != null) {
            if (other.features == null) return false
            if (!features.contentEquals(other.features)) return false
        } else if (other.features != null) return false
        if (createdAt != other.createdAt) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + classId.hashCode()
        result = 31 * result + imagePath.hashCode()
        result = 31 * result + (features?.contentHashCode() ?: 0)
        result = 31 * result + createdAt.hashCode()
        return result
    }
}

/**
 * 扩展函数：将实体转换为领域模型
 */
fun LearnedClassEntity.toDomainModel(): com.fsl.app.domain.model.LearnedClass {
    return com.fsl.app.domain.model.LearnedClass(
        name = name,
        sampleCount = sampleCount,
        accuracy = accuracy
    )
}

/**
 * 扩展函数：将领域模型转换为实体
 */
fun LearnedClass.toEntity(id: String = java.util.UUID.randomUUID().toString()): LearnedClassEntity {
    val currentTime = System.currentTimeMillis()
    return LearnedClassEntity(
        id = id,
        name = name,
        sampleCount = sampleCount,
        accuracy = 0.85f, // 默认准确率
        createdAt = currentTime,
        updatedAt = currentTime
    )
}
