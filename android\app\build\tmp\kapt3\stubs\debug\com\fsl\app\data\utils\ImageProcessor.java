package com.fsl.app.data.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0002\b\u0003\b\u0007\u0018\u0000 \n2\u00020\u0001:\u0001\nB\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0006\u001a\u00020\u0005J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u0005J\u000e\u0010\t\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0005\u00a8\u0006\u000b"}, d2 = {"Lcom/fsl/app/data/utils/ImageProcessor;", "", "()V", "augmentImage", "", "Landroid/graphics/Bitmap;", "bitmap", "bitmapToTensor", "", "preprocessImage", "Companion", "app_debug"})
@javax.inject.Singleton
public final class ImageProcessor {
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.utils.ImageProcessor.Companion Companion = null;
    private static final int INPUT_SIZE = 224;
    private static final float[] IMAGENET_MEAN = {0.485F, 0.456F, 0.406F};
    private static final float[] IMAGENET_STD = {0.229F, 0.224F, 0.225F};
    
    @javax.inject.Inject
    public ImageProcessor() {
        super();
    }
    
    /**
     * 将Bitmap转换为模型输入张量
     */
    @org.jetbrains.annotations.NotNull
    public final float[] bitmapToTensor(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 预处理图像
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap preprocessImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 数据增强
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.Bitmap> augmentImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/fsl/app/data/utils/ImageProcessor$Companion;", "", "()V", "IMAGENET_MEAN", "", "IMAGENET_STD", "INPUT_SIZE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}