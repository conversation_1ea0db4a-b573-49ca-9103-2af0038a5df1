package com.fsl.app.data.inference;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0014\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010!\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0015\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\'\b\u0007\u0018\u0000 p2\u00020\u0001:\u0001pB+\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u001f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u000e2\u0006\u0010!\u001a\u00020\u0010H\u0002J\u0010\u0010\"\u001a\u00020\u000e2\u0006\u0010#\u001a\u00020$H\u0002J*\u0010%\u001a\b\u0012\u0004\u0012\u00020\'0&2\u0006\u0010(\u001a\u00020$H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b)\u0010*J\u0010\u0010+\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020$H\u0002J\u0018\u0010,\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020$2\u0006\u0010-\u001a\u00020\u0016H\u0002J\u0016\u0010.\u001a\u00020\u000e2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000e0/H\u0002J\u0016\u00100\u001a\u00020\u000e2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000e0/H\u0002J*\u00101\u001a\b\u0012\u0004\u0012\u0002020&2\u0006\u00103\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u00105J\u0018\u00106\u001a\u00020\u00122\u0006\u00107\u001a\u00020\u000e2\u0006\u00108\u001a\u00020\u000eH\u0002J\u0010\u00109\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020$H\u0002J6\u0010:\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0/0&2\f\u0010;\u001a\b\u0012\u0004\u0012\u00020$0/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b<\u0010=J>\u0010:\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0/0&2\f\u0010;\u001a\b\u0012\u0004\u0012\u00020$0/2\u0006\u0010-\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b>\u0010?J\u0010\u0010@\u001a\u00020\u000e2\u0006\u0010A\u001a\u00020BH\u0002J\u0010\u0010C\u001a\u00020\u000e2\u0006\u0010A\u001a\u00020BH\u0002J%\u0010D\u001a\b\u0012\u0004\u0012\u00020\u000e0/2\f\u0010;\u001a\b\u0012\u0004\u0012\u00020$0/H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010=J\u0010\u0010E\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020$H\u0002J\u0016\u0010F\u001a\u00020\u000e2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000e0/H\u0002J\f\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00190/J\u0014\u0010H\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020J0IH\u0016J\u0010\u0010K\u001a\u00020\u000e2\u0006\u0010L\u001a\u00020\u0010H\u0002J\u0010\u0010M\u001a\u00020\u000e2\u0006\u0010N\u001a\u00020\u0010H\u0002J\u0006\u0010O\u001a\u00020\u0016J\"\u0010P\u001a\b\u0012\u0004\u0012\u0002020&H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bQ\u0010RJ\b\u0010S\u001a\u000202H\u0002J\b\u0010T\u001a\u00020\u0016H\u0002J\b\u0010\u0015\u001a\u00020\u0016H\u0016J\u0010\u0010U\u001a\u00020\u000e2\u0006\u0010V\u001a\u00020\u000eH\u0002J\b\u0010W\u001a\u00020\u0016H\u0002J\"\u0010X\u001a\b\u0012\u0004\u0012\u0002020&H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bY\u0010RJ\u0011\u0010Z\u001a\u000202H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010RJ\u0011\u0010[\u001a\u000202H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010RJ\u0018\u0010\\\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u000e2\u0006\u0010]\u001a\u00020\u0012H\u0002J\u0010\u0010^\u001a\u00020B2\u0006\u0010(\u001a\u00020$H\u0002J\u0010\u0010_\u001a\u00020B2\u0006\u0010(\u001a\u00020$H\u0002J\b\u0010`\u001a\u000202H\u0002J\u0011\u0010a\u001a\u000202H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010RJ\"\u0010b\u001a\b\u0012\u0004\u0012\u0002020&H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bc\u0010RJ\u0011\u0010d\u001a\u000202H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010RJ\u0011\u0010e\u001a\u000202H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010RJ\u001a\u0010f\u001a\u00020\u000e2\u0006\u0010g\u001a\u00020\u000e2\b\b\u0002\u0010h\u001a\u00020\u0012H\u0002J8\u0010i\u001a\b\u0012\u0004\u0012\u0002020&2\u0006\u00103\u001a\u00020\u00192\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000e0/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bj\u0010kJ@\u0010i\u001a\b\u0012\u0004\u0012\u0002020&2\u0006\u00103\u001a\u00020\u00192\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000e0/2\u0006\u0010-\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bl\u0010mJ\u001e\u0010n\u001a\u0002022\u0006\u00103\u001a\u00020\u00192\f\u0010o\u001a\b\u0012\u0004\u0012\u00020\u000e0/H\u0002R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u0013R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00190\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006q"}, d2 = {"Lcom/fsl/app/data/inference/PyTorchInferenceEngine;", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "context", "Landroid/content/Context;", "imageProcessor", "Lcom/fsl/app/data/utils/ImageProcessor;", "assetUtils", "Lcom/fsl/app/data/utils/AssetUtils;", "nativeEngine", "Lcom/fsl/app/data/inference/NativeInferenceEngine;", "(Landroid/content/Context;Lcom/fsl/app/data/utils/ImageProcessor;Lcom/fsl/app/data/utils/AssetUtils;Lcom/fsl/app/data/inference/NativeInferenceEngine;)V", "backbone", "Lorg/pytorch/Module;", "featureCentering", "", "featureDimension", "", "featureNormalization", "", "Ljava/lang/Float;", "imageSize", "isInitialized", "", "learnedClassNames", "", "", "prototypes", "supportFeatures", "supportLabels", "", "useSoftmax", "adjustFeatureDimension", "features", "targetDim", "bitmapToFloatArray", "bitmap", "Landroid/graphics/Bitmap;", "classify", "Lkotlin/Result;", "Lcom/fsl/app/domain/model/ClassificationResult;", "image", "classify-gIAlu-s", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "computeFeatures", "computeFeaturesOptimized", "useGpu", "computeMeanFeature", "", "computePrototypeGpu", "deleteClass", "", "className", "deleteClass-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "euclideanDistance", "features1", "features2", "extractDefaultFeatures", "extractFeatures", "images", "extractFeatures-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractFeatures-0E7RQCE", "(Ljava/util/List;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractFeaturesWithBackbone", "inputTensor", "Lorg/pytorch/Tensor;", "extractFeaturesWithBackboneGpu", "extractFeaturesWithGpu", "extractFeaturesWithNative", "flattenFeatures", "getLearnedClassNames", "getModelInfo", "", "", "getPrototype", "classIndex", "getSupportFeature", "index", "hasLearnedClasses", "initialize", "initialize-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initializeFeatureProcessing", "isGpuAvailable", "l2DistanceToPrototypes", "queryFeatures", "loadBackboneModel", "loadModel", "loadModel-IoAF18A", "loadSavedPrototypes", "loadSavedSupportSet", "normalizeFeatures", "p", "preprocessImage", "preprocessImageForGpu", "recomputePrototypes", "recomputePrototypesWithGpu", "saveModel", "saveModel-IoAF18A", "savePrototypes", "saveSupportSet", "softmax", "scores", "temperature", "updateModel", "updateModel-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateModel-BWLJW6A", "(Ljava/lang/String;Ljava/util/List;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSupportSet", "newFeatures", "Companion", "app_debug"})
@javax.inject.Singleton
public final class PyTorchInferenceEngine implements com.fsl.app.domain.repository.IInferenceRepository {
    private final android.content.Context context = null;
    private final com.fsl.app.data.utils.ImageProcessor imageProcessor = null;
    private final com.fsl.app.data.utils.AssetUtils assetUtils = null;
    private final com.fsl.app.data.inference.NativeInferenceEngine nativeEngine = null;
    private org.pytorch.Module backbone;
    private boolean isInitialized = false;
    private boolean useSoftmax = false;
    private float[] prototypes;
    private float[] supportFeatures;
    private int[] supportLabels;
    private java.util.List<java.lang.String> learnedClassNames;
    private float[] featureCentering;
    private java.lang.Float featureNormalization;
    private int featureDimension = 640;
    private int imageSize = 84;
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.inference.PyTorchInferenceEngine.Companion Companion = null;
    private static final java.lang.String TAG = "PyTorchInferenceEngine";
    private static final java.lang.String BACKBONE_MODEL_FILE = "resnet12_backbone.ptl";
    private static final java.lang.String PROTOTYPES_FILE_NAME = "prototypes.json";
    private static final java.lang.String SUPPORT_DATA_FILE_NAME = "support_data.json";
    private static final int FEATURE_DIM = 640;
    private static final int IMAGE_SIZE = 84;
    private static final float[] MEAN = {0.485F, 0.456F, 0.406F};
    private static final float[] STD = {0.229F, 0.224F, 0.225F};
    
    @javax.inject.Inject
    public PyTorchInferenceEngine(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.utils.ImageProcessor imageProcessor, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.utils.AssetUtils assetUtils, @org.jetbrains.annotations.Nullable
    com.fsl.app.data.inference.NativeInferenceEngine nativeEngine) {
        super();
    }
    
    /**
     * 加载ResNet12 backbone模型
     * 基于easyfsl的ResNet12实现
     */
    private final boolean loadBackboneModel() {
        return false;
    }
    
    /**
     * 初始化特征处理参数
     * 基于easyfsl的特征中心化和归一化
     */
    private final void initializeFeatureProcessing() {
    }
    
    /**
     * 计算图像特征
     * 基于easyfsl.FewShotClassifier.compute_features
     */
    private final float[] computeFeatures(android.graphics.Bitmap image) {
        return null;
    }
    
    @java.lang.Override
    public boolean isInitialized() {
        return false;
    }
    
    /**
     * 检查是否有已学习的类别
     */
    public final boolean hasLearnedClasses() {
        return false;
    }
    
    /**
     * 获取已学习的类别名称
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getLearnedClassNames() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.util.Map<java.lang.String, java.lang.Object> getModelInfo() {
        return null;
    }
    
    /**
     * 更新support set数据
     * 基于easyfsl.FewShotClassifier.compute_prototypes_and_store_support_set
     */
    private final void updateSupportSet(java.lang.String className, java.util.List<float[]> newFeatures) {
    }
    
    /**
     * 重新计算所有原型
     * 基于easyfsl.methods.utils.compute_prototypes
     */
    private final void recomputePrototypes() {
    }
    
    /**
     * 计算L2距离到原型
     * 基于easyfsl.FewShotClassifier.l2_distance_to_prototypes
     * 返回负的欧几里得距离作为分数 (距离越小，分数越高)
     */
    private final float[] l2DistanceToPrototypes(float[] queryFeatures) {
        return null;
    }
    
    /**
     * 计算欧几里得距离
     */
    private final float euclideanDistance(float[] features1, float[] features2) {
        return 0.0F;
    }
    
    /**
     * 应用softmax函数
     * 基于easyfsl.FewShotClassifier.softmax_if_specified
     */
    private final float[] softmax(float[] scores, float temperature) {
        return null;
    }
    
    /**
     * 特征归一化
     * 基于torch.nn.functional.normalize
     */
    private final float[] normalizeFeatures(float[] features, float p) {
        return null;
    }
    
    /**
     * 计算特征均值
     */
    private final float[] computeMeanFeature(java.util.List<float[]> features) {
        return null;
    }
    
    /**
     * 将特征列表展平为一维数组
     */
    private final float[] flattenFeatures(java.util.List<float[]> features) {
        return null;
    }
    
    /**
     * 获取指定索引的support特征
     */
    private final float[] getSupportFeature(int index) {
        return null;
    }
    
    /**
     * 获取指定类别的原型
     */
    private final float[] getPrototype(int classIndex) {
        return null;
    }
    
    /**
     * 预处理图像
     * 基于few-shot learning的标准预处理流程
     */
    private final org.pytorch.Tensor preprocessImage(android.graphics.Bitmap image) {
        return null;
    }
    
    /**
     * 使用backbone提取特征
     */
    private final float[] extractFeaturesWithBackbone(org.pytorch.Tensor inputTensor) {
        return null;
    }
    
    /**
     * 默认特征提取方法
     * 基于图像的基本统计特征
     */
    private final float[] extractDefaultFeatures(android.graphics.Bitmap image) {
        return null;
    }
    
    /**
     * 调整特征维度
     */
    private final float[] adjustFeatureDimension(float[] features, int targetDim) {
        return null;
    }
    
    /**
     * 使用Native引擎提取特征
     */
    private final float[] extractFeaturesWithNative(android.graphics.Bitmap image) {
        return null;
    }
    
    /**
     * 将Bitmap转换为FloatArray (用于Native引擎)
     */
    private final float[] bitmapToFloatArray(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 保存原型数据
     * 基于easyfsl的原型存储格式
     */
    private final java.lang.Object savePrototypes(kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 加载已保存的原型数据
     */
    private final java.lang.Object loadSavedPrototypes(kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 保存support set数据
     */
    private final java.lang.Object saveSupportSet(kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 加载已保存的support set数据
     */
    private final java.lang.Object loadSavedSupportSet(kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 检查GPU是否可用
     */
    private final boolean isGpuAvailable() {
        return false;
    }
    
    /**
     * GPU加速特征提取
     * 使用批处理和并行计算优化
     */
    private final java.lang.Object extractFeaturesWithGpu(java.util.List<android.graphics.Bitmap> images, kotlin.coroutines.Continuation<? super java.util.List<float[]>> continuation) {
        return null;
    }
    
    /**
     * 优化的特征计算（支持GPU加速）
     */
    private final float[] computeFeaturesOptimized(android.graphics.Bitmap image, boolean useGpu) {
        return null;
    }
    
    /**
     * GPU优化的图像预处理
     */
    private final org.pytorch.Tensor preprocessImageForGpu(android.graphics.Bitmap image) {
        return null;
    }
    
    /**
     * GPU加速的backbone特征提取
     */
    private final float[] extractFeaturesWithBackboneGpu(org.pytorch.Tensor inputTensor) {
        return null;
    }
    
    /**
     * GPU加速的原型重计算
     */
    private final java.lang.Object recomputePrototypesWithGpu(kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * GPU加速的原型计算
     */
    private final float[] computePrototypeGpu(java.util.List<float[]> features) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/fsl/app/data/inference/PyTorchInferenceEngine$Companion;", "", "()V", "BACKBONE_MODEL_FILE", "", "FEATURE_DIM", "", "IMAGE_SIZE", "MEAN", "", "PROTOTYPES_FILE_NAME", "STD", "SUPPORT_DATA_FILE_NAME", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}