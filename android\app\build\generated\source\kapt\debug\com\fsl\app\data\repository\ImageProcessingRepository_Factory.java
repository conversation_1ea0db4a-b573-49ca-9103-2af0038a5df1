// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.repository;

import com.fsl.app.data.utils.ImageProcessor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class ImageProcessingRepository_Factory implements Factory<ImageProcessingRepository> {
  private final Provider<ImageProcessor> imageProcessorProvider;

  public ImageProcessingRepository_Factory(Provider<ImageProcessor> imageProcessorProvider) {
    this.imageProcessorProvider = imageProcessorProvider;
  }

  @Override
  public ImageProcessingRepository get() {
    return newInstance(imageProcessorProvider.get());
  }

  public static ImageProcessingRepository_Factory create(
      Provider<ImageProcessor> imageProcessorProvider) {
    return new ImageProcessingRepository_Factory(imageProcessorProvider);
  }

  public static ImageProcessingRepository newInstance(ImageProcessor imageProcessor) {
    return new ImageProcessingRepository(imageProcessor);
  }
}
