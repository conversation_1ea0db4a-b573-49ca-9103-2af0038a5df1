// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.gallery;

import com.fsl.app.domain.repository.GalleryRepository;
import com.fsl.app.domain.repository.IInferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class GalleryViewModel_Factory implements Factory<GalleryViewModel> {
  private final Provider<GalleryRepository> galleryRepositoryProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  public GalleryViewModel_Factory(Provider<GalleryRepository> galleryRepositoryProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider) {
    this.galleryRepositoryProvider = galleryRepositoryProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
  }

  @Override
  public GalleryViewModel get() {
    return newInstance(galleryRepositoryProvider.get(), inferenceRepositoryProvider.get());
  }

  public static GalleryViewModel_Factory create(
      Provider<GalleryRepository> galleryRepositoryProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider) {
    return new GalleryViewModel_Factory(galleryRepositoryProvider, inferenceRepositoryProvider);
  }

  public static GalleryViewModel newInstance(GalleryRepository galleryRepository,
      IInferenceRepository inferenceRepository) {
    return new GalleryViewModel(galleryRepository, inferenceRepository);
  }
}
