kotlin.Enumandroid.os.Parcelable+com.fsl.app.domain.usecase.ValidationResult+com.fsl.app.presentation.camera.CameraState#androidx.lifecycle.AndroidViewModelandroidx.lifecycle.ViewModelandroid.app.Application#androidx.activity.ComponentActivityandroidx.room.RoomDatabase2com.fsl.app.domain.repository.IInferenceRepository/com.fsl.app.domain.repository.GalleryRepository8com.fsl.app.domain.repository.IImageProcessingRepository.com.fsl.app.domain.repository.IModelRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   