package com.fsl.app.data.inference

import kotlin.math.*

/**
 * 修复置信度计算的工具类
 * 解决图库置信度为负值的问题
 */
object FixedConfidenceCalculator {
    
    /**
     * 修复L2距离到置信度的转换
     * 确保置信度在0-1之间
     */
    fun l2DistanceToConfidenceScores(queryFeatures: FloatArray, prototypes: FloatArray, nWay: Int, featureDim: Int): FloatArray {
        if (prototypes.isEmpty() || nWay == 0) {
            return FloatArray(0)
        }

        val distances = FloatArray(nWay)
        
        // 1. 计算所有距离
        for (i in 0 until nWay) {
            val prototype = FloatArray(featureDim)
            System.arraycopy(prototypes, i * featureDim, prototype, 0, featureDim)
            distances[i] = euclideanDistance(queryFeatures, prototype)
        }
        
        // 2. 转换距离为置信度分数
        return convertDistancesToConfidence(distances)
    }
    
    /**
     * 将距离转换为置信度分数
     * 方法1: 使用softmax归一化
     */
    fun convertDistancesToConfidence(distances: FloatArray): FloatArray {
        val scores = FloatArray(distances.size)
        
        // 使用负距离的指数函数 (距离越小，分数越高)
        val maxDistance = distances.maxOrNull() ?: 1.0f
        val temperature = 1.0f // softmax温度参数
        
        var sumExp = 0.0f
        for (i in distances.indices) {
            // 归一化距离到0-1范围，然后取负值
            val normalizedNegDistance = -(distances[i] / maxDistance)
            scores[i] = exp(normalizedNegDistance / temperature)
            sumExp += scores[i]
        }
        
        // softmax归一化
        for (i in scores.indices) {
            scores[i] = scores[i] / sumExp
        }
        
        return scores
    }
    
    /**
     * 将距离转换为置信度分数
     * 方法2: 简单的线性转换
     */
    fun convertDistancesToConfidenceLinear(distances: FloatArray): FloatArray {
        val scores = FloatArray(distances.size)
        
        if (distances.isEmpty()) return scores
        
        val maxDistance = distances.maxOrNull() ?: 1.0f
        val minDistance = distances.minOrNull() ?: 0.0f
        val range = maxDistance - minDistance
        
        for (i in distances.indices) {
            if (range > 0) {
                // 距离越小，置信度越高
                scores[i] = 1.0f - ((distances[i] - minDistance) / range)
            } else {
                // 所有距离相同，平均分配置信度
                scores[i] = 1.0f / distances.size
            }
            
            // 确保置信度在合理范围内
            scores[i] = max(0.01f, min(0.99f, scores[i]))
        }
        
        return scores
    }
    
    /**
     * 计算欧几里得距离
     */
    private fun euclideanDistance(a: FloatArray, b: FloatArray): Float {
        if (a.size != b.size) {
            throw IllegalArgumentException("特征向量维度不匹配: ${a.size} vs ${b.size}")
        }
        
        var sum = 0.0f
        for (i in a.indices) {
            val diff = a[i] - b[i]
            sum += diff * diff
        }
        return sqrt(sum)
    }
    
    /**
     * 验证置信度分数的有效性
     */
    fun validateConfidenceScores(scores: FloatArray): Boolean {
        for (score in scores) {
            if (score < 0.0f || score > 1.0f || score.isNaN() || score.isInfinite()) {
                return false
            }
        }
        return true
    }
    
    /**
     * 修复无效的置信度分数
     */
    fun fixInvalidConfidenceScores(scores: FloatArray): FloatArray {
        val fixedScores = FloatArray(scores.size)
        
        for (i in scores.indices) {
            when {
                scores[i].isNaN() || scores[i].isInfinite() -> {
                    fixedScores[i] = 1.0f / scores.size // 平均分配
                }
                scores[i] < 0.0f -> {
                    fixedScores[i] = 0.01f // 最小置信度
                }
                scores[i] > 1.0f -> {
                    fixedScores[i] = 0.99f // 最大置信度
                }
                else -> {
                    fixedScores[i] = scores[i]
                }
            }
        }
        
        return fixedScores
    }
}
