{"logs": [{"outputFile": "com.fsl.app-mergeDebugResources-69:/values/values.xml", "map": [{"source": "F:\\geek\\fsl\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,77,78,79,90,91,94", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1962,4849,4896,4943,5687,5732,5898", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1999,4891,4938,4985,5727,5772,5935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c6f0531ba1c37aa655fc6903d88ebaf2\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "259,263", "startColumns": "4,4", "startOffsets": "16725,16902", "endColumns": "53,66", "endOffsets": "16774,16964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7ce7a25d9c85aaf80d651fca6342aa7f\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2019,2035,2041,3012,3028", "startColumns": "4,4,4,4,4", "startOffsets": "130018,130443,130621,163109,163520", "endLines": "2034,2040,2050,3027,3031", "endColumns": "24,24,24,24,24", "endOffsets": "130438,130616,130900,163515,163642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bac1b3e49ce81dbd1ba2ff5e66773\\transformed\\fragment-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "254,267,290,2657,2662", "startColumns": "4,4,4,4,4", "startOffsets": "16470,17107,18316,151657,151827", "endLines": "254,267,290,2661,2665", "endColumns": "56,64,63,24,24", "endOffsets": "16522,17167,18375,151822,151971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\27f4573bcf6555a93319f1243cc87aa6\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "20700", "endColumns": "82", "endOffsets": "20778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f0a6ff10e7855dd2623816e2b527841\\transformed\\jetified-camera-view-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,2955", "startColumns": "4,4,4", "startOffsets": "250,511,161356", "endLines": "7,17,2958", "endColumns": "11,11,24", "endOffsets": "397,813,161494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\612fd5b5064e8d8de31b256d9d37ffdf\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "288", "startColumns": "4", "startOffsets": "18212", "endColumns": "53", "endOffsets": "18261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ae8178220089305ce36aae88e52db865\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "289", "startColumns": "4", "startOffsets": "18266", "endColumns": "49", "endOffsets": "18311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5037e9d4ddaa957f7ac422280b19ed1f\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,43,44,67,68,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,212,213,214,260,261,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,296,328,329,330,331,332,333,334,375,1736,1737,1741,1742,1746,1896,1897,2541,2547,2603,2636,2666,2699", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2645,2710,4129,4198,11252,11322,11390,11462,11532,11593,11667,12524,12585,12646,12708,12772,12834,12895,12963,13063,13123,13189,13262,13331,13388,13440,13955,14027,14103,16779,16814,17218,17273,17336,17391,17449,17507,17568,17631,17688,17739,17789,17850,17907,17973,18007,18042,18676,20824,20891,20963,21032,21101,21175,21247,23346,113261,113378,113579,113689,113890,125535,125607,146812,147015,149245,150976,151976,152658", "endLines": "9,28,29,43,44,67,68,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,212,213,214,260,261,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,296,328,329,330,331,332,333,334,375,1736,1740,1741,1745,1746,1896,1897,2546,2556,2635,2656,2698,2704", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1550,1638,2705,2771,4193,4256,11317,11385,11457,11527,11588,11662,11735,12580,12641,12703,12767,12829,12890,12958,13058,13118,13184,13257,13326,13383,13435,13497,14022,14098,14163,16809,16844,17268,17331,17386,17444,17502,17563,17626,17683,17734,17784,17845,17902,17968,18002,18037,18072,18741,20886,20958,21027,21096,21170,21242,21330,23412,113373,113574,113684,113885,114014,125602,125669,147010,147311,150971,151652,152653,152820"}}, {"source": "F:\\geek\\fsl\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "18,20,1,2,6,19,17,16,23,28,25,3,13,26,22,4,10,9,24,12,11,27,8,14,5,29,7,21,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,888,16,57,214,844,746,703,1034,1262,1124,95,577,1170,984,134,393,348,1080,502,440,1216,300,627,174,1310,253,933,664", "endColumns": "42,44,40,37,38,43,54,42,45,47,45,38,49,45,49,39,46,44,43,74,61,45,47,36,39,38,46,50,38", "endOffsets": "839,928,52,90,248,883,796,741,1075,1305,1165,129,622,1211,1029,169,435,388,1119,572,497,1257,343,659,209,1344,295,979,698"}, "to": {"startLines": "324,325,327,335,336,337,338,342,345,348,350,351,352,353,356,357,358,359,360,365,366,367,370,371,374,376,377,381,382", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20612,20655,20783,21335,21373,21412,21456,21678,21888,22036,22130,22176,22215,22265,22423,22473,22513,22560,22605,22835,22910,22972,23122,23170,23306,23417,23456,23676,23727", "endColumns": "42,44,40,37,38,43,54,42,45,47,45,38,49,45,49,39,46,44,43,74,61,45,47,36,39,38,46,50,38", "endOffsets": "20650,20695,20819,21368,21407,21451,21506,21716,21929,22079,22171,22210,22260,22306,22468,22508,22555,22600,22644,22905,22967,23013,23165,23202,23341,23451,23498,23722,23761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72ccbacfecbbba1cbe69ffeb616187eb\\transformed\\jetified-ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,251,252,253,256,258,291,339,340,343,344,347,354,355,361,362,363,364,368,369,373,378,379,380,1455,1458,1461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14168,14227,14286,14346,14406,14466,14526,14586,14646,14706,14766,14826,14886,14945,15005,15065,15125,15185,15245,15305,15365,15425,15485,15545,15604,15664,15724,15783,15842,15901,15960,16019,16283,16357,16415,16585,16670,18380,21511,21576,21721,21787,21978,22311,22363,22649,22711,22765,22801,23018,23068,23260,23503,23550,23586,91924,92036,92147", "endLines": "215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,251,252,253,256,258,291,339,340,343,344,347,354,355,361,362,363,364,368,369,373,378,379,380,1457,1460,1464", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "14222,14281,14341,14401,14461,14521,14581,14641,14701,14761,14821,14881,14940,15000,15060,15120,15180,15240,15300,15360,15420,15480,15540,15599,15659,15719,15778,15837,15896,15955,16014,16073,16352,16410,16465,16631,16720,18428,21571,21625,21782,21883,22031,22358,22418,22706,22760,22796,22830,23063,23117,23301,23545,23581,23671,92031,92142,92337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\82c63c69ac718150815caccd1171ae66\\transformed\\navigation-runtime-2.5.3\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "262,2002,2934,2937", "startColumns": "4,4,4,4", "startOffsets": "16849,129197,160564,160679", "endLines": "262,2008,2936,2939", "endColumns": "52,24,24,24", "endOffsets": "16897,129496,160674,160789"}}, {"source": "F:\\geek\\fsl\\android\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "9", "endColumns": "12", "endOffsets": "499"}, "to": {"startLines": "1782", "startColumns": "4", "startOffsets": "116603", "endLines": "1788", "endColumns": "12", "endOffsets": "116871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\154a1ba9c71ec06842c95a5522f1809d\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,180,181,182,183,184,185,186,187,188,204,205,206,207,208,209,210,211,247,248,249,250,257,264,265,268,285,292,293,294,295,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,372,383,384,385,386,387,388,396,397,401,405,409,414,420,427,431,435,440,444,448,452,456,460,464,470,474,480,484,490,494,499,503,506,510,516,520,526,530,536,539,543,547,551,555,559,560,561,562,565,568,571,574,578,579,580,581,582,585,587,589,591,596,597,601,607,611,612,614,625,626,630,636,640,641,642,646,673,677,678,682,710,880,906,1077,1103,1134,1142,1148,1162,1184,1189,1194,1204,1213,1222,1226,1233,1241,1248,1249,1258,1261,1264,1268,1272,1276,1279,1280,1285,1290,1300,1305,1312,1318,1319,1322,1326,1331,1333,1335,1338,1341,1343,1347,1350,1357,1360,1363,1367,1369,1373,1375,1377,1379,1383,1391,1399,1411,1417,1426,1429,1440,1443,1444,1449,1450,1465,1534,1604,1605,1615,1624,1625,1627,1631,1634,1637,1640,1643,1646,1649,1652,1656,1659,1662,1665,1669,1672,1676,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1702,1704,1705,1706,1707,1708,1709,1710,1711,1713,1714,1716,1717,1719,1721,1722,1724,1725,1726,1727,1728,1729,1731,1732,1733,1734,1735,1747,1749,1751,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1767,1768,1769,1770,1771,1772,1774,1778,1789,1790,1791,1792,1793,1794,1798,1799,1800,1801,1803,1805,1807,1809,1811,1812,1813,1814,1816,1818,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1834,1835,1836,1837,1839,1841,1842,1844,1845,1847,1849,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1864,1865,1866,1867,1869,1870,1871,1872,1873,1875,1877,1879,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1898,1973,1976,1979,1982,1996,2009,2051,2080,2107,2116,2178,2537,2557,2585,2705,2729,2735,2741,2762,2886,2945,2951,2959,2965,3000,3032,3098,3118,3173,3185,3211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,2004,2081,2159,2265,2371,2450,2530,2587,2776,2850,2925,2990,3056,3116,3177,3249,3322,3389,3457,3516,3575,3634,3693,3752,3806,3860,3913,3967,4021,4075,4261,4335,4414,4487,4561,4632,4704,4776,4990,5047,5105,5178,5252,5326,5401,5473,5546,5616,5777,5837,5940,6009,6078,6148,6222,6298,6362,6439,6515,6592,6657,6726,6803,6878,6947,7015,7092,7158,7219,7316,7381,7450,7549,7620,7679,7737,7794,7853,7917,7988,8060,8132,8204,8276,8343,8411,8479,8538,8601,8665,8755,8846,8906,8972,9039,9105,9175,9239,9292,9359,9420,9487,9600,9658,9721,9786,9851,9926,9999,10071,10120,10181,10242,10303,10365,10429,10493,10557,10622,10685,10745,10806,10872,10931,10991,11053,11124,11184,11740,11826,11913,12003,12090,12178,12260,12343,12433,13502,13554,13612,13657,13723,13787,13844,13901,16078,16135,16183,16232,16636,16969,17016,17172,18077,18433,18497,18559,18619,18746,18820,18890,18968,19022,19092,19177,19225,19271,19332,19395,19461,19525,19596,19659,19724,19788,19849,19910,19962,20035,20109,20178,20253,20327,20401,20542,23207,23766,23844,23934,24022,24118,24208,24790,24879,25126,25407,25659,25944,26337,26814,27036,27258,27534,27761,27991,28221,28451,28681,28908,29327,29553,29978,30208,30636,30855,31138,31346,31477,31704,32130,32355,32782,33003,33428,33548,33824,34125,34449,34740,35054,35191,35322,35427,35669,35836,36040,36248,36519,36631,36743,36848,36965,37179,37325,37465,37551,37899,37987,38233,38651,38900,38982,39080,39672,39772,40024,40448,40703,40797,40886,41123,43147,43389,43491,43744,45900,56432,57948,68579,70107,71864,72490,72910,73971,75236,75492,75728,76275,76769,77374,77572,78152,78716,79091,79209,79747,79904,80100,80373,80629,80799,80940,81004,81369,81736,82412,82676,83014,83367,83461,83647,83953,84215,84340,84467,84706,84917,85036,85229,85406,85861,86042,86164,86423,86536,86723,86825,86932,87061,87336,87844,88340,89217,89511,90081,90230,90962,91134,91218,91554,91646,92342,97588,102977,103039,103617,104201,104292,104405,104634,104794,104946,105117,105283,105452,105619,105782,106025,106195,106368,106539,106813,107012,107217,107547,107631,107727,107823,107921,108021,108123,108225,108327,108429,108531,108631,108727,108839,108968,109091,109222,109353,109451,109565,109659,109799,109933,110029,110141,110241,110357,110453,110565,110665,110805,110941,111105,111235,111393,111543,111684,111828,111963,112075,112225,112353,112481,112617,112749,112879,113009,113121,114019,114165,114309,114447,114513,114603,114679,114783,114873,114975,115083,115191,115291,115371,115463,115561,115671,115749,115855,115947,116051,116161,116283,116446,116876,116956,117056,117146,117256,117346,117587,117681,117787,117879,117979,118091,118205,118321,118437,118531,118645,118757,118859,118979,119101,119183,119287,119407,119533,119631,119725,119813,119925,120041,120163,120275,120450,120566,120652,120744,120856,120980,121047,121173,121241,121369,121513,121641,121710,121805,121920,122033,122132,122241,122352,122463,122564,122669,122769,122899,122990,123113,123207,123319,123405,123509,123605,123693,123811,123915,124019,124145,124233,124341,124441,124531,124641,124725,124827,124911,124965,125029,125135,125221,125331,125415,125674,128290,128408,128523,128603,128964,129501,130905,132249,133610,133998,136773,146677,147316,148673,152825,153576,153838,154038,154417,158695,160976,161205,161499,161714,162797,163647,166673,167417,169548,169888,171199", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,180,181,182,183,184,185,186,187,188,204,205,206,207,208,209,210,211,247,248,249,250,257,264,265,268,285,292,293,294,295,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,372,383,384,385,386,387,395,396,400,404,408,413,419,426,430,434,439,443,447,451,455,459,463,469,473,479,483,489,493,498,502,505,509,515,519,525,529,535,538,542,546,550,554,558,559,560,561,564,567,570,573,577,578,579,580,581,584,586,588,590,595,596,600,606,610,611,613,624,625,629,635,639,640,641,645,672,676,677,681,709,879,905,1076,1102,1133,1141,1147,1161,1183,1188,1193,1203,1212,1221,1225,1232,1240,1247,1248,1257,1260,1263,1267,1271,1275,1278,1279,1284,1289,1299,1304,1311,1317,1318,1321,1325,1330,1332,1334,1337,1340,1342,1346,1349,1356,1359,1362,1366,1368,1372,1374,1376,1378,1382,1390,1398,1410,1416,1425,1428,1439,1442,1443,1448,1449,1454,1533,1603,1604,1614,1623,1624,1626,1630,1633,1636,1639,1642,1645,1648,1651,1655,1658,1661,1664,1668,1671,1675,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1701,1703,1704,1705,1706,1707,1708,1709,1710,1712,1713,1715,1716,1718,1720,1721,1723,1724,1725,1726,1727,1728,1730,1731,1732,1733,1734,1735,1748,1750,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1766,1767,1768,1769,1770,1771,1773,1777,1781,1789,1790,1791,1792,1793,1797,1798,1799,1800,1802,1804,1806,1808,1810,1811,1812,1813,1815,1817,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1833,1834,1835,1836,1838,1840,1841,1843,1844,1846,1848,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1863,1864,1865,1866,1868,1869,1870,1871,1872,1874,1876,1878,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1972,1975,1978,1981,1995,2001,2018,2079,2106,2115,2177,2536,2540,2584,2602,2728,2734,2740,2761,2885,2905,2950,2954,2964,2999,3011,3097,3117,3172,3184,3210,3217", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2076,2154,2260,2366,2445,2525,2582,2640,2845,2920,2985,3051,3111,3172,3244,3317,3384,3452,3511,3570,3629,3688,3747,3801,3855,3908,3962,4016,4070,4124,4330,4409,4482,4556,4627,4699,4771,4844,5042,5100,5173,5247,5321,5396,5468,5541,5611,5682,5832,5893,6004,6073,6143,6217,6293,6357,6434,6510,6587,6652,6721,6798,6873,6942,7010,7087,7153,7214,7311,7376,7445,7544,7615,7674,7732,7789,7848,7912,7983,8055,8127,8199,8271,8338,8406,8474,8533,8596,8660,8750,8841,8901,8967,9034,9100,9170,9234,9287,9354,9415,9482,9595,9653,9716,9781,9846,9921,9994,10066,10115,10176,10237,10298,10360,10424,10488,10552,10617,10680,10740,10801,10867,10926,10986,11048,11119,11179,11247,11821,11908,11998,12085,12173,12255,12338,12428,12519,13549,13607,13652,13718,13782,13839,13896,13950,16130,16178,16227,16278,16665,17011,17060,17213,18104,18492,18554,18614,18671,18815,18885,18963,19017,19087,19172,19220,19266,19327,19390,19456,19520,19591,19654,19719,19783,19844,19905,19957,20030,20104,20173,20248,20322,20396,20537,20607,23255,23839,23929,24017,24113,24203,24785,24874,25121,25402,25654,25939,26332,26809,27031,27253,27529,27756,27986,28216,28446,28676,28903,29322,29548,29973,30203,30631,30850,31133,31341,31472,31699,32125,32350,32777,32998,33423,33543,33819,34120,34444,34735,35049,35186,35317,35422,35664,35831,36035,36243,36514,36626,36738,36843,36960,37174,37320,37460,37546,37894,37982,38228,38646,38895,38977,39075,39667,39767,40019,40443,40698,40792,40881,41118,43142,43384,43486,43739,45895,56427,57943,68574,70102,71859,72485,72905,73966,75231,75487,75723,76270,76764,77369,77567,78147,78711,79086,79204,79742,79899,80095,80368,80624,80794,80935,80999,81364,81731,82407,82671,83009,83362,83456,83642,83948,84210,84335,84462,84701,84912,85031,85224,85401,85856,86037,86159,86418,86531,86718,86820,86927,87056,87331,87839,88335,89212,89506,90076,90225,90957,91129,91213,91549,91641,91919,97583,102972,103034,103612,104196,104287,104400,104629,104789,104941,105112,105278,105447,105614,105777,106020,106190,106363,106534,106808,107007,107212,107542,107626,107722,107818,107916,108016,108118,108220,108322,108424,108526,108626,108722,108834,108963,109086,109217,109348,109446,109560,109654,109794,109928,110024,110136,110236,110352,110448,110560,110660,110800,110936,111100,111230,111388,111538,111679,111823,111958,112070,112220,112348,112476,112612,112744,112874,113004,113116,113256,114160,114304,114442,114508,114598,114674,114778,114868,114970,115078,115186,115286,115366,115458,115556,115666,115744,115850,115942,116046,116156,116278,116441,116598,116951,117051,117141,117251,117341,117582,117676,117782,117874,117974,118086,118200,118316,118432,118526,118640,118752,118854,118974,119096,119178,119282,119402,119528,119626,119720,119808,119920,120036,120158,120270,120445,120561,120647,120739,120851,120975,121042,121168,121236,121364,121508,121636,121705,121800,121915,122028,122127,122236,122347,122458,122559,122664,122764,122894,122985,123108,123202,123314,123400,123504,123600,123688,123806,123910,124014,124140,124228,124336,124436,124526,124636,124720,124822,124906,124960,125024,125130,125216,125326,125410,125530,128285,128403,128518,128598,128959,129192,130013,132244,133605,133993,136768,146672,146807,148668,149240,153571,153833,154033,154412,158690,159296,161200,161351,161709,162792,163104,166668,167412,169543,169883,171194,171397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8c8453ce42bd8e9d5c0d99a12434622c\\transformed\\navigation-common-2.5.3\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "2906,2919,2925,2931,2940", "startColumns": "4,4,4,4,4", "startOffsets": "159301,159940,160184,160431,160794", "endLines": "2918,2924,2930,2933,2944", "endColumns": "24,24,24,24,24", "endOffsets": "159935,160179,160426,160559,160971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fe4df33e34c916b774a0ecdee1cee2a\\transformed\\jetified-material3-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,103,147", "endColumns": "47,43,45", "endOffsets": "98,142,188"}, "to": {"startLines": "341,346,349", "startColumns": "4,4,4", "startOffsets": "21630,21934,22084", "endColumns": "47,43,45", "endOffsets": "21673,21973,22125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\28297456bfed98835a42443329442fd0\\transformed\\jetified-activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "266,287", "startColumns": "4,4", "startOffsets": "17065,18152", "endColumns": "41,59", "endOffsets": "17102,18207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5e27ec58fc9884cfad32f25da8cb0eea\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "286", "startColumns": "4", "startOffsets": "18109", "endColumns": "42", "endOffsets": "18147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38e083de9a839aecd2d2e673a8d1b0ac\\transformed\\jetified-glide-5.0.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "255", "startColumns": "4", "startOffsets": "16527", "endColumns": "57", "endOffsets": "16580"}}]}]}