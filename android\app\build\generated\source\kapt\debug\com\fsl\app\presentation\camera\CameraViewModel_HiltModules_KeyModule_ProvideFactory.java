// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.camera;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.internal.lifecycle.HiltViewModelMap.KeySet")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class CameraViewModel_HiltModules_KeyModule_ProvideFactory implements Factory<String> {
  @Override
  public String get() {
    return provide();
  }

  public static CameraViewModel_HiltModules_KeyModule_ProvideFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static String provide() {
    return Preconditions.checkNotNullFromProvides(CameraViewModel_HiltModules.KeyModule.provide());
  }

  private static final class InstanceHolder {
    private static final CameraViewModel_HiltModules_KeyModule_ProvideFactory INSTANCE = new CameraViewModel_HiltModules_KeyModule_ProvideFactory();
  }
}
