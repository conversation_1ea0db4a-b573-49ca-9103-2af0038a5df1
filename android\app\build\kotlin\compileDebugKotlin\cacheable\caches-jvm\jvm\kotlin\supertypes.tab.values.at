/ Header Record For PersistentHashMapValueStorage android.app.Application$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase3 2com.fsl.app.domain.repository.IInferenceRepository0 /com.fsl.app.domain.repository.GalleryRepository9 8com.fsl.app.domain.repository.IImageProcessingRepository/ .com.fsl.app.domain.repository.IModelRepository android.os.Parcelable kotlin.Enum android.os.Parcelable, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult androidx.lifecycle.ViewModel, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository dagger.internal.Factory