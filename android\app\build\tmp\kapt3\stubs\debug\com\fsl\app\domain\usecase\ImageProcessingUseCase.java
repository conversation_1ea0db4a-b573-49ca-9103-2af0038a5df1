package com.fsl.app.domain.usecase;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\u0007J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\b\u001a\u00020\u0007J\u000e\u0010\u000b\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0007J\u000e\u0010\f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000eJ\u001e\u0010\u000f\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;", "", "imageProcessingRepository", "Lcom/fsl/app/domain/repository/IImageProcessingRepository;", "(Lcom/fsl/app/domain/repository/IImageProcessingRepository;)V", "augmentImage", "", "Landroid/graphics/Bitmap;", "bitmap", "normalizeImage", "", "preprocessImage", "preprocessImageProxy", "imageProxy", "Landroidx/camera/core/ImageProxy;", "resizeImage", "width", "", "height", "app_debug"})
@javax.inject.Singleton
public final class ImageProcessingUseCase {
    private final com.fsl.app.domain.repository.IImageProcessingRepository imageProcessingRepository = null;
    
    @javax.inject.Inject
    public ImageProcessingUseCase(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IImageProcessingRepository imageProcessingRepository) {
        super();
    }
    
    /**
     * 预处理图像
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap preprocessImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 预处理ImageProxy
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap preprocessImageProxy(@org.jetbrains.annotations.NotNull
    androidx.camera.core.ImageProxy imageProxy) {
        return null;
    }
    
    /**
     * 图像增强
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.Bitmap> augmentImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 调整图像大小
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap resizeImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, int width, int height) {
        return null;
    }
    
    /**
     * 图像归一化
     */
    @org.jetbrains.annotations.NotNull
    public final float[] normalizeImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
}