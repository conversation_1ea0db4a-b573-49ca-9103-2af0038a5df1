// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.domain.usecase;

import com.fsl.app.domain.repository.IImageProcessingRepository;
import com.fsl.app.domain.repository.IInferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class ClassificationUseCase_Factory implements Factory<ClassificationUseCase> {
  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  private final Provider<IImageProcessingRepository> imageProcessingRepositoryProvider;

  public ClassificationUseCase_Factory(Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IImageProcessingRepository> imageProcessingRepositoryProvider) {
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
    this.imageProcessingRepositoryProvider = imageProcessingRepositoryProvider;
  }

  @Override
  public ClassificationUseCase get() {
    return newInstance(inferenceRepositoryProvider.get(), imageProcessingRepositoryProvider.get());
  }

  public static ClassificationUseCase_Factory create(
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IImageProcessingRepository> imageProcessingRepositoryProvider) {
    return new ClassificationUseCase_Factory(inferenceRepositoryProvider, imageProcessingRepositoryProvider);
  }

  public static ClassificationUseCase newInstance(IInferenceRepository inferenceRepository,
      IImageProcessingRepository imageProcessingRepository) {
    return new ClassificationUseCase(inferenceRepository, imageProcessingRepository);
  }
}
