/**
 * 原型网络实现
 * 
 * 原型网络算法的C++实现
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "include/prototypical_network.h"
#include <cmath>
#include <algorithm>
#include <numeric>
#include <android/log.h>

#define LOG_TAG "PrototypicalNetwork"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

namespace fsl {

PrototypicalNetwork::PrototypicalNetwork() : featureDim_(0) {}

PrototypicalNetwork::~PrototypicalNetwork() = default;

bool PrototypicalNetwork::initialize(int featureDim) {
    if (featureDim <= 0) {
        LOGE("Invalid feature dimension: %d", featureDim);
        return false;
    }
    
    featureDim_ = featureDim;
    prototypes_.clear();
    
    LOGI("Prototypical network initialized with feature dim: %d", featureDim);
    return true;
}

bool PrototypicalNetwork::addPrototype(const std::string& className, const FeatureVector& prototype) {
    if (!validateFeatureDim(prototype)) {
        LOGE("Invalid feature dimension for class: %s", className.c_str());
        return false;
    }
    
    if (className.empty()) {
        LOGE("Empty class name");
        return false;
    }
    
    // 归一化原型
    FeatureVector normalizedPrototype = normalizeL2(prototype);
    prototypes_[className] = normalizedPrototype;
    
    LOGI("Added prototype for class: %s", className.c_str());
    return true;
}

bool PrototypicalNetwork::updatePrototype(const std::string& className, const FeatureVector& prototype) {
    if (!validateFeatureDim(prototype)) {
        LOGE("Invalid feature dimension for class: %s", className.c_str());
        return false;
    }
    
    auto it = prototypes_.find(className);
    if (it == prototypes_.end()) {
        LOGE("Class not found: %s", className.c_str());
        return false;
    }
    
    // 归一化并更新原型
    FeatureVector normalizedPrototype = normalizeL2(prototype);
    it->second = normalizedPrototype;
    
    LOGI("Updated prototype for class: %s", className.c_str());
    return true;
}

bool PrototypicalNetwork::removePrototype(const std::string& className) {
    auto it = prototypes_.find(className);
    if (it == prototypes_.end()) {
        LOGE("Class not found: %s", className.c_str());
        return false;
    }
    
    prototypes_.erase(it);
    LOGI("Removed prototype for class: %s", className.c_str());
    return true;
}

std::pair<std::string, float> PrototypicalNetwork::classify(const FeatureVector& queryFeature) {
    if (!validateFeatureDim(queryFeature)) {
        LOGE("Invalid query feature dimension");
        return std::make_pair("unknown", 0.0f);
    }
    
    if (prototypes_.empty()) {
        LOGE("No prototypes available");
        return std::make_pair("unknown", 0.0f);
    }
    
    // 归一化查询特征
    FeatureVector normalizedQuery = normalizeL2(queryFeature);
    
    std::string bestClass;
    float bestSimilarity = -1.0f;
    
    // 计算与所有原型的相似度
    for (const auto& pair : prototypes_) {
        float similarity = cosineSimilarity(normalizedQuery, pair.second);
        
        if (similarity > bestSimilarity) {
            bestSimilarity = similarity;
            bestClass = pair.first;
        }
    }
    
    // 将相似度转换为置信度 (0-1范围)
    float confidence = (bestSimilarity + 1.0f) / 2.0f;
    
    return std::make_pair(bestClass, confidence);
}

std::map<std::string, float> PrototypicalNetwork::computeAllScores(const FeatureVector& queryFeature) {
    std::map<std::string, float> scores;
    
    if (!validateFeatureDim(queryFeature)) {
        LOGE("Invalid query feature dimension");
        return scores;
    }
    
    if (prototypes_.empty()) {
        LOGE("No prototypes available");
        return scores;
    }
    
    // 归一化查询特征
    FeatureVector normalizedQuery = normalizeL2(queryFeature);
    
    // 计算所有相似度
    std::vector<float> similarities;
    std::vector<std::string> classNames;
    
    for (const auto& pair : prototypes_) {
        float similarity = cosineSimilarity(normalizedQuery, pair.second);
        similarities.push_back(similarity);
        classNames.push_back(pair.first);
    }
    
    // 应用softmax获得概率分布
    std::vector<float> probabilities = softmax(similarities);
    
    // 构建结果映射
    for (size_t i = 0; i < classNames.size(); ++i) {
        scores[classNames[i]] = probabilities[i];
    }
    
    return scores;
}

FeatureVector PrototypicalNetwork::computePrototype(const FeatureMatrix& supportFeatures) {
    if (supportFeatures.empty()) {
        LOGE("Empty support features");
        return FeatureVector();
    }
    
    if (!validateFeatureDim(supportFeatures[0])) {
        LOGE("Invalid feature dimension in support set");
        return FeatureVector();
    }
    
    int featureDim = supportFeatures[0].size();
    FeatureVector prototype(featureDim, 0.0f);
    
    // 计算均值
    for (const auto& feature : supportFeatures) {
        if (feature.size() != static_cast<size_t>(featureDim)) {
            LOGE("Inconsistent feature dimensions in support set");
            return FeatureVector();
        }
        
        for (int i = 0; i < featureDim; ++i) {
            prototype[i] += feature[i];
        }
    }
    
    float numSamples = static_cast<float>(supportFeatures.size());
    for (int i = 0; i < featureDim; ++i) {
        prototype[i] /= numSamples;
    }
    
    // L2归一化
    return normalizeL2(prototype);
}

const std::map<std::string, FeatureVector>& PrototypicalNetwork::getPrototypes() const {
    return prototypes_;
}

void PrototypicalNetwork::clear() {
    prototypes_.clear();
    LOGI("Cleared all prototypes");
}

float PrototypicalNetwork::euclideanDistance(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return std::numeric_limits<float>::max();
    }
    
    float distance = 0.0f;
    for (size_t i = 0; i < a.size(); ++i) {
        float diff = a[i] - b[i];
        distance += diff * diff;
    }
    
    return std::sqrt(distance);
}

float PrototypicalNetwork::cosineSimilarity(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return 0.0f;
    }
    
    float dotProd = dotProduct(a, b);
    float normA = l2Norm(a);
    float normB = l2Norm(b);
    
    if (normA == 0.0f || normB == 0.0f) {
        return 0.0f;
    }
    
    return dotProd / (normA * normB);
}

FeatureVector PrototypicalNetwork::normalizeL2(const FeatureVector& vector) {
    float norm = l2Norm(vector);
    
    if (norm == 0.0f) {
        return vector;
    }
    
    FeatureVector normalized(vector.size());
    for (size_t i = 0; i < vector.size(); ++i) {
        normalized[i] = vector[i] / norm;
    }
    
    return normalized;
}

float PrototypicalNetwork::l2Norm(const FeatureVector& vector) {
    float norm = 0.0f;
    for (float val : vector) {
        norm += val * val;
    }
    return std::sqrt(norm);
}

float PrototypicalNetwork::dotProduct(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return 0.0f;
    }
    
    float product = 0.0f;
    for (size_t i = 0; i < a.size(); ++i) {
        product += a[i] * b[i];
    }
    
    return product;
}

std::vector<float> PrototypicalNetwork::softmax(const std::vector<float>& scores) {
    if (scores.empty()) {
        return std::vector<float>();
    }
    
    // 找到最大值以提高数值稳定性
    float maxScore = *std::max_element(scores.begin(), scores.end());
    
    // 计算指数
    std::vector<float> expScores(scores.size());
    float sumExp = 0.0f;
    
    for (size_t i = 0; i < scores.size(); ++i) {
        expScores[i] = std::exp(scores[i] - maxScore);
        sumExp += expScores[i];
    }
    
    // 归一化
    std::vector<float> probabilities(scores.size());
    for (size_t i = 0; i < scores.size(); ++i) {
        probabilities[i] = expScores[i] / sumExp;
    }
    
    return probabilities;
}

bool PrototypicalNetwork::validateFeatureDim(const FeatureVector& feature) {
    return featureDim_ > 0 && static_cast<int>(feature.size()) == featureDim_;
}

} // namespace fsl
