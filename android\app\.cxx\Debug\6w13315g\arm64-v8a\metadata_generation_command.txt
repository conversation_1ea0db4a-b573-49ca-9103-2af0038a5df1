                    -HF:\geek\fsl\android\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014
-DCMAKE_TOOLCHAIN_FILE=D:\androidsdk\ndk\27.0.11718014\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\androidsdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BF:\geek\fsl\android\app\.cxx\Debug\6w13315g\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
-DANDROID_PLATFORM=android-29
-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014
                    Build command args: []
                    Version: 2