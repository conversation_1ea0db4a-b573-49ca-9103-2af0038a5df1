/**
 * 图像处理仓库实现
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.repository

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.camera.core.ImageProxy
import com.fsl.app.data.utils.ImageProcessor
import com.fsl.app.domain.repository.IImageProcessingRepository
import java.nio.ByteBuffer
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ImageProcessingRepository @Inject constructor(
    private val imageProcessor: ImageProcessor
) : IImageProcessingRepository {

    override fun preprocessImage(bitmap: Bitmap): Bitmap {
        return imageProcessor.preprocessImage(bitmap)
    }

    override fun preprocessImageProxy(imageProxy: ImageProxy): Bitmap {
        try {
            // 将ImageProxy转换为Bitmap
            val buffer: ByteBuffer = imageProxy.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)

            val bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                ?: throw IllegalArgumentException("无法解码图像")

            return preprocessImage(bitmap)
        } catch (e: Exception) {
            // 如果转换失败，创建一个默认的测试图像
            return createTestBitmap()
        }
    }

    override fun augmentImage(bitmap: Bitmap): List<Bitmap> {
        return imageProcessor.augmentImage(bitmap)
    }

    override fun resizeImage(bitmap: Bitmap, width: Int, height: Int): Bitmap {
        return Bitmap.createScaledBitmap(bitmap, width, height, true)
    }

    override fun normalizeImage(bitmap: Bitmap): FloatArray {
        return imageProcessor.bitmapToTensor(bitmap)
    }

    /**
     * 创建测试用的Bitmap
     */
    private fun createTestBitmap(): Bitmap {
        return Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
            // 填充随机颜色
            val colors = IntArray(224 * 224) {
                android.graphics.Color.rgb(
                    kotlin.random.Random.nextInt(256),
                    kotlin.random.Random.nextInt(256),
                    kotlin.random.Random.nextInt(256)
                )
            }
            setPixels(colors, 0, 224, 0, 0, 224, 224)
        }
    }
}
