package com.fsl.app.presentation;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\f\u001a\u00020\rJ\b\u0010\u000e\u001a\u00020\rH\u0002J\u0006\u0010\u000f\u001a\u00020\rJ\u000e\u0010\u0010\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u0012R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0013"}, d2 = {"Lcom/fsl/app/presentation/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "modelManagementUseCase", "Lcom/fsl/app/domain/usecase/ModelManagementUseCase;", "(Lcom/fsl/app/domain/usecase/ModelManagementUseCase;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/MainUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "initializeApp", "retryInitialization", "switchTab", "tab", "", "app_debug"})
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    private final com.fsl.app.domain.usecase.ModelManagementUseCase modelManagementUseCase = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.MainUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.MainUiState> uiState = null;
    
    @javax.inject.Inject
    public MainViewModel(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ModelManagementUseCase modelManagementUseCase) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.MainUiState> getUiState() {
        return null;
    }
    
    /**
     * 初始化应用
     */
    private final void initializeApp() {
    }
    
    /**
     * 切换标签页
     */
    public final void switchTab(@org.jetbrains.annotations.NotNull
    java.lang.String tab) {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 重新初始化应用
     */
    public final void retryInitialization() {
    }
}