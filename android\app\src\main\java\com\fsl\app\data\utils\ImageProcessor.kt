/**
 * 图像处理工具类
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.utils

import android.graphics.Bitmap
import android.graphics.Matrix
import androidx.camera.core.ImageProxy
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ImageProcessor @Inject constructor() {
    
    companion object {
        private const val INPUT_SIZE = 224
        private val IMAGENET_MEAN = floatArrayOf(0.485f, 0.456f, 0.406f)
        private val IMAGENET_STD = floatArrayOf(0.229f, 0.224f, 0.225f)
    }
    
    /**
     * 将Bitmap转换为模型输入张量
     */
    fun bitmapToTensor(bitmap: Bitmap): FloatArray {
        // 调整大小到模型输入尺寸
        val resizedBitmap = Bitmap.createScaledBitmap(bitmap, INPUT_SIZE, INPUT_SIZE, true)
        
        // 转换为浮点数组
        val pixels = IntArray(INPUT_SIZE * INPUT_SIZE)
        resizedBitmap.getPixels(pixels, 0, INPUT_SIZE, 0, 0, INPUT_SIZE, INPUT_SIZE)
        
        val floatArray = FloatArray(3 * INPUT_SIZE * INPUT_SIZE)
        
        // 归一化并重排列为CHW格式
        for (i in pixels.indices) {
            val pixel = pixels[i]
            val r = ((pixel shr 16) and 0xFF) / 255.0f
            val g = ((pixel shr 8) and 0xFF) / 255.0f
            val b = (pixel and 0xFF) / 255.0f
            
            // ImageNet归一化
            floatArray[i] = (r - IMAGENET_MEAN[0]) / IMAGENET_STD[0]
            floatArray[INPUT_SIZE * INPUT_SIZE + i] = (g - IMAGENET_MEAN[1]) / IMAGENET_STD[1]
            floatArray[2 * INPUT_SIZE * INPUT_SIZE + i] = (b - IMAGENET_MEAN[2]) / IMAGENET_STD[2]
        }
        
        return floatArray
    }
    
    /**
     * 预处理图像
     */
    fun preprocessImage(bitmap: Bitmap): Bitmap {
        return Bitmap.createScaledBitmap(bitmap, INPUT_SIZE, INPUT_SIZE, true)
    }
    
    /**
     * 数据增强
     */
    fun augmentImage(bitmap: Bitmap): List<Bitmap> {
        val augmentedImages = mutableListOf<Bitmap>()
        
        // 原图
        augmentedImages.add(bitmap)
        
        // 水平翻转
        val matrix = Matrix()
        matrix.preScale(-1f, 1f)
        augmentedImages.add(
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, false)
        )
        
        // 旋转
        for (angle in listOf(90f, 180f, 270f)) {
            val rotateMatrix = Matrix()
            rotateMatrix.postRotate(angle)
            augmentedImages.add(
                Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, rotateMatrix, false)
            )
        }
        
        return augmentedImages
    }
}
