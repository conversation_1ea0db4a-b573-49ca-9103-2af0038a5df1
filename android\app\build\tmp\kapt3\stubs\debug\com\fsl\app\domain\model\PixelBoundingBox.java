package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 像素坐标边界框
 */
@kotlinx.parcelize.Parcelize
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\t\u0010\u001c\u001a\u00020\u0003H\u00d6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u00d6\u0003J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001J\u0006\u0010\"\u001a\u00020\u001eJ\u0018\u0010#\u001a\u0004\u0018\u00010$2\u0006\u0010%\u001a\u00020\u00032\u0006\u0010&\u001a\u00020\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\u0019\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0011\u0010\f\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\r\u0010\nR\u0011\u0010\u000e\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\u000f\u0010\nR\u0011\u0010\u0010\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\u0011\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\nR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\nR\u0011\u0010\u0015\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\u0016\u0010\n\u00a8\u0006."}, d2 = {"Lcom/fsl/app/domain/model/PixelBoundingBox;", "Landroid/os/Parcelable;", "left", "", "top", "right", "bottom", "(IIII)V", "area", "getArea", "()I", "getBottom", "centerX", "getCenterX", "centerY", "getCenterY", "height", "getHeight", "getLeft", "getRight", "getTop", "width", "getWidth", "component1", "component2", "component3", "component4", "copy", "describeContents", "equals", "", "other", "", "hashCode", "isValid", "toNormalizedCoordinates", "Lcom/fsl/app/domain/model/TrackingResult;", "imageWidth", "imageHeight", "toString", "", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
public final class PixelBoundingBox implements android.os.Parcelable {
    private final int left = 0;
    private final int top = 0;
    private final int right = 0;
    private final int bottom = 0;
    public static final android.os.Parcelable.Creator<com.fsl.app.domain.model.PixelBoundingBox> CREATOR = null;
    
    /**
     * 像素坐标边界框
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.PixelBoundingBox copy(int left, int top, int right, int bottom) {
        return null;
    }
    
    /**
     * 像素坐标边界框
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 像素坐标边界框
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 像素坐标边界框
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public PixelBoundingBox(int left, int top, int right, int bottom) {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int getLeft() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int getTop() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int getRight() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int getBottom() {
        return 0;
    }
    
    public final int getWidth() {
        return 0;
    }
    
    public final int getHeight() {
        return 0;
    }
    
    public final int getCenterX() {
        return 0;
    }
    
    public final int getCenterY() {
        return 0;
    }
    
    public final int getArea() {
        return 0;
    }
    
    /**
     * 检查边界框是否有效
     */
    public final boolean isValid() {
        return false;
    }
    
    /**
     * 转换为归一化坐标
     */
    @org.jetbrains.annotations.Nullable
    public final com.fsl.app.domain.model.TrackingResult toNormalizedCoordinates(int imageWidth, int imageHeight) {
        return null;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 3)
    public static final class Creator implements android.os.Parcelable.Creator<com.fsl.app.domain.model.PixelBoundingBox> {
        
        public Creator() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.PixelBoundingBox createFromParcel(@org.jetbrains.annotations.NotNull
        android.os.Parcel in) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.PixelBoundingBox[] newArray(int size) {
            return null;
        }
    }
}