/**
 * 图像处理器头文件
 * 
 * 图像预处理和后处理的C++接口
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef IMAGE_PROCESSOR_H
#define IMAGE_PROCESSOR_H

#include <vector>
#include <random>

namespace fsl {

/**
 * 图像增强类型
 */
enum class AugmentationType {
    NONE = 0,
    HORIZONTAL_FLIP,
    ROTATION,
    BRIGHTNESS,
    CONTRAST,
    NOISE
};

/**
 * 图像处理器类
 * 
 * 提供图像预处理、增强和后处理功能
 */
class ImageProcessor {
public:
    ImageProcessor();
    ~ImageProcessor();
    
    /**
     * 预处理图像
     * @param imageData 输入图像数据 (RGB)
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @return 预处理后的图像数据
     */
    std::vector<float> preprocessImage(const float* imageData, int width, int height, int channels = 3);
    
    /**
     * 调整图像大小
     * @param imageData 输入图像数据
     * @param srcWidth 源图像宽度
     * @param srcHeight 源图像高度
     * @param dstWidth 目标图像宽度
     * @param dstHeight 目标图像高度
     * @param channels 图像通道数
     * @return 调整大小后的图像数据
     */
    std::vector<float> resizeImage(const float* imageData, int srcWidth, int srcHeight,
                                  int dstWidth, int dstHeight, int channels = 3);
    
    /**
     * 图像增强
     * @param imageData 输入图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @param type 增强类型
     * @return 增强后的图像数据
     */
    std::vector<float> augmentImage(const float* imageData, int width, int height, int channels,
                                   AugmentationType type);
    
    /**
     * 水平翻转
     * @param imageData 输入图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @return 翻转后的图像数据
     */
    std::vector<float> horizontalFlip(const float* imageData, int width, int height, int channels);
    
    /**
     * 旋转图像
     * @param imageData 输入图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @param angle 旋转角度（度）
     * @return 旋转后的图像数据
     */
    std::vector<float> rotate(const float* imageData, int width, int height, int channels, float angle);
    
    /**
     * 调整亮度
     * @param imageData 输入图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @param factor 亮度调整因子
     * @return 调整后的图像数据
     */
    std::vector<float> adjustBrightness(const float* imageData, int width, int height, int channels, float factor);
    
    /**
     * 调整对比度
     * @param imageData 输入图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @param factor 对比度调整因子
     * @return 调整后的图像数据
     */
    std::vector<float> adjustContrast(const float* imageData, int width, int height, int channels, float factor);
    
    /**
     * 添加噪声
     * @param imageData 输入图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @param intensity 噪声强度
     * @return 添加噪声后的图像数据
     */
    std::vector<float> addNoise(const float* imageData, int width, int height, int channels, float intensity);
    
    /**
     * 中心裁剪
     * @param imageData 输入图像数据
     * @param srcWidth 源图像宽度
     * @param srcHeight 源图像高度
     * @param cropWidth 裁剪宽度
     * @param cropHeight 裁剪高度
     * @param channels 图像通道数
     * @return 裁剪后的图像数据
     */
    std::vector<float> centerCrop(const float* imageData, int srcWidth, int srcHeight,
                                 int cropWidth, int cropHeight, int channels = 3);
    
    /**
     * 随机裁剪
     * @param imageData 输入图像数据
     * @param srcWidth 源图像宽度
     * @param srcHeight 源图像高度
     * @param cropWidth 裁剪宽度
     * @param cropHeight 裁剪高度
     * @param channels 图像通道数
     * @return 裁剪后的图像数据
     */
    std::vector<float> randomCrop(const float* imageData, int srcWidth, int srcHeight,
                                 int cropWidth, int cropHeight, int channels = 3);

private:
    std::mt19937 randomGenerator_;
};

} // namespace fsl

#endif // IMAGE_PROCESSOR_H
