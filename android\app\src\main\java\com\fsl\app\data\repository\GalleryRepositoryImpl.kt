/**
 * 图库仓库实现
 *
 * 实现图库相关的数据操作
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.repository

import android.content.Context
import android.os.Environment
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.model.GalleryImage
import com.fsl.app.domain.repository.GalleryRepository
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 图库仓库实现类
 */
@Singleton
class GalleryRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : GalleryRepository {

    private val _imagesFlow = MutableStateFlow<List<GalleryImage>>(emptyList())
    private val gson = Gson()

    companion object {
        private const val GALLERY_DIR = "FSL_Gallery"
        private const val METADATA_FILE = "image_metadata.json"
    }

    /**
     * 获取图库目录
     */
    private fun getGalleryDir(): File {
        val dir = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), GALLERY_DIR)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }

    /**
     * 获取元数据文件
     */
    private fun getMetadataFile(): File {
        return File(getGalleryDir(), METADATA_FILE)
    }

    override suspend fun getAllImages(): Result<List<GalleryImage>> {
        return withContext(Dispatchers.IO) {
            try {
                val galleryDir = getGalleryDir()
                val metadataFile = getMetadataFile()
                
                // 读取元数据
                val metadata = if (metadataFile.exists()) {
                    val json = metadataFile.readText()
                    val type = object : TypeToken<Map<String, ImageMetadata>>() {}.type
                    gson.fromJson<Map<String, ImageMetadata>>(json, type) ?: emptyMap()
                } else {
                    emptyMap<String, ImageMetadata>()
                }

                // 扫描图库目录
                val imageFiles = galleryDir.listFiles { file ->
                    file.isFile && file.extension.lowercase() in listOf("jpg", "jpeg", "png")
                } ?: emptyArray()

                val images = imageFiles.map { file ->
                    val meta = metadata[file.name]
                    GalleryImage(
                        file = file,
                        classificationResult = meta?.classificationResult,
                        timestamp = meta?.timestamp ?: file.lastModified(),
                        isLearned = meta?.isLearned ?: false
                    )
                }.sortedByDescending { it.timestamp }

                _imagesFlow.value = images
                Result.success(images)
            } catch (e: Exception) {
                android.util.Log.e("GalleryRepository", "获取图库图像失败", e)
                Result.failure(e)
            }
        }
    }

    override fun getImagesFlow(): Flow<List<GalleryImage>> {
        return _imagesFlow.asStateFlow()
    }

    override suspend fun getImagesByLabel(label: String): Result<List<GalleryImage>> {
        return withContext(Dispatchers.IO) {
            try {
                val allImages = getAllImages().getOrThrow()
                val filteredImages = allImages.filter { 
                    it.classificationResult?.className == label 
                }
                Result.success(filteredImages)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun getLearnedImages(): Result<List<GalleryImage>> {
        return withContext(Dispatchers.IO) {
            try {
                val allImages = getAllImages().getOrThrow()
                val learnedImages = allImages.filter { it.isLearned }
                Result.success(learnedImages)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun deleteImage(image: GalleryImage): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // 删除文件
                if (image.file.exists()) {
                    image.file.delete()
                }

                // 更新元数据
                updateMetadata { metadata ->
                    metadata.remove(image.fileName)
                }

                // 刷新图库
                refreshGallery()
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun markAsLearned(image: GalleryImage, className: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                updateMetadata { metadata ->
                    val existingMeta = metadata[image.fileName] ?: ImageMetadata(
                        timestamp = image.timestamp,
                        classificationResult = image.classificationResult,
                        isLearned = false
                    )
                    
                    metadata[image.fileName] = existingMeta.copy(
                        isLearned = true,
                        learnedClassName = className
                    )
                }

                refreshGallery()
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun refreshGallery(): Result<Unit> {
        return getAllImages().map { Unit }
    }

    override suspend fun getGalleryStats(): Result<Map<String, Int>> {
        return withContext(Dispatchers.IO) {
            try {
                val allImages = getAllImages().getOrThrow()
                val stats = mutableMapOf<String, Int>()
                
                stats["total"] = allImages.size
                stats["learned"] = allImages.count { it.isLearned }
                
                // 按分类标签统计
                allImages.groupBy { it.classLabel }.forEach { (label, images) ->
                    stats[label] = images.size
                }

                Result.success(stats.toMap())
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 更新元数据文件
     */
    private suspend fun updateMetadata(updater: (MutableMap<String, ImageMetadata>) -> Unit) {
        withContext(Dispatchers.IO) {
            try {
                val metadataFile = getMetadataFile()
                
                // 读取现有元数据
                val metadata = if (metadataFile.exists()) {
                    val json = metadataFile.readText()
                    val type = object : TypeToken<MutableMap<String, ImageMetadata>>() {}.type
                    gson.fromJson<MutableMap<String, ImageMetadata>>(json, type) ?: mutableMapOf()
                } else {
                    mutableMapOf<String, ImageMetadata>()
                }

                // 应用更新
                updater(metadata)

                // 保存元数据
                val json = gson.toJson(metadata)
                metadataFile.writeText(json)
            } catch (e: Exception) {
                android.util.Log.e("GalleryRepository", "更新元数据失败", e)
            }
        }
    }

    /**
     * 保存图像元数据
     */
    suspend fun saveImageMetadata(
        fileName: String,
        classificationResult: ClassificationResult?,
        timestamp: Long = System.currentTimeMillis()
    ) {
        updateMetadata { metadata ->
            metadata[fileName] = ImageMetadata(
                timestamp = timestamp,
                classificationResult = classificationResult,
                isLearned = false
            )
        }
        refreshGallery()
    }

    /**
     * 图像元数据数据类
     */
    private data class ImageMetadata(
        val timestamp: Long,
        val classificationResult: ClassificationResult?,
        val isLearned: Boolean,
        val learnedClassName: String? = null
    )
}
