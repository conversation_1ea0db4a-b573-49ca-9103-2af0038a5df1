# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- GPU加速学习系统，性能提升2-3倍
- 实时学习进度显示，包含阶段和时间估算
- GPU可用性检测（Vulkan/OpenGL ES）
- 批处理优化的特征提取
- 并行原型计算
- 智能GPU/CPU回退机制
- 用户可控的GPU加速开关
- 类别模块完整实现，支持拍摄样本后自动增加已学习类别
- 异步学习机制，提升用户体验
- 完整的数据库到UI数据流
- 智能类别管理（新建vs添加样本）

### Fixed
- 修复学习过程缓慢的性能问题
- 修复缺少学习进度显示的用户体验问题
- 修复拍摄样本后已学习类别不增加的问题
- 修复SampleCollectionViewModel缺少ModelRepository依赖
- 修复LearningViewModel数据库加载逻辑
- 修复LearnedClass模型缺少accuracy字段
- 修复LearnedClassEntity.toDomainModel()扩展函数映射错误

### Changed
- 学习界面支持GPU加速模式切换
- 动态进度条显示（学习时橙色，收集时蓝色）
- "完成收集"按钮改为"完成学习"，更准确反映功能
- 优化学习流程，支持批量样本学习
- 改进UI反馈，实时显示学习进度和剩余时间

### Performance
- GPU加速特征提取，速度提升2-3倍
- 批处理优化，减少GPU调用开销
- 并行原型计算，提升模型更新效率
- 智能处理间隔，避免GPU过热

## [1.0.0] - 2024-05-27

### Added
- FSL Android应用完整实现
- 基于easyfsl的Few-Shot Learning推理引擎
- 实时相机检测和分类
- 样本收集和增量学习功能
- 图库管理和样本预览
- 手动模式检测
- 完整的数据库存储系统
- Native C++推理引擎
- PyTorch Mobile集成

### Technical Features
- Clean Architecture架构设计
- MVVM模式实现
- Jetpack Compose UI
- Room数据库
- Hilt依赖注入
- Coroutines异步处理
- CameraX相机集成
- JNI Native接口

### Performance
- 200ms推理间隔，流畅实时检测
- 异步样本学习，不阻塞UI
- 多线程架构，推理与可视化分离
- 智能边界框生成
- 优化的内存管理

### User Experience
- 直观的拍摄和学习工作流
- 实时检测结果显示
- 完整的图库管理
- 清晰的UI反馈
- 详细的调试日志
