#!/usr/bin/env python3
"""
PyTorch模型下载和转换脚本

下载预训练模型并转换为PyTorch Mobile格式
用于Android少样本学习项目

<AUTHOR> Assistant
@date 2024
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
import torchvision.transforms as transforms
from torch.utils.mobile_optimizer import optimize_for_mobile
import urllib.request
import zipfile
import json

class PrototypicalNetwork(nn.Module):
    """
    原型网络模型
    
    基于论文: "Prototypical Networks for Few-shot Learning"
    """
    
    def __init__(self, backbone='resnet18', feature_dim=512, num_classes=5):
        super(PrototypicalNetwork, self).__init__()
        
        # 特征提取器 (使用预训练的ResNet)
        if backbone == 'resnet18':
            self.backbone = models.resnet18(pretrained=True)
            # 移除最后的分类层
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-1])
            backbone_dim = 512
        elif backbone == 'mobilenet_v2':
            self.backbone = models.mobilenet_v2(pretrained=True)
            self.backbone.classifier = nn.Identity()
            backbone_dim = 1280
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")
        
        # 特征投影层
        self.feature_projection = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 原型存储 (可学习参数)
        self.prototypes = nn.Parameter(torch.randn(num_classes, feature_dim))
        self.num_classes = num_classes
        self.feature_dim = feature_dim
        
    def extract_features(self, x):
        """提取特征"""
        # 特征提取
        features = self.backbone(x)
        features = features.view(features.size(0), -1)
        
        # 特征投影
        features = self.feature_projection(features)
        
        # L2归一化
        features = F.normalize(features, p=2, dim=1)
        
        return features
    
    def forward(self, x):
        """前向传播"""
        # 提取特征
        features = self.extract_features(x)
        
        # 计算到原型的距离
        prototypes = F.normalize(self.prototypes, p=2, dim=1)
        
        # 计算余弦相似度
        similarities = torch.mm(features, prototypes.t())
        
        return similarities, features
    
    def update_prototype(self, class_idx, support_features):
        """更新指定类别的原型"""
        with torch.no_grad():
            # 计算支持样本的均值作为新原型
            new_prototype = support_features.mean(dim=0)
            new_prototype = F.normalize(new_prototype, p=2, dim=0)
            self.prototypes[class_idx] = new_prototype

def create_model():
    """创建原型网络模型"""
    print("创建原型网络模型...")
    
    # 创建模型
    model = PrototypicalNetwork(
        backbone='mobilenet_v2',  # 使用MobileNet以减小模型大小
        feature_dim=512,
        num_classes=10  # 支持10个类别
    )
    
    # 设置为评估模式
    model.eval()
    
    # 初始化一些示例原型
    with torch.no_grad():
        # 为每个类别生成不同的原型
        for i in range(model.num_classes):
            # 生成具有不同特征分布的原型
            prototype = torch.randn(model.feature_dim)
            prototype = F.normalize(prototype, p=2, dim=0)
            model.prototypes[i] = prototype
    
    print(f"模型创建完成: {model.num_classes}个类别, {model.feature_dim}维特征")
    return model

def convert_to_mobile(model, output_path):
    """转换模型为PyTorch Mobile格式"""
    print("转换模型为PyTorch Mobile格式...")
    
    # 创建示例输入
    example_input = torch.randn(1, 3, 224, 224)
    
    # 追踪模型
    traced_model = torch.jit.trace(model, example_input)
    
    # 优化模型以适配移动端
    optimized_model = optimize_for_mobile(traced_model)
    
    # 保存模型
    optimized_model._save_for_lite_interpreter(output_path)
    
    print(f"模型已保存到: {output_path}")
    return output_path

def create_class_names_file(output_dir):
    """创建类别名称文件"""
    class_names = [
        "cat", "dog", "bird", "flower", "car",
        "person", "bicycle", "airplane", "boat", "train"
    ]
    
    class_names_path = os.path.join(output_dir, "class_names.json")
    with open(class_names_path, 'w', encoding='utf-8') as f:
        json.dump(class_names, f, ensure_ascii=False, indent=2)
    
    print(f"类别名称文件已保存到: {class_names_path}")
    return class_names_path

def create_model_info_file(output_dir, model_path):
    """创建模型信息文件"""
    model_info = {
        "model_name": "prototypical_network",
        "version": "1.0.0",
        "framework": "pytorch_mobile",
        "input_size": [224, 224, 3],
        "feature_dim": 512,
        "num_classes": 10,
        "model_file": os.path.basename(model_path),
        "preprocessing": {
            "mean": [0.485, 0.456, 0.406],
            "std": [0.229, 0.224, 0.225],
            "resize": 224,
            "normalize": True
        }
    }
    
    info_path = os.path.join(output_dir, "model_info.json")
    with open(info_path, 'w', encoding='utf-8') as f:
        json.dump(model_info, f, ensure_ascii=False, indent=2)
    
    print(f"模型信息文件已保存到: {info_path}")
    return info_path

def download_and_prepare_model():
    """下载并准备PyTorch模型"""
    print("=== PyTorch模型下载和转换 ===")
    
    # 创建输出目录
    output_dir = "../app/src/main/assets"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建模型
        model = create_model()
        
        # 转换为移动端格式
        model_path = os.path.join(output_dir, "prototypical_network.ptl")
        convert_to_mobile(model, model_path)
        
        # 创建辅助文件
        create_class_names_file(output_dir)
        create_model_info_file(output_dir, model_path)
        
        # 验证模型
        print("\n验证模型...")
        verify_model(model_path)
        
        print("\n=== 模型准备完成 ===")
        print(f"模型文件: {model_path}")
        print(f"模型大小: {os.path.getsize(model_path) / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False

def verify_model(model_path):
    """验证模型是否正确"""
    try:
        # 加载模型
        model = torch.jit.load(model_path)
        model.eval()
        
        # 测试推理
        test_input = torch.randn(1, 3, 224, 224)
        with torch.no_grad():
            output = model(test_input)
            similarities, features = output
            
        print(f"✓ 模型验证成功")
        print(f"  输入形状: {test_input.shape}")
        print(f"  相似度输出形状: {similarities.shape}")
        print(f"  特征输出形状: {features.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型验证失败: {e}")
        return False

def install_dependencies():
    """安装必要的依赖"""
    print("检查PyTorch依赖...")
    
    try:
        import torch
        import torchvision
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ TorchVision版本: {torchvision.__version__}")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请安装PyTorch: pip install torch torchvision")
        return False

if __name__ == "__main__":
    print("PyTorch模型下载和转换工具")
    print("=" * 50)
    
    # 检查依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 下载和准备模型
    success = download_and_prepare_model()
    
    if success:
        print("\n🎉 模型准备成功！")
        print("现在可以在Android项目中使用PyTorch Mobile模型了。")
        sys.exit(0)
    else:
        print("\n❌ 模型准备失败！")
        sys.exit(1)
