/**
 * 学习类别领域模型
 *
 * 表示一个已学习的分类类别，包含类别信息和原型特征
 *
 * <AUTHOR> Assistant
 * @date 2024
 */
package com.fsl.app.domain.model

/**
 * 学习类别数据类
 *
 * @property id 类别唯一标识符
 * @property name 类别名称
 * @property description 类别描述
 * @property sampleCount 样本数量
 * @property prototypeFeatures 原型特征向量
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 */
data class LearnedClass(
    val id: Long = 0,
    val name: String,
    val description: String = "",
    val sampleCount: Int = 0,
    val accuracy: Float = 0f,
    val prototypeFeatures: FloatArray? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * 重写equals方法，排除prototypeFeatures的比较
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as LearnedClass

        if (id != other.id) return false
        if (name != other.name) return false
        if (description != other.description) return false
        if (sampleCount != other.sampleCount) return false
        if (accuracy != other.accuracy) return false
        if (createdAt != other.createdAt) return false
        if (updatedAt != other.updatedAt) return false

        return true
    }

    /**
     * 重写hashCode方法，排除prototypeFeatures
     */
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + description.hashCode()
        result = 31 * result + sampleCount
        result = 31 * result + accuracy.hashCode()
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + updatedAt.hashCode()
        return result
    }

    /**
     * 检查是否有有效的原型特征
     */
    fun hasValidPrototype(): Boolean {
        return prototypeFeatures != null && prototypeFeatures.isNotEmpty()
    }

    /**
     * 获取特征向量维度
     */
    fun getFeatureDimension(): Int {
        return prototypeFeatures?.size ?: 0
    }
}
