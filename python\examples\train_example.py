"""
少样本学习训练示例
演示如何使用本框架进行少样本学习模型的训练和评估

@author: AI Assistant
@date: 2024
"""

import os
import sys
import torch
from torch.utils.data import DataLoader
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from datasets import LocalDataset, get_default_transforms
from methods import PrototypicalNetworks, SimpleShot, get_backbone
from core import TaskSampler
from training import EpisodicTrainer, TrainingConfig
from inference import InferenceEngine


def create_sample_dataset():
    """
    创建示例数据集
    
    这里创建一个简单的数据集结构用于演示
    实际使用时，请替换为您的真实数据集路径
    """
    # 创建示例数据目录结构
    data_dir = Path("sample_data")
    data_dir.mkdir(exist_ok=True)
    
    # 创建类别目录
    for class_id in range(5):  # 5个类别
        class_dir = data_dir / f"class_{class_id}"
        class_dir.mkdir(exist_ok=True)
        
        # 这里应该放置真实的图像文件
        # 为了演示，我们只创建目录结构
        print(f"请在 {class_dir} 目录中放置类别 {class_id} 的图像文件")
    
    return str(data_dir)


def main():
    """主函数"""
    print("=" * 60)
    print("少样本学习训练示例")
    print("=" * 60)
    
    # 1. 创建或指定数据集路径
    data_path = create_sample_dataset()
    print(f"数据集路径: {data_path}")
    
    # 检查数据集是否存在
    if not any(Path(data_path).iterdir()):
        print("警告: 数据集目录为空，请添加图像数据后重新运行")
        return
    
    # 2. 创建数据集
    print("\n创建数据集...")
    
    # 训练集
    train_dataset = LocalDataset(
        data_source=data_path,
        image_size=84,
        training=True,  # 使用训练时的数据增强
    )
    
    # 验证集（这里使用相同的数据，实际应用中应该使用不同的数据）
    val_dataset = LocalDataset(
        data_source=data_path,
        image_size=84,
        training=False,  # 验证时不使用数据增强
    )
    
    print(f"训练集: {train_dataset}")
    print(f"验证集: {val_dataset}")
    
    # 3. 创建任务采样器
    print("\n创建任务采样器...")
    
    n_way = 5
    n_shot = 5
    n_query = 15
    n_tasks = 100  # 为了演示，使用较少的任务数
    
    train_sampler = TaskSampler(
        dataset=train_dataset,
        n_way=n_way,
        n_shot=n_shot,
        n_query=n_query,
        n_tasks=n_tasks,
    )
    
    val_sampler = TaskSampler(
        dataset=val_dataset,
        n_way=n_way,
        n_shot=n_shot,
        n_query=n_query,
        n_tasks=50,  # 验证任务数较少
    )
    
    print(f"训练采样器: {train_sampler.get_statistics()}")
    
    # 4. 创建数据加载器
    print("\n创建数据加载器...")
    
    train_loader = DataLoader(
        train_dataset,
        batch_sampler=train_sampler,
        num_workers=0,  # Windows下建议设为0
        pin_memory=True,
        collate_fn=train_sampler.episodic_collate_fn,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_sampler=val_sampler,
        num_workers=0,
        pin_memory=True,
        collate_fn=val_sampler.episodic_collate_fn,
    )
    
    # 5. 创建模型
    print("\n创建模型...")
    
    # 创建骨干网络
    backbone = get_backbone("resnet12", feature_dim=512)
    
    # 创建少样本分类器
    model = PrototypicalNetworks(
        backbone=backbone,
        use_softmax=True,
        feature_normalization=2.0,  # L2归一化
    )
    
    print(f"模型: {model}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 6. 配置训练参数
    print("\n配置训练参数...")
    
    config = TrainingConfig(
        num_epochs=10,  # 为了演示，使用较少的epoch
        learning_rate=0.001,
        weight_decay=1e-4,
        optimizer="adam",
        scheduler="step",
        step_size=5,
        gamma=0.5,
        early_stopping=True,
        patience=5,
        log_interval=2,
        eval_interval=1,
        checkpoint_dir="checkpoints",
        random_seed=42,
    )
    
    print(f"训练配置: {config.to_dict()}")
    
    # 7. 创建训练器
    print("\n创建训练器...")
    
    trainer = EpisodicTrainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        config=config,
    )
    
    # 8. 开始训练
    print("\n开始训练...")
    
    try:
        training_results = trainer.train()
        print(f"\n训练完成!")
        print(f"最佳验证准确率: {training_results['best_val_accuracy']:.4f}")
        print(f"总训练时间: {training_results['training_time']:.2f}秒")
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return
    
    # 9. 推理演示
    print("\n推理演示...")
    
    # 创建推理引擎
    inference_engine = InferenceEngine(model=model)
    
    # 使用验证集的第一个任务进行推理演示
    try:
        val_batch = next(iter(val_loader))
        support_images, support_labels, query_images, query_labels, _ = val_batch
        
        # 设置支持集
        class_names = [f"类别_{i}" for i in range(n_way)]
        inference_engine.set_support_set(
            support_images, support_labels, class_names
        )
        
        # 单样本推理
        single_result = inference_engine.predict_single(
            query_images[0],
            return_confidence=True,
            return_probabilities=True,
        )
        
        print(f"单样本推理结果:")
        print(f"  预测类别: {single_result['predicted_class']}")
        print(f"  置信度: {single_result['confidence']:.4f}")
        print(f"  推理时间: {single_result['inference_time']:.4f}秒")
        
        # 批量推理
        batch_results = inference_engine.predict_batch(
            query_images[:5],  # 预测前5个样本
            return_confidence=True,
        )
        
        print(f"\n批量推理结果 (前5个样本):")
        for i, result in enumerate(batch_results):
            print(f"  样本{i}: {result['predicted_class']} (置信度: {result['confidence']:.4f})")
        
        # 任务评估
        task_results = inference_engine.evaluate_on_task(
            support_images, support_labels,
            query_images, query_labels,
            class_names
        )
        
        print(f"\n任务评估结果:")
        print(f"  准确率: {task_results['accuracy']:.4f}")
        print(f"  平均置信度: {task_results['mean_confidence']:.4f}")
        print(f"  推理速度: {task_results['samples_per_second']:.2f} 样本/秒")
        
    except Exception as e:
        print(f"推理演示过程中发生错误: {e}")
    
    # 10. 保存模型
    print("\n保存模型...")
    
    model_save_path = "trained_model.pth"
    inference_engine.save_model(model_save_path)
    print(f"模型已保存到: {model_save_path}")
    
    print("\n=" * 60)
    print("示例运行完成!")
    print("=" * 60)


if __name__ == "__main__":
    main()
