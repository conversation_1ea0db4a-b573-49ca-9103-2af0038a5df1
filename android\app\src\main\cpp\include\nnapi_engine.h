/**
 * Android NNAPI推理引擎头文件
 * 
 * 使用Android Neural Networks API进行高性能推理
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef NNAPI_ENGINE_H
#define NNAPI_ENGINE_H

#include <vector>
#include <string>
#include <memory>
#include <map>

#ifdef USE_NNAPI
#include <android/NeuralNetworks.h>
#endif

namespace fsl {

// 前向声明
using FeatureVector = std::vector<float>;

/**
 * NNAPI推理引擎类
 */
class NNAPIEngine {
public:
    NNAPIEngine();
    ~NNAPIEngine();
    
    /**
     * 初始化NNAPI引擎
     */
    bool initialize();
    
    /**
     * 检查NNAPI是否可用
     */
    static bool isNNAPIAvailable();
    
    /**
     * 加载模型
     * @param modelBuffer 模型数据缓冲区
     * @param modelSize 模型数据大小
     */
    bool loadModel(const void* modelBuffer, size_t modelSize);
    
    /**
     * 执行推理
     * @param inputData 输入数据
     * @param inputSize 输入数据大小
     * @param outputData 输出数据缓冲区
     * @param outputSize 输出数据大小
     */
    bool executeInference(const float* inputData, size_t inputSize,
                         float* outputData, size_t outputSize);
    
    /**
     * 提取特征
     * @param imageData 图像数据 (224x224x3, RGB, 0-1范围)
     * @param width 图像宽度
     * @param height 图像高度
     * @return 特征向量
     */
    FeatureVector extractFeatures(const float* imageData, int width, int height);
    
    /**
     * 分类图像
     * @param imageData 图像数据
     * @param width 图像宽度  
     * @param height 图像高度
     * @param classNames 类别名称列表
     * @return 分类结果 (类别索引, 置信度)
     */
    std::pair<int, float> classify(const float* imageData, int width, int height,
                                  const std::vector<std::string>& classNames);
    
    /**
     * 获取所有类别的分数
     */
    std::vector<float> getAllScores(const float* imageData, int width, int height);
    
    /**
     * 设置输入尺寸
     */
    void setInputSize(int width, int height, int channels = 3);
    
    /**
     * 获取模型信息
     */
    std::map<std::string, std::string> getModelInfo() const;
    
    /**
     * 获取性能统计
     */
    struct PerformanceStats {
        double avgInferenceTime;
        double avgPreprocessTime;
        int totalInferences;
        bool usingNNAPI;
        std::string deviceName;
        
        PerformanceStats() : avgInferenceTime(0), avgPreprocessTime(0),
                           totalInferences(0), usingNNAPI(false) {}
    };
    
    PerformanceStats getPerformanceStats() const;
    
    /**
     * 重置性能统计
     */
    void resetPerformanceStats();

private:
    // NNAPI相关成员
#ifdef USE_NNAPI
    ANeuralNetworksModel* m_model;
    ANeuralNetworksCompilation* m_compilation;
    ANeuralNetworksExecution* m_execution;
    ANeuralNetworksMemory* m_modelMemory;
#endif
    
    // 模型参数
    bool m_initialized;
    bool m_modelLoaded;
    int m_inputWidth;
    int m_inputHeight;
    int m_inputChannels;
    int m_outputSize;
    
    // 性能统计
    mutable PerformanceStats m_stats;
    mutable std::vector<double> m_inferenceTimes;
    mutable std::vector<double> m_preprocessTimes;
    
    // 私有方法
    
    /**
     * 创建NNAPI模型
     */
    bool createModel();
    
    /**
     * 编译模型
     */
    bool compileModel();
    
    /**
     * 准备执行
     */
    bool prepareExecution();
    
    /**
     * 清理资源
     */
    void cleanup();
    
    /**
     * 预处理图像数据
     */
    std::vector<float> preprocessImage(const float* imageData, int width, int height);
    
    /**
     * 后处理输出数据
     */
    std::vector<float> postprocessOutput(const float* outputData, size_t outputSize);
    
    /**
     * 更新性能统计
     */
    void updatePerformanceStats(double inferenceTime, double preprocessTime) const;
    
    /**
     * 获取NNAPI设备信息
     */
    std::string getNNAPIDeviceInfo() const;
    
    /**
     * 检查NNAPI错误
     */
    bool checkNNAPIError(int result, const std::string& operation) const;
    
    /**
     * 创建MobileNetV3模型结构
     */
    bool createMobileNetV3Model();
    
    /**
     * 添加卷积层
     */
    bool addConvolutionLayer(uint32_t inputOperand, uint32_t weightOperand, 
                           uint32_t biasOperand, uint32_t outputOperand,
                           int32_t strideX, int32_t strideY,
                           int32_t paddingLeft, int32_t paddingRight,
                           int32_t paddingTop, int32_t paddingBottom);
    
    /**
     * 添加激活层
     */
    bool addActivationLayer(uint32_t inputOperand, uint32_t outputOperand, int32_t fusedActivation);
    
    /**
     * 添加池化层
     */
    bool addPoolingLayer(uint32_t inputOperand, uint32_t outputOperand,
                        int32_t filterWidth, int32_t filterHeight,
                        int32_t strideX, int32_t strideY,
                        int32_t paddingLeft, int32_t paddingRight,
                        int32_t paddingTop, int32_t paddingBottom);
    
    /**
     * 添加全连接层
     */
    bool addFullyConnectedLayer(uint32_t inputOperand, uint32_t weightOperand,
                               uint32_t biasOperand, uint32_t outputOperand);
};

} // namespace fsl

#endif // NNAPI_ENGINE_H
