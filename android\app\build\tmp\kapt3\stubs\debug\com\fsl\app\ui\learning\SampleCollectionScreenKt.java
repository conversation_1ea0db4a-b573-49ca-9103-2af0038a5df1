package com.fsl.app.ui.learning;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001aH\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u001e\u0010\t\u001a\u001a\u0012\u0004\u0012\u00020\b\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0007\u001a\b\u0010\u0010\u001a\u00020\fH\u0002\u00a8\u0006\u0011"}, d2 = {"SampleCard", "", "sample", "Lcom/fsl/app/presentation/learning/CollectedSample;", "onRemove", "Lkotlin/Function0;", "SampleCollectionScreen", "className", "", "onSamplesCollected", "Lkotlin/Function2;", "", "Landroid/graphics/Bitmap;", "onNavigateBack", "viewModel", "Lcom/fsl/app/presentation/learning/SampleCollectionViewModel;", "createMockSample", "app_debug"})
public final class SampleCollectionScreenKt {
    
    /**
     * 样本收集界面
     */
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    public static final void SampleCollectionScreen(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.util.List<android.graphics.Bitmap>, kotlin.Unit> onSamplesCollected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.learning.SampleCollectionViewModel viewModel) {
    }
    
    /**
     * 样本卡片
     */
    @androidx.compose.runtime.Composable
    private static final void SampleCard(com.fsl.app.presentation.learning.CollectedSample sample, kotlin.jvm.functions.Function0<kotlin.Unit> onRemove) {
    }
    
    /**
     * 创建模拟样本（用于测试）
     */
    private static final android.graphics.Bitmap createMockSample() {
        return null;
    }
}