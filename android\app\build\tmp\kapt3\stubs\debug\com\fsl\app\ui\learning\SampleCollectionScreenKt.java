package com.fsl.app.ui.learning;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\u001e\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001aN\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u001e\u0010\u000f\u001a\u001a\u0012\u0004\u0012\u00020\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u0011\u0012\u0004\u0012\u00020\u00010\u00102\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001aH\u0010\u0013\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u001e\u0010\u000f\u001a\u001a\u0012\u0004\u0012\u00020\n\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u0011\u0012\u0004\u0012\u00020\u00010\u00102\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a\u0010\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002\u00a8\u0006\u0017"}, d2 = {"PermissionDeniedContent", "", "onNavigateBack", "Lkotlin/Function0;", "SampleCard", "sample", "Lcom/fsl/app/presentation/learning/CollectedSample;", "onRemove", "SampleCollectionContent", "className", "", "collectionState", "Lcom/fsl/app/presentation/learning/SampleCollectionState;", "viewModel", "Lcom/fsl/app/presentation/learning/SampleCollectionViewModel;", "onSamplesCollected", "Lkotlin/Function2;", "", "Landroid/graphics/Bitmap;", "SampleCollectionScreen", "formatTime", "timeMs", "", "app_debug"})
public final class SampleCollectionScreenKt {
    
    /**
     * 样本收集界面
     */
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    public static final void SampleCollectionScreen(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.util.List<android.graphics.Bitmap>, kotlin.Unit> onSamplesCollected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.learning.SampleCollectionViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    private static final void SampleCollectionContent(java.lang.String className, com.fsl.app.presentation.learning.SampleCollectionState collectionState, com.fsl.app.presentation.learning.SampleCollectionViewModel viewModel, kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.util.List<android.graphics.Bitmap>, kotlin.Unit> onSamplesCollected, kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    /**
     * 样本卡片
     */
    @androidx.compose.runtime.Composable
    private static final void SampleCard(com.fsl.app.presentation.learning.CollectedSample sample, kotlin.jvm.functions.Function0<kotlin.Unit> onRemove) {
    }
    
    /**
     * 权限拒绝内容
     */
    @androidx.compose.runtime.Composable
    private static final void PermissionDeniedContent(kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    /**
     * 格式化时间显示
     * @param timeMs 时间（毫秒）
     * @return 格式化的时间字符串
     */
    private static final java.lang.String formatTime(long timeMs) {
        return null;
    }
}