[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -2054427415}, {"level_": 0, "message_": "rebuilding JSON F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\3v4h1g2t\\arm64-v8a\\android_gradle_build.json due to:", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2099013228}, {"level_": 0, "message_": "- expected json F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\3v4h1g2t\\arm64-v8a\\android_gradle_build.json file is not present, will remove stale json folder", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 800880752}, {"level_": 0, "message_": "- missing previous command file F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\3v4h1g2t\\arm64-v8a\\metadata_generation_command.txt, will remove stale json folder", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1632421966}, {"level_": 0, "message_": "- command changed from previous, will remove stale json folder", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1338297770}, {"level_": 0, "message_": "created folder 'F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\3v4h1g2t\\arm64-v8a'", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1188652540}, {"level_": 0, "message_": "executing cmake @echo off\n\"D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\geek\\\\fsl\\\\android\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\androidsdk\\\\ndk\\\\27.0.11718014\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3v4h1g2t\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3v4h1g2t\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\geek\\\\fsl\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3v4h1g2t\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_PLATFORM=android-27\" ^\n  \"-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\"\n", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -84894043}, {"level_": 0, "message_": "@echo off\n\"D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\geek\\\\fsl\\\\android\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\androidsdk\\\\ndk\\\\27.0.11718014\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3v4h1g2t\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3v4h1g2t\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\geek\\\\fsl\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3v4h1g2t\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_PLATFORM=android-27\" ^\n  \"-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\"\n", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1458306490}, {"level_": 0, "message_": "Exiting generation of F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\3v4h1g2t\\arm64-v8a\\compile_commands.json.bin normally", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -851512102}, {"level_": 0, "message_": "done executing cmake", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1650644021}, {"level_": 0, "message_": "write command file F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\3v4h1g2t\\arm64-v8a\\metadata_generation_command.txt", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1405025632}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 287429063}]