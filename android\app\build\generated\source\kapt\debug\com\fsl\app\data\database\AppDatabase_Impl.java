package com.fsl.app.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomOpenHelper;
import androidx.room.RoomOpenHelper.Delegate;
import androidx.room.RoomOpenHelper.ValidationResult;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.room.util.TableInfo.Column;
import androidx.room.util.TableInfo.ForeignKey;
import androidx.room.util.TableInfo.Index;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import androidx.sqlite.db.SupportSQLiteOpenHelper.Callback;
import androidx.sqlite.db.SupportSQLiteOpenHelper.Configuration;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile LearnedClassDao _learnedClassDao;

  private volatile TrainingSampleDao _trainingSampleDao;

  @Override
  protected SupportSQLiteOpenHelper createOpenHelper(DatabaseConfiguration configuration) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(configuration, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(SupportSQLiteDatabase _db) {
        _db.execSQL("CREATE TABLE IF NOT EXISTS `learned_classes` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `sampleCount` INTEGER NOT NULL, `accuracy` REAL NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        _db.execSQL("CREATE TABLE IF NOT EXISTS `training_samples` (`id` TEXT NOT NULL, `classId` TEXT NOT NULL, `imagePath` TEXT NOT NULL, `features` BLOB, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        _db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        _db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '05443e5b3e503a3c6a449e6bb5cadf14')");
      }

      @Override
      public void dropAllTables(SupportSQLiteDatabase _db) {
        _db.execSQL("DROP TABLE IF EXISTS `learned_classes`");
        _db.execSQL("DROP TABLE IF EXISTS `training_samples`");
        if (mCallbacks != null) {
          for (int _i = 0, _size = mCallbacks.size(); _i < _size; _i++) {
            mCallbacks.get(_i).onDestructiveMigration(_db);
          }
        }
      }

      @Override
      protected void onCreate(SupportSQLiteDatabase _db) {
        if (mCallbacks != null) {
          for (int _i = 0, _size = mCallbacks.size(); _i < _size; _i++) {
            mCallbacks.get(_i).onCreate(_db);
          }
        }
      }

      @Override
      public void onOpen(SupportSQLiteDatabase _db) {
        mDatabase = _db;
        internalInitInvalidationTracker(_db);
        if (mCallbacks != null) {
          for (int _i = 0, _size = mCallbacks.size(); _i < _size; _i++) {
            mCallbacks.get(_i).onOpen(_db);
          }
        }
      }

      @Override
      public void onPreMigrate(SupportSQLiteDatabase _db) {
        DBUtil.dropFtsSyncTriggers(_db);
      }

      @Override
      public void onPostMigrate(SupportSQLiteDatabase _db) {
      }

      @Override
      protected RoomOpenHelper.ValidationResult onValidateSchema(SupportSQLiteDatabase _db) {
        final HashMap<String, TableInfo.Column> _columnsLearnedClasses = new HashMap<String, TableInfo.Column>(6);
        _columnsLearnedClasses.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLearnedClasses.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLearnedClasses.put("sampleCount", new TableInfo.Column("sampleCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLearnedClasses.put("accuracy", new TableInfo.Column("accuracy", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLearnedClasses.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLearnedClasses.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysLearnedClasses = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesLearnedClasses = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoLearnedClasses = new TableInfo("learned_classes", _columnsLearnedClasses, _foreignKeysLearnedClasses, _indicesLearnedClasses);
        final TableInfo _existingLearnedClasses = TableInfo.read(_db, "learned_classes");
        if (! _infoLearnedClasses.equals(_existingLearnedClasses)) {
          return new RoomOpenHelper.ValidationResult(false, "learned_classes(com.fsl.app.data.database.LearnedClassEntity).\n"
                  + " Expected:\n" + _infoLearnedClasses + "\n"
                  + " Found:\n" + _existingLearnedClasses);
        }
        final HashMap<String, TableInfo.Column> _columnsTrainingSamples = new HashMap<String, TableInfo.Column>(5);
        _columnsTrainingSamples.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTrainingSamples.put("classId", new TableInfo.Column("classId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTrainingSamples.put("imagePath", new TableInfo.Column("imagePath", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTrainingSamples.put("features", new TableInfo.Column("features", "BLOB", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTrainingSamples.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTrainingSamples = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTrainingSamples = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTrainingSamples = new TableInfo("training_samples", _columnsTrainingSamples, _foreignKeysTrainingSamples, _indicesTrainingSamples);
        final TableInfo _existingTrainingSamples = TableInfo.read(_db, "training_samples");
        if (! _infoTrainingSamples.equals(_existingTrainingSamples)) {
          return new RoomOpenHelper.ValidationResult(false, "training_samples(com.fsl.app.data.database.TrainingSampleEntity).\n"
                  + " Expected:\n" + _infoTrainingSamples + "\n"
                  + " Found:\n" + _existingTrainingSamples);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "05443e5b3e503a3c6a449e6bb5cadf14", "c7c4eab8ec35a6b4c8c2cf000204ca3d");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(configuration.context)
        .name(configuration.name)
        .callback(_openCallback)
        .build();
    final SupportSQLiteOpenHelper _helper = configuration.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "learned_classes","training_samples");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `learned_classes`");
      _db.execSQL("DELETE FROM `training_samples`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(LearnedClassDao.class, LearnedClassDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TrainingSampleDao.class, TrainingSampleDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  public List<Migration> getAutoMigrations(
      @NonNull Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecsMap) {
    return Arrays.asList();
  }

  @Override
  public LearnedClassDao learnedClassDao() {
    if (_learnedClassDao != null) {
      return _learnedClassDao;
    } else {
      synchronized(this) {
        if(_learnedClassDao == null) {
          _learnedClassDao = new LearnedClassDao_Impl(this);
        }
        return _learnedClassDao;
      }
    }
  }

  @Override
  public TrainingSampleDao trainingSampleDao() {
    if (_trainingSampleDao != null) {
      return _trainingSampleDao;
    } else {
      synchronized(this) {
        if(_trainingSampleDao == null) {
          _trainingSampleDao = new TrainingSampleDao_Impl(this);
        }
        return _trainingSampleDao;
      }
    }
  }
}
