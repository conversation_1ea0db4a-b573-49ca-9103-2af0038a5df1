/**
 * 相机ViewModel
 *
 * 管理相机状态和图像分类逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.camera

import android.app.Application
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.media.MediaScannerConnection
import android.os.Environment
import androidx.camera.core.ImageProxy
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.ImageProcessingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

/**
 * 相机状态枚举
 */
sealed class CameraState {
    object Idle : CameraState()
    object Processing : CameraState()
    data class Error(val message: String) : CameraState()
}

@HiltViewModel
class CameraViewModel @Inject constructor(
    application: Application,
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase,
    private val galleryRepository: com.fsl.app.data.repository.GalleryRepositoryImpl,
    private val realTimeInferenceEngine: com.fsl.app.data.inference.RealTimeInferenceEngine
) : AndroidViewModel(application) {

    private val _cameraState = MutableStateFlow<CameraState>(CameraState.Idle)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()

    private val _classificationResult = MutableStateFlow<ClassificationResult?>(null)
    val classificationResult: StateFlow<ClassificationResult?> = _classificationResult.asStateFlow()

    private val _trackingResults = MutableStateFlow<List<com.fsl.app.domain.model.TrackingResult>>(emptyList())
    val trackingResults: StateFlow<List<com.fsl.app.domain.model.TrackingResult>> = _trackingResults.asStateFlow()

    private val _isRealTimeMode = MutableStateFlow(true)
    val isRealTimeMode: StateFlow<Boolean> = _isRealTimeMode.asStateFlow()

    private val _isFrontCamera = MutableStateFlow(false)
    val isFrontCamera: StateFlow<Boolean> = _isFrontCamera.asStateFlow()

    // 存储最后捕获的图像，用于手动模式保存
    private var lastCapturedBitmap: Bitmap? = null

    // 初始化实时推理引擎
    init {
        viewModelScope.launch {
            realTimeInferenceEngine.initialize()

            // 监听推理结果
            realTimeInferenceEngine.inferenceResults.collect { result ->
                result?.let {
                    _classificationResult.value = it.classificationResult
                    _trackingResults.value = it.trackingResults
                }
            }
        }
    }

    /**
     * 分类图像
     *
     * @param imageProxy 相机图像代理
     */
    fun classifyImage(imageProxy: ImageProxy?) {
        if (imageProxy == null) {
            android.util.Log.w("CameraViewModel", "ImageProxy为null，跳过推理")
            return
        }

        try {
            if (_isRealTimeMode.value) {
                // 实时模式：使用专用的实时推理引擎
                val bitmap = imageProcessingUseCase.preprocessImageProxy(imageProxy)
                realTimeInferenceEngine.submitInference(bitmap)
            } else {
                // 手动模式：使用传统的同步推理并保存图片
                viewModelScope.launch {
                    try {
                        _cameraState.value = CameraState.Processing

                        val bitmap = imageProcessingUseCase.preprocessImageProxy(imageProxy)
                        lastCapturedBitmap = bitmap // 保存图片用于后续保存到图库

                        val result = classificationUseCase.classify(bitmap)

                        if (result.isSuccess) {
                            val classificationResult = result.getOrNull()
                            _classificationResult.value = classificationResult

                            // 自动保存到图库
                            saveImageToGallery(bitmap, classificationResult)

                            _cameraState.value = CameraState.Idle
                            android.util.Log.i("CameraViewModel", "手动模式分类完成并保存: ${classificationResult?.className}")
                        } else {
                            _cameraState.value = CameraState.Error(
                                result.exceptionOrNull()?.message ?: "分类失败"
                            )
                        }
                    } catch (e: Exception) {
                        _cameraState.value = CameraState.Error(e.message ?: "未知错误")
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("CameraViewModel", "图像处理失败", e)
            if (!_isRealTimeMode.value) {
                _cameraState.value = CameraState.Error(e.message ?: "图像处理失败")
            }
        } finally {
            imageProxy.close()
        }
    }

    /**
     * 分类Bitmap图像
     *
     * @param bitmap 图像位图
     */
    fun classifyBitmap(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                _cameraState.value = CameraState.Processing

                val result = classificationUseCase.classify(bitmap)

                if (result.isSuccess) {
                    _classificationResult.value = result.getOrNull()
                    _cameraState.value = CameraState.Idle
                } else {
                    _cameraState.value = CameraState.Error(
                        result.exceptionOrNull()?.message ?: "分类失败"
                    )
                }
            } catch (e: Exception) {
                _cameraState.value = CameraState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 捕获图像（手动模式）
     * 注意：这个方法需要与CameraPreview组件配合，实际捕获相机图像
     */
    fun captureImage() {
        if (_isRealTimeMode.value) return

        android.util.Log.i("CameraViewModel", "手动模式拍照请求")
        // 实际的图像捕获将通过CameraPreview的回调来处理
        // 这里只是触发拍照动作，真正的图像处理在classifyImage中完成
    }

    /**
     * 切换相机（前置/后置）
     */
    fun switchCamera() {
        _isFrontCamera.value = !_isFrontCamera.value
    }

    /**
     * 切换实时/手动模式
     */
    fun toggleRealTimeMode() {
        val newMode = !_isRealTimeMode.value
        _isRealTimeMode.value = newMode

        // 清除之前的分类结果和跟踪结果
        _classificationResult.value = null
        _trackingResults.value = emptyList()

        // 根据模式启动或停止实时推理引擎
        if (newMode) {
            realTimeInferenceEngine.start()
            android.util.Log.i("CameraViewModel", "切换到实时模式，启动推理引擎")
        } else {
            realTimeInferenceEngine.stop()
            android.util.Log.i("CameraViewModel", "切换到手动模式，停止推理引擎")
        }
    }

    /**
     * 清除分类结果
     */
    fun clearClassificationResult() {
        _classificationResult.value = null
    }

    /**
     * 重置相机状态
     */
    fun resetCameraState() {
        _cameraState.value = CameraState.Idle
    }

    /**
     * 处理相机错误
     */
    fun handleCameraError(error: String) {
        _cameraState.value = CameraState.Error(error)
    }

    /**
     * 获取当前相机配置
     */
    fun getCameraConfig(): CameraConfig {
        return CameraConfig(
            isFrontCamera = _isFrontCamera.value,
            isRealTimeMode = _isRealTimeMode.value
        )
    }

    /**
     * 保存真实图像到图库
     */
    fun saveImageToGallery(bitmap: Bitmap, classificationResult: ClassificationResult?) {
        viewModelScope.launch {
            saveImageToGalleryInternal(bitmap, classificationResult)
        }
    }

    /**
     * 保存图像到图库（内部实现）
     */
    private suspend fun saveImageToGalleryInternal(bitmap: Bitmap, classificationResult: ClassificationResult?) {
        withContext(Dispatchers.IO) {
            try {
                val context = getApplication<Application>()
                val timestamp = System.currentTimeMillis()
                val filename = "FSL_${timestamp}.jpg"
                val imagesDir = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "FSL_Gallery")

                if (!imagesDir.exists()) {
                    imagesDir.mkdirs()
                }

                val imageFile = File(imagesDir, filename)
                val outputStream = FileOutputStream(imageFile)

                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                outputStream.flush()
                outputStream.close()

                android.util.Log.i("CameraViewModel", "图像已保存到: ${imageFile.absolutePath}")

                // 保存元数据
                galleryRepository.saveImageMetadata(filename, classificationResult, timestamp)

                // 通知媒体扫描器
                MediaScannerConnection.scanFile(
                    context,
                    arrayOf(imageFile.absolutePath),
                    arrayOf("image/jpeg"),
                    null
                )

            } catch (e: Exception) {
                android.util.Log.e("CameraViewModel", "保存图像失败", e)
            }
        }
    }

    /**
     * 创建测试图片（用于演示图库功能）
     */
    fun createTestImages() {
        viewModelScope.launch {
            try {
                val testImages = listOf(
                    "苹果" to Color.rgb(220, 20, 60),
                    "香蕉" to Color.rgb(255, 255, 0),
                    "橙子" to Color.rgb(255, 165, 0),
                    "杯子" to Color.rgb(70, 130, 180),
                    "书本" to Color.rgb(139, 69, 19)
                )

                testImages.forEach { (className, color) ->
                    val bitmap = createTestBitmap(className, color)
                    val classificationResult = ClassificationResult(
                        className = className,
                        confidence = (0.7f + Math.random() * 0.3f).toFloat(),
                        timestamp = System.currentTimeMillis()
                    )
                    saveImageToGallery(bitmap, classificationResult)
                }

                android.util.Log.i("CameraViewModel", "测试图片创建完成")
            } catch (e: Exception) {
                android.util.Log.e("CameraViewModel", "创建测试图片失败", e)
            }
        }
    }

    /**
     * 创建测试用的Bitmap
     */
    private fun createTestBitmap(className: String, color: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(400, 400, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint()

        // 绘制背景
        paint.color = Color.rgb(240, 240, 240)
        canvas.drawRect(0f, 0f, 400f, 400f, paint)

        // 绘制主要形状
        paint.color = color
        when (className) {
            "苹果", "橙子" -> {
                canvas.drawCircle(200f, 200f, 120f, paint)
            }
            "香蕉" -> {
                canvas.drawOval(100f, 160f, 300f, 240f, paint)
            }
            "杯子", "书本" -> {
                canvas.drawRect(120f, 140f, 280f, 260f, paint)
            }
        }

        // 添加文字标签
        paint.color = Color.BLACK
        paint.textSize = 32f
        paint.textAlign = Paint.Align.CENTER
        paint.typeface = android.graphics.Typeface.DEFAULT_BOLD
        canvas.drawText(className, 200f, 350f, paint)

        return bitmap
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        realTimeInferenceEngine.cleanup()
        lastCapturedBitmap?.recycle()
        lastCapturedBitmap = null
        android.util.Log.i("CameraViewModel", "ViewModel资源已清理")
    }
}

/**
 * 相机配置数据类
 */
data class CameraConfig(
    val isFrontCamera: Boolean,
    val isRealTimeMode: Boolean
)
