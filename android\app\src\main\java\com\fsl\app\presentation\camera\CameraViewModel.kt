/**
 * 相机ViewModel
 *
 * 管理相机状态和图像分类逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.camera

import android.app.Application
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.media.MediaScannerConnection
import android.os.Environment
import androidx.camera.core.ImageProxy
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.ImageProcessingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

/**
 * 相机状态枚举
 */
sealed class CameraState {
    object Idle : CameraState()
    object Processing : CameraState()
    data class Error(val message: String) : CameraState()
}

@HiltViewModel
class CameraViewModel @Inject constructor(
    application: Application,
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase,
    private val galleryRepository: com.fsl.app.data.repository.GalleryRepositoryImpl
) : AndroidViewModel(application) {

    private val _cameraState = MutableStateFlow<CameraState>(CameraState.Idle)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()

    private val _classificationResult = MutableStateFlow<ClassificationResult?>(null)
    val classificationResult: StateFlow<ClassificationResult?> = _classificationResult.asStateFlow()

    private val _isRealTimeMode = MutableStateFlow(true)
    val isRealTimeMode: StateFlow<Boolean> = _isRealTimeMode.asStateFlow()

    private val _isFrontCamera = MutableStateFlow(false)
    val isFrontCamera: StateFlow<Boolean> = _isFrontCamera.asStateFlow()

    /**
     * 分类图像
     *
     * @param imageProxy 相机图像代理
     */
    fun classifyImage(imageProxy: ImageProxy?) {
        viewModelScope.launch {
            try {
                // 实时模式下不显示处理状态，避免闪烁
                if (!_isRealTimeMode.value) {
                    _cameraState.value = CameraState.Processing
                }

                if (imageProxy == null) {
                    // 如果ImageProxy为null，使用模拟数据进行分类
                    val mockBitmap = createMockClassificationBitmap()
                    val result = classificationUseCase.classify(mockBitmap)

                    if (result.isSuccess) {
                        _classificationResult.value = result.getOrNull()
                        // 实时模式下不改变状态
                        if (!_isRealTimeMode.value) {
                            _cameraState.value = CameraState.Idle
                        }
                    } else {
                        // 实时模式下不显示错误，避免闪烁
                        if (!_isRealTimeMode.value) {
                            _cameraState.value = CameraState.Error(
                                result.exceptionOrNull()?.message ?: "分类失败"
                            )
                        }
                    }
                    return@launch
                }

                // 预处理图像
                val bitmap = imageProcessingUseCase.preprocessImageProxy(imageProxy)

                // 执行分类
                val result = classificationUseCase.classify(bitmap)

                if (result.isSuccess) {
                    _classificationResult.value = result.getOrNull()
                    // 实时模式下不改变状态
                    if (!_isRealTimeMode.value) {
                        _cameraState.value = CameraState.Idle
                    }
                } else {
                    // 实时模式下不显示错误，避免闪烁
                    if (!_isRealTimeMode.value) {
                        _cameraState.value = CameraState.Error(
                            result.exceptionOrNull()?.message ?: "分类失败"
                        )
                    }
                }
            } catch (e: Exception) {
                // 实时模式下不显示错误，避免闪烁
                if (!_isRealTimeMode.value) {
                    _cameraState.value = CameraState.Error(e.message ?: "未知错误")
                }
            } finally {
                imageProxy?.close()
            }
        }
    }

    /**
     * 分类Bitmap图像
     *
     * @param bitmap 图像位图
     */
    fun classifyBitmap(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                _cameraState.value = CameraState.Processing

                val result = classificationUseCase.classify(bitmap)

                if (result.isSuccess) {
                    _classificationResult.value = result.getOrNull()
                    _cameraState.value = CameraState.Idle
                } else {
                    _cameraState.value = CameraState.Error(
                        result.exceptionOrNull()?.message ?: "分类失败"
                    )
                }
            } catch (e: Exception) {
                _cameraState.value = CameraState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 捕获图像（手动模式）
     */
    fun captureImage() {
        if (_isRealTimeMode.value) return

        viewModelScope.launch {
            try {
                _cameraState.value = CameraState.Processing

                // 创建模拟图像进行分类（在真实应用中，这里会从相机获取图像）
                val mockBitmap = createMockClassificationBitmap()

                // 执行分类
                val result = classificationUseCase.classify(mockBitmap)

                if (result.isSuccess) {
                    _classificationResult.value = result.getOrNull()
                    _cameraState.value = CameraState.Idle

                    // 保存图像到图库
                    saveImageToGallery(mockBitmap, result.getOrNull())
                } else {
                    _cameraState.value = CameraState.Error(
                        result.exceptionOrNull()?.message ?: "分类失败"
                    )
                }
            } catch (e: Exception) {
                _cameraState.value = CameraState.Error(e.message ?: "拍照失败")
            }
        }
    }

    /**
     * 切换相机（前置/后置）
     */
    fun switchCamera() {
        _isFrontCamera.value = !_isFrontCamera.value
    }

    /**
     * 切换实时/手动模式
     */
    fun toggleRealTimeMode() {
        _isRealTimeMode.value = !_isRealTimeMode.value
        // 清除之前的分类结果
        _classificationResult.value = null
    }

    /**
     * 清除分类结果
     */
    fun clearClassificationResult() {
        _classificationResult.value = null
    }

    /**
     * 重置相机状态
     */
    fun resetCameraState() {
        _cameraState.value = CameraState.Idle
    }

    /**
     * 处理相机错误
     */
    fun handleCameraError(error: String) {
        _cameraState.value = CameraState.Error(error)
    }

    /**
     * 获取当前相机配置
     */
    fun getCameraConfig(): CameraConfig {
        return CameraConfig(
            isFrontCamera = _isFrontCamera.value,
            isRealTimeMode = _isRealTimeMode.value
        )
    }

    /**
     * 创建模拟分类用的Bitmap
     */
    private fun createMockClassificationBitmap(): Bitmap {
        val bitmap = Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint()

        // 绘制背景
        paint.color = Color.rgb(240, 240, 240)
        canvas.drawRect(0f, 0f, 224f, 224f, paint)

        // 根据时间选择不同的物体类型
        val objectTypes = arrayOf("apple", "banana", "orange", "cup", "book")
        val currentType = objectTypes[(System.currentTimeMillis() / 3000 % objectTypes.size).toInt()]

        when (currentType) {
            "apple" -> {
                // 绘制苹果 - 红色圆形
                paint.color = Color.rgb(220, 20, 60)
                canvas.drawCircle(112f, 112f, 60f, paint)
            }
            "banana" -> {
                // 绘制香蕉 - 黄色椭圆
                paint.color = Color.rgb(255, 255, 0)
                canvas.drawOval(60f, 80f, 164f, 144f, paint)
            }
            "orange" -> {
                // 绘制橙子 - 橙色圆形
                paint.color = Color.rgb(255, 165, 0)
                canvas.drawCircle(112f, 112f, 55f, paint)
            }
            "cup" -> {
                // 绘制杯子 - 蓝色矩形
                paint.color = Color.rgb(70, 130, 180)
                canvas.drawRect(80f, 90f, 144f, 160f, paint)
            }
            "book" -> {
                // 绘制书本 - 棕色矩形
                paint.color = Color.rgb(139, 69, 19)
                canvas.drawRect(70f, 80f, 154f, 144f, paint)
            }
        }

        return bitmap
    }

    /**
     * 保存图像到图库
     */
    private suspend fun saveImageToGallery(bitmap: Bitmap, classificationResult: ClassificationResult?) {
        withContext(Dispatchers.IO) {
            try {
                val context = getApplication<Application>()
                val timestamp = System.currentTimeMillis()
                val filename = "FSL_${timestamp}.jpg"
                val imagesDir = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES), "FSL_Gallery")

                if (!imagesDir.exists()) {
                    imagesDir.mkdirs()
                }

                val imageFile = File(imagesDir, filename)
                val outputStream = FileOutputStream(imageFile)

                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                outputStream.flush()
                outputStream.close()

                android.util.Log.i("CameraViewModel", "图像已保存到: ${imageFile.absolutePath}")

                // 保存元数据
                galleryRepository.saveImageMetadata(filename, classificationResult, timestamp)

                // 通知媒体扫描器
                MediaScannerConnection.scanFile(
                    context,
                    arrayOf(imageFile.absolutePath),
                    arrayOf("image/jpeg"),
                    null
                )

            } catch (e: Exception) {
                android.util.Log.e("CameraViewModel", "保存图像失败", e)
            }
        }
    }
}

/**
 * 相机配置数据类
 */
data class CameraConfig(
    val isFrontCamera: Boolean,
    val isRealTimeMode: Boolean
)
