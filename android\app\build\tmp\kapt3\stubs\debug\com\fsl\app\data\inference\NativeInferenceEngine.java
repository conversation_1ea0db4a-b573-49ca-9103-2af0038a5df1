package com.fsl.app.data.inference;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010$\n\u0002\b\t\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u0000 ,2\u00020\u0001:\u0001,B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J$\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u0086 \u00a2\u0006\u0002\u0010\rJ#\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0012H\u0086 J\t\u0010\u0014\u001a\u00020\u0015H\u0086 J#\u0010\u0016\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0012H\u0086 J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\n0\fH\u0086 \u00a2\u0006\u0002\u0010\u0018J\u0017\u0010\u0019\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u001aH\u0086 J\t\u0010\u001b\u001a\u00020\bH\u0086 J\t\u0010\u001c\u001a\u00020\bH\u0086 J\t\u0010\u001d\u001a\u00020\bH\u0086 J\u0011\u0010\u001e\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020\nH\u0086 J\u0011\u0010 \u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0086 J\u0011\u0010!\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020\nH\u0086 J!\u0010\"\u001a\u00020\u00152\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u00122\u0006\u0010&\u001a\u00020\u0012H\u0086 J.\u0010\'\u001a\n\u0012\u0004\u0012\u00020(\u0018\u00010\f2\u0006\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0012H\u0086 \u00a2\u0006\u0002\u0010)J$\u0010*\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\fH\u0086 \u00a2\u0006\u0002\u0010\rJ\u000e\u0010+\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006-"}, d2 = {"Lcom/fsl/app/data/inference/NativeInferenceEngine;", "", "()V", "bitmapToFloatArray", "", "bitmap", "Landroid/graphics/Bitmap;", "nativeAddClass", "", "className", "", "features", "", "(Ljava/lang/String;[[F)Z", "nativeClassify", "Lcom/fsl/app/domain/model/ClassificationResult;", "imageData", "width", "", "height", "nativeClearTracking", "", "nativeExtractFeatures", "nativeGetClassNames", "()[Ljava/lang/String;", "nativeGetModelInfo", "", "nativeInitialize", "nativeIsInitialized", "nativeIsNNAPIAvailable", "nativeLoadModel", "filePath", "nativeRemoveClass", "nativeSaveModel", "nativeSetTrackingParams", "iouThreshold", "", "maxAge", "minHits", "nativeTrackAndClassify", "Lcom/fsl/app/domain/model/TrackingResult;", "([FII)[Lcom/fsl/app/domain/model/TrackingResult;", "nativeUpdateClass", "preprocessBitmap", "Companion", "app_debug"})
@javax.inject.Singleton
public final class NativeInferenceEngine {
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.inference.NativeInferenceEngine.Companion Companion = null;
    private static boolean nativeLibraryLoaded = false;
    
    @javax.inject.Inject
    public NativeInferenceEngine() {
        super();
    }
    
    /**
     * 初始化推理引擎
     */
    public final native boolean nativeInitialize() {
        return false;
    }
    
    /**
     * 分类图像
     */
    @org.jetbrains.annotations.Nullable
    public final native com.fsl.app.domain.model.ClassificationResult nativeClassify(@org.jetbrains.annotations.NotNull
    float[] imageData, int width, int height) {
        return null;
    }
    
    /**
     * 提取特征
     */
    @org.jetbrains.annotations.Nullable
    public final native float[] nativeExtractFeatures(@org.jetbrains.annotations.NotNull
    float[] imageData, int width, int height) {
        return null;
    }
    
    /**
     * 添加类别
     */
    public final native boolean nativeAddClass(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    float[][] features) {
        return false;
    }
    
    /**
     * 更新类别
     */
    public final native boolean nativeUpdateClass(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    float[][] features) {
        return false;
    }
    
    /**
     * 删除类别
     */
    public final native boolean nativeRemoveClass(@org.jetbrains.annotations.NotNull
    java.lang.String className) {
        return false;
    }
    
    /**
     * 获取类别名称列表
     */
    @org.jetbrains.annotations.NotNull
    public final native java.lang.String[] nativeGetClassNames() {
        return null;
    }
    
    /**
     * 保存模型
     */
    public final native boolean nativeSaveModel(@org.jetbrains.annotations.NotNull
    java.lang.String filePath) {
        return false;
    }
    
    /**
     * 加载模型
     */
    public final native boolean nativeLoadModel(@org.jetbrains.annotations.NotNull
    java.lang.String filePath) {
        return false;
    }
    
    /**
     * 检查是否已初始化
     */
    public final native boolean nativeIsInitialized() {
        return false;
    }
    
    /**
     * 实时跟踪和分类
     */
    @org.jetbrains.annotations.Nullable
    public final native com.fsl.app.domain.model.TrackingResult[] nativeTrackAndClassify(@org.jetbrains.annotations.NotNull
    float[] imageData, int width, int height) {
        return null;
    }
    
    /**
     * 清除跟踪状态
     */
    public final native void nativeClearTracking() {
    }
    
    /**
     * 设置跟踪参数
     */
    public final native void nativeSetTrackingParams(float iouThreshold, int maxAge, int minHits) {
    }
    
    /**
     * 获取模型信息
     */
    @org.jetbrains.annotations.Nullable
    public final native java.util.Map<java.lang.String, java.lang.String> nativeGetModelInfo() {
        return null;
    }
    
    /**
     * 检查NNAPI是否可用
     */
    public final native boolean nativeIsNNAPIAvailable() {
        return false;
    }
    
    /**
     * 将Bitmap转换为FloatArray
     */
    @org.jetbrains.annotations.NotNull
    public final float[] bitmapToFloatArray(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 预处理Bitmap
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap preprocessBitmap(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/fsl/app/data/inference/NativeInferenceEngine$Companion;", "", "()V", "nativeLibraryLoaded", "", "isNativeLibraryAvailable", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final boolean isNativeLibraryAvailable() {
            return false;
        }
    }
}