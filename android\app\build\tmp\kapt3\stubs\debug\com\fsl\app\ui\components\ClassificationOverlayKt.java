package com.fsl.app.ui.components;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u00a8\u0006\u0006"}, d2 = {"ClassificationOverlay", "", "result", "Lcom/fsl/app/domain/model/ClassificationResult;", "modifier", "Landroidx/compose/ui/Modifier;", "app_debug"})
public final class ClassificationOverlayKt {
    
    /**
     * 分类结果覆盖层
     *
     * @param result 分类结果
     * @param modifier 修饰符
     */
    @androidx.compose.runtime.Composable
    public static final void ClassificationOverlay(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult result, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
}