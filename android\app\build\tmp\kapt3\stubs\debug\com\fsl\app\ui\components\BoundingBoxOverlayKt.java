package com.fsl.app.ui.components;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a8\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u0007\u001a\u001a\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\bH\u0003\u001a\u001a\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00062\b\b\u0002\u0010\r\u001a\u00020\bH\u0003\u001a)\u0010\u0010\u001a\u00020\u0001*\u00020\u00112\u0006\u0010\f\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u0013H\u0002\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0015\u001a)\u0010\u0016\u001a\u00020\u0001*\u00020\u00112\u0006\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0013H\u0002\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u0019"}, d2 = {"BoundingBoxOverlay", "", "modifier", "Landroidx/compose/ui/Modifier;", "trackingResults", "", "Lcom/fsl/app/domain/model/TrackingResult;", "isRealTimeMode", "", "currentClassification", "Lcom/fsl/app/domain/model/ClassificationResult;", "ClassificationCard", "classification", "isCompact", "TrackingCard", "tracking", "drawCenterDetectionBox", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "canvasSize", "Landroidx/compose/ui/geometry/Size;", "drawCenterDetectionBox-cSwnlzA", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/fsl/app/domain/model/ClassificationResult;J)V", "drawTrackingBox", "drawTrackingBox-cSwnlzA", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/fsl/app/domain/model/TrackingResult;J)V", "app_debug"})
public final class BoundingBoxOverlayKt {
    
    /**
     * 边界框覆盖组件
     */
    @androidx.compose.runtime.Composable
    public static final void BoundingBoxOverlay(@org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.TrackingResult> trackingResults, boolean isRealTimeMode, @org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult currentClassification) {
    }
    
    /**
     * 分类结果卡片
     */
    @androidx.compose.runtime.Composable
    private static final void ClassificationCard(com.fsl.app.domain.model.ClassificationResult classification, boolean isCompact) {
    }
    
    /**
     * 跟踪结果卡片
     */
    @androidx.compose.runtime.Composable
    private static final void TrackingCard(com.fsl.app.domain.model.TrackingResult tracking, boolean isCompact) {
    }
}