# PyTorch环境设置脚本
# 
# 安装PyTorch并下载模型用于Android项目
#
# <AUTHOR> Assistant
# @date 2024

Write-Host "=== PyTorch环境设置 ===" -ForegroundColor Green

$errors = 0

# 检查Python
Write-Host "`n1. 检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($pythonVersion -match "Python (\d+\.\d+)") {
        $version = [version]$matches[1]
        if ($version -ge [version]"3.7") {
            Write-Host "  ✅ Python版本: $pythonVersion" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Python版本过低: $pythonVersion (需要3.7+)" -ForegroundColor Red
            $errors++
        }
    } else {
        Write-Host "  ❌ 无法获取Python版本" -ForegroundColor Red
        $errors++
    }
} catch {
    Write-Host "  ❌ Python未安装或不在PATH中" -ForegroundColor Red
    Write-Host "  请安装Python 3.7+: https://www.python.org/downloads/" -ForegroundColor Yellow
    $errors++
}

# 检查pip
Write-Host "`n2. 检查pip..." -ForegroundColor Yellow
try {
    $pipVersion = pip --version 2>&1
    Write-Host "  ✅ pip版本: $pipVersion" -ForegroundColor Green
} catch {
    Write-Host "  ❌ pip未安装" -ForegroundColor Red
    $errors++
}

if ($errors -gt 0) {
    Write-Host "`n❌ 环境检查失败，请先安装Python和pip" -ForegroundColor Red
    exit $errors
}

# 创建虚拟环境
Write-Host "`n3. 创建Python虚拟环境..." -ForegroundColor Yellow
$venvPath = "pytorch_env"

if (Test-Path $venvPath) {
    Write-Host "  虚拟环境已存在，删除旧环境..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force $venvPath
}

try {
    python -m venv $venvPath
    Write-Host "  ✅ 虚拟环境创建成功" -ForegroundColor Green
} catch {
    Write-Host "  ❌ 虚拟环境创建失败" -ForegroundColor Red
    $errors++
}

# 激活虚拟环境
Write-Host "`n4. 激活虚拟环境..." -ForegroundColor Yellow
$activateScript = "$venvPath\Scripts\Activate.ps1"

if (Test-Path $activateScript) {
    try {
        & $activateScript
        Write-Host "  ✅ 虚拟环境激活成功" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ 虚拟环境激活失败" -ForegroundColor Red
        $errors++
    }
} else {
    Write-Host "  ❌ 激活脚本不存在" -ForegroundColor Red
    $errors++
}

# 升级pip
Write-Host "`n5. 升级pip..." -ForegroundColor Yellow
try {
    python -m pip install --upgrade pip
    Write-Host "  ✅ pip升级完成" -ForegroundColor Green
} catch {
    Write-Host "  ⚠️  pip升级失败，继续安装..." -ForegroundColor Yellow
}

# 安装PyTorch
Write-Host "`n6. 安装PyTorch..." -ForegroundColor Yellow
Write-Host "  正在安装PyTorch CPU版本..." -ForegroundColor White

try {
    # 安装PyTorch CPU版本 (更快下载)
    python -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    Write-Host "  ✅ PyTorch安装成功" -ForegroundColor Green
} catch {
    Write-Host "  ❌ PyTorch安装失败，尝试默认源..." -ForegroundColor Yellow
    try {
        python -m pip install torch torchvision torchaudio
        Write-Host "  ✅ PyTorch安装成功 (默认源)" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ PyTorch安装失败" -ForegroundColor Red
        $errors++
    }
}

# 验证PyTorch安装
Write-Host "`n7. 验证PyTorch安装..." -ForegroundColor Yellow
try {
    $torchVersion = python -c "import torch; print(f'PyTorch {torch.__version__}')" 2>&1
    $torchvisionVersion = python -c "import torchvision; print(f'TorchVision {torchvision.__version__}')" 2>&1
    
    Write-Host "  ✅ $torchVersion" -ForegroundColor Green
    Write-Host "  ✅ $torchvisionVersion" -ForegroundColor Green
} catch {
    Write-Host "  ❌ PyTorch验证失败" -ForegroundColor Red
    $errors++
}

# 下载和转换模型
if ($errors -eq 0) {
    Write-Host "`n8. 下载和转换模型..." -ForegroundColor Yellow
    
    try {
        python download_pytorch_model.py
        Write-Host "  ✅ 模型下载和转换成功" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ 模型下载和转换失败" -ForegroundColor Red
        Write-Host "  错误信息: $_" -ForegroundColor Red
        $errors++
    }
}

# 检查生成的文件
Write-Host "`n9. 检查生成的文件..." -ForegroundColor Yellow
$assetFiles = @(
    "../app/src/main/assets/prototypical_network.ptl",
    "../app/src/main/assets/class_names.json",
    "../app/src/main/assets/model_info.json"
)

$missingFiles = @()
foreach ($file in $assetFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        $sizeStr = if ($size -gt 1MB) { "{0:N1} MB" -f ($size / 1MB) } else { "{0:N0} KB" -f ($size / 1KB) }
        Write-Host "  ✅ $file ($sizeStr)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (缺失)" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    $errors += $missingFiles.Count
}

# 创建使用说明
Write-Host "`n10. 创建使用说明..." -ForegroundColor Yellow
$readmePath = "../PYTORCH_SETUP.md"
$readmeContent = @"
# PyTorch Mobile 设置说明

## 环境信息
- Python版本: $(python --version 2>&1)
- PyTorch版本: $(python -c "import torch; print(torch.__version__)" 2>&1)
- TorchVision版本: $(python -c "import torchvision; print(torchvision.__version__)" 2>&1)

## 生成的文件
- prototypical_network.ptl: PyTorch Mobile模型文件
- class_names.json: 类别名称配置
- model_info.json: 模型信息配置

## 使用方法
1. 确保Android项目中包含PyTorch Mobile依赖
2. 模型文件已放置在app/src/main/assets/目录下
3. 运行Android项目即可使用真实的PyTorch推理

## 重新生成模型
如需重新生成模型，运行:
```bash
cd scripts
python download_pytorch_model.py
```

## 注意事项
- 模型使用MobileNetV2作为骨干网络，适合移动端部署
- 支持10个类别的少样本学习
- 特征维度为512维
- 输入图像尺寸为224x224
"@

$readmeContent | Out-File -FilePath $readmePath -Encoding UTF8
Write-Host "  ✅ 使用说明已创建: $readmePath" -ForegroundColor Green

# 总结
Write-Host "`n=== PyTorch环境设置总结 ===" -ForegroundColor Cyan
Write-Host "错误数量: $errors" -ForegroundColor $(if ($errors -eq 0) { "Green" } else { "Red" })

if ($errors -eq 0) {
    Write-Host "`n🎉 PyTorch环境设置成功！" -ForegroundColor Green
    Write-Host "✅ Python虚拟环境已创建" -ForegroundColor Green
    Write-Host "✅ PyTorch已安装并验证" -ForegroundColor Green
    Write-Host "✅ 原型网络模型已生成" -ForegroundColor Green
    Write-Host "✅ Android资源文件已准备" -ForegroundColor Green
    
    Write-Host "`n📱 下一步:" -ForegroundColor Cyan
    Write-Host "1. 在Android Studio中打开项目" -ForegroundColor White
    Write-Host "2. 同步Gradle依赖" -ForegroundColor White
    Write-Host "3. 编译并运行项目" -ForegroundColor White
    Write-Host "4. 项目将使用真实的PyTorch Mobile推理" -ForegroundColor White
    
    exit 0
} else {
    Write-Host "`n❌ PyTorch环境设置失败！" -ForegroundColor Red
    Write-Host "请检查上述错误并重新运行脚本" -ForegroundColor Red
    exit $errors
}
