// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.domain.usecase;

import com.fsl.app.domain.repository.IImageProcessingRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class ImageProcessingUseCase_Factory implements Factory<ImageProcessingUseCase> {
  private final Provider<IImageProcessingRepository> imageProcessingRepositoryProvider;

  public ImageProcessingUseCase_Factory(
      Provider<IImageProcessingRepository> imageProcessingRepositoryProvider) {
    this.imageProcessingRepositoryProvider = imageProcessingRepositoryProvider;
  }

  @Override
  public ImageProcessingUseCase get() {
    return newInstance(imageProcessingRepositoryProvider.get());
  }

  public static ImageProcessingUseCase_Factory create(
      Provider<IImageProcessingRepository> imageProcessingRepositoryProvider) {
    return new ImageProcessingUseCase_Factory(imageProcessingRepositoryProvider);
  }

  public static ImageProcessingUseCase newInstance(
      IImageProcessingRepository imageProcessingRepository) {
    return new ImageProcessingUseCase(imageProcessingRepository);
  }
}
