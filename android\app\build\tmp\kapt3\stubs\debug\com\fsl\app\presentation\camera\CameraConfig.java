package com.fsl.app.presentation.camera;

import java.lang.System;

/**
 * 相机配置数据类
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\t\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\b\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\t\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u00032\b\u0010\u000b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\f\u001a\u00020\rH\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0006R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/fsl/app/presentation/camera/CameraConfig;", "", "isFrontCamera", "", "isRealTimeMode", "(ZZ)V", "()Z", "component1", "component2", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class CameraConfig {
    private final boolean isFrontCamera = false;
    private final boolean isRealTimeMode = false;
    
    /**
     * 相机配置数据类
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.camera.CameraConfig copy(boolean isFrontCamera, boolean isRealTimeMode) {
        return null;
    }
    
    /**
     * 相机配置数据类
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 相机配置数据类
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 相机配置数据类
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public CameraConfig(boolean isFrontCamera, boolean isRealTimeMode) {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean isFrontCamera() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean isRealTimeMode() {
        return false;
    }
}