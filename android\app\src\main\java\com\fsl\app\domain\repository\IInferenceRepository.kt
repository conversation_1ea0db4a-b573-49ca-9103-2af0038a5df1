/**
 * 推理仓库接口
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.repository

import android.graphics.Bitmap
import com.fsl.app.domain.model.ClassificationResult

/**
 * 推理仓库接口
 */
interface IInferenceRepository {

    /**
     * 初始化推理引擎
     */
    suspend fun initialize(): Result<Unit>

    /**
     * 分类图像
     */
    suspend fun classify(image: Bitmap): Result<ClassificationResult>

    /**
     * 提取特征
     */
    suspend fun extractFeatures(images: List<Bitmap>): Result<List<FloatArray>>

    /**
     * 提取特征 (支持GPU加速)
     */
    suspend fun extractFeatures(images: List<Bitmap>, useGpu: Boolean = true): Result<List<FloatArray>>

    /**
     * 更新模型
     */
    suspend fun updateModel(className: String, features: List<FloatArray>): Result<Unit>

    /**
     * 更新模型 (支持GPU加速)
     */
    suspend fun updateModel(className: String, features: List<FloatArray>, useGpu: Boolean = true): Result<Unit>

    /**
     * 删除类别
     */
    suspend fun deleteClass(className: String): Result<Unit>

    /**
     * 保存模型
     */
    suspend fun saveModel(): Result<Unit>

    /**
     * 加载模型
     */
    suspend fun loadModel(): Result<Unit>

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean

    /**
     * 获取模型信息
     */
    fun getModelInfo(): Map<String, Any>
}
