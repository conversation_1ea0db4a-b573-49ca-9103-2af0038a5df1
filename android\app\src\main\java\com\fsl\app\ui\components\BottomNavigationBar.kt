/**
 * 底部导航栏组件
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CameraAlt
import androidx.compose.material.icons.filled.Photo
import androidx.compose.material.icons.filled.School
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import com.fsl.app.R

/**
 * 导航项数据类
 */
data class NavigationItem(
    val route: String,
    val icon: ImageVector,
    val labelResId: Int
)

/**
 * 底部导航栏
 */
@Composable
fun BottomNavigationBar(
    currentRoute: String?,
    onNavigate: (String) -> Unit
) {
    val navigationItems = listOf(
        NavigationItem("camera", Icons.Default.CameraAlt, R.string.camera),
        NavigationItem("gallery", Icons.Default.Photo, R.string.gallery),
        NavigationItem("learning", Icons.Default.School, R.string.learning),
        NavigationItem("settings", Icons.Default.Settings, R.string.settings)
    )

    NavigationBar {
        navigationItems.forEach { item ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = item.icon,
                        contentDescription = stringResource(item.labelResId)
                    )
                },
                label = {
                    Text(stringResource(item.labelResId))
                },
                selected = currentRoute == item.route,
                onClick = {
                    onNavigate(item.route)
                }
            )
        }
    }
}
