package com.fsl.app.presentation.learning;

import java.lang.System;

/**
 * 样本收集状态
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b(\b\u0086\b\u0018\u00002\u00020\u0001B\u008f\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0013\u001a\u00020\b\u0012\b\b\u0002\u0010\u0014\u001a\u00020\b\u0012\b\b\u0002\u0010\u0015\u001a\u00020\b\u00a2\u0006\u0002\u0010\u0016J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\t\u0010(\u001a\u00020\u0011H\u00c6\u0003J\t\u0010)\u001a\u00020\bH\u00c6\u0003J\t\u0010*\u001a\u00020\bH\u00c6\u0003J\t\u0010+\u001a\u00020\bH\u00c6\u0003J\u000f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\bH\u00c6\u0003J\t\u0010.\u001a\u00020\nH\u00c6\u0003J\u000b\u0010/\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00100\u001a\u00020\bH\u00c6\u0003J\t\u00101\u001a\u00020\u000eH\u00c6\u0003J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0011H\u00c6\u0003J\u0093\u0001\u00104\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00112\b\b\u0002\u0010\u0013\u001a\u00020\b2\b\b\u0002\u0010\u0014\u001a\u00020\b2\b\b\u0002\u0010\u0015\u001a\u00020\bH\u00c6\u0001J\u0013\u00105\u001a\u00020\b2\b\u00106\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00107\u001a\u00020\nH\u00d6\u0001J\t\u00108\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u001eR\u0011\u0010\f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u001eR\u0011\u0010\u0014\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u001eR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0018R\u0011\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001dR\u0011\u0010\u0015\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001eR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0013\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001e\u00a8\u00069"}, d2 = {"Lcom/fsl/app/presentation/learning/SampleCollectionState;", "", "className", "", "collectedSamples", "", "Lcom/fsl/app/presentation/learning/CollectedSample;", "isCollecting", "", "targetSampleCount", "", "error", "isLearning", "learningProgress", "", "learningStage", "estimatedTimeRemaining", "", "learningStartTime", "useGpuAcceleration", "isLearningCompleted", "learningSuccess", "(Ljava/lang/String;Ljava/util/List;ZILjava/lang/String;ZFLjava/lang/String;JJZZZ)V", "getClassName", "()Ljava/lang/String;", "getCollectedSamples", "()Ljava/util/List;", "getError", "getEstimatedTimeRemaining", "()J", "()Z", "getLearningProgress", "()F", "getLearningStage", "getLearningStartTime", "getLearningSuccess", "getTargetSampleCount", "()I", "getUseGpuAcceleration", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
public final class SampleCollectionState {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String className = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.fsl.app.presentation.learning.CollectedSample> collectedSamples = null;
    private final boolean isCollecting = false;
    private final int targetSampleCount = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    private final boolean isLearning = false;
    private final float learningProgress = 0.0F;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String learningStage = null;
    private final long estimatedTimeRemaining = 0L;
    private final long learningStartTime = 0L;
    private final boolean useGpuAcceleration = false;
    private final boolean isLearningCompleted = false;
    private final boolean learningSuccess = false;
    
    /**
     * 样本收集状态
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.learning.SampleCollectionState copy(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.presentation.learning.CollectedSample> collectedSamples, boolean isCollecting, int targetSampleCount, @org.jetbrains.annotations.Nullable
    java.lang.String error, boolean isLearning, float learningProgress, @org.jetbrains.annotations.NotNull
    java.lang.String learningStage, long estimatedTimeRemaining, long learningStartTime, boolean useGpuAcceleration, boolean isLearningCompleted, boolean learningSuccess) {
        return null;
    }
    
    /**
     * 样本收集状态
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 样本收集状态
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 样本收集状态
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public SampleCollectionState() {
        super();
    }
    
    public SampleCollectionState(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.presentation.learning.CollectedSample> collectedSamples, boolean isCollecting, int targetSampleCount, @org.jetbrains.annotations.Nullable
    java.lang.String error, boolean isLearning, float learningProgress, @org.jetbrains.annotations.NotNull
    java.lang.String learningStage, long estimatedTimeRemaining, long learningStartTime, boolean useGpuAcceleration, boolean isLearningCompleted, boolean learningSuccess) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getClassName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.presentation.learning.CollectedSample> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.presentation.learning.CollectedSample> getCollectedSamples() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean isCollecting() {
        return false;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int getTargetSampleCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isLearning() {
        return false;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float getLearningProgress() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getLearningStage() {
        return null;
    }
    
    public final long component9() {
        return 0L;
    }
    
    public final long getEstimatedTimeRemaining() {
        return 0L;
    }
    
    public final long component10() {
        return 0L;
    }
    
    public final long getLearningStartTime() {
        return 0L;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean getUseGpuAcceleration() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean isLearningCompleted() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean getLearningSuccess() {
        return false;
    }
}