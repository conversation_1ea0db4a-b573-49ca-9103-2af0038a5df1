/**
 * 图像处理用例
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.usecase

import android.graphics.Bitmap
import androidx.camera.core.ImageProxy
import com.fsl.app.domain.repository.IImageProcessingRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ImageProcessingUseCase @Inject constructor(
    private val imageProcessingRepository: IImageProcessingRepository
) {
    
    /**
     * 预处理图像
     */
    fun preprocessImage(bitmap: Bitmap): Bitmap {
        return imageProcessingRepository.preprocessImage(bitmap)
    }
    
    /**
     * 预处理ImageProxy
     */
    fun preprocessImageProxy(imageProxy: ImageProxy): Bitmap {
        return imageProcessingRepository.preprocessImageProxy(imageProxy)
    }
    
    /**
     * 图像增强
     */
    fun augmentImage(bitmap: Bitmap): List<Bitmap> {
        return imageProcessingRepository.augmentImage(bitmap)
    }
    
    /**
     * 调整图像大小
     */
    fun resizeImage(bitmap: Bitmap, width: Int, height: Int): Bitmap {
        return imageProcessingRepository.resizeImage(bitmap, width, height)
    }
    
    /**
     * 图像归一化
     */
    fun normalizeImage(bitmap: Bitmap): FloatArray {
        return imageProcessingRepository.normalizeImage(bitmap)
    }
}
