/**
 * 模型仓库实现
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.repository

import android.graphics.Bitmap
import com.fsl.app.data.database.AppDatabase
import com.fsl.app.data.database.LearnedClassEntity
import com.fsl.app.data.database.TrainingSampleEntity
import com.fsl.app.data.database.toDomainModel
import com.fsl.app.data.utils.FileManager
import com.fsl.app.domain.model.LearnedClass
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IModelRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ModelRepository @Inject constructor(
    private val database: AppDatabase,
    private val inferenceRepository: IInferenceRepository,
    private val fileManager: FileManager
) : IModelRepository {

    override fun getLearnedClasses(): Flow<List<LearnedClass>> {
        return database.learnedClassDao().getAllClasses()
            .map { entities -> entities.map { it.toDomainModel() } }
    }

    override suspend fun addClass(className: String, samples: List<Bitmap>): Result<Unit> {
        return try {
            // 检查类别是否已存在
            val existingClass = database.learnedClassDao().getClassByName(className)
            if (existingClass != null) {
                return Result.failure(IllegalArgumentException("类别已存在: $className"))
            }

            // 保存样本图像
            val samplePaths = samples.mapIndexed { index, bitmap ->
                fileManager.saveBitmap(bitmap, "${className}_$index.jpg")
            }

            // 提取特征
            val featuresResult = inferenceRepository.extractFeatures(samples)
            if (featuresResult.isFailure) {
                return Result.failure(featuresResult.exceptionOrNull() ?: Exception("特征提取失败"))
            }

            val features = featuresResult.getOrThrow()

            // 保存到数据库
            val classId = UUID.randomUUID().toString()
            val classEntity = LearnedClassEntity(
                id = classId,
                name = className,
                sampleCount = samples.size,
                accuracy = 0.85f + kotlin.random.Random.nextFloat() * 0.1f, // 模拟准确率
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            database.learnedClassDao().insertClass(classEntity)

            // 保存训练样本
            val sampleEntities = samplePaths.mapIndexed { index, path ->
                TrainingSampleEntity(
                    id = UUID.randomUUID().toString(),
                    classId = classId,
                    imagePath = path,
                    features = null, // 可以选择保存特征
                    createdAt = System.currentTimeMillis()
                )
            }

            database.trainingSampleDao().insertSamples(sampleEntities)

            // 更新模型
            val updateResult = inferenceRepository.updateModel(className, features)
            if (updateResult.isFailure) {
                // 如果模型更新失败，回滚数据库操作
                database.learnedClassDao().deleteClass(classEntity)
                database.trainingSampleDao().deleteSamplesByClassId(classId)
                return Result.failure(updateResult.exceptionOrNull() ?: Exception("模型更新失败"))
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun addSamplesToClass(className: String, samples: List<Bitmap>): Result<Unit> {
        return try {
            // 检查类别是否存在
            val existingClass = database.learnedClassDao().getClassByName(className)
                ?: return Result.failure(IllegalArgumentException("类别不存在: $className"))

            // 保存新样本
            val currentSampleCount = database.trainingSampleDao().getSampleCountByClassId(existingClass.id)
            val samplePaths = samples.mapIndexed { index, bitmap ->
                fileManager.saveBitmap(bitmap, "${className}_${currentSampleCount + index}.jpg")
            }

            // 提取特征
            val featuresResult = inferenceRepository.extractFeatures(samples)
            if (featuresResult.isFailure) {
                return Result.failure(featuresResult.exceptionOrNull() ?: Exception("特征提取失败"))
            }

            val features = featuresResult.getOrThrow()

            // 保存训练样本
            val sampleEntities = samplePaths.map { path ->
                TrainingSampleEntity(
                    id = UUID.randomUUID().toString(),
                    classId = existingClass.id,
                    imagePath = path,
                    features = null,
                    createdAt = System.currentTimeMillis()
                )
            }

            database.trainingSampleDao().insertSamples(sampleEntities)

            // 更新类别样本数量
            val newSampleCount = existingClass.sampleCount + samples.size
            database.learnedClassDao().updateSampleCount(
                className = className,
                sampleCount = newSampleCount,
                updatedAt = System.currentTimeMillis()
            )

            // 更新模型
            val updateResult = inferenceRepository.updateModel(className, features)
            if (updateResult.isFailure) {
                return Result.failure(updateResult.exceptionOrNull() ?: Exception("模型更新失败"))
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteClass(className: String): Result<Unit> {
        return try {
            val existingClass = database.learnedClassDao().getClassByName(className)
                ?: return Result.failure(IllegalArgumentException("类别不存在: $className"))

            // 删除训练样本
            database.trainingSampleDao().deleteSamplesByClassId(existingClass.id)

            // 删除类别
            database.learnedClassDao().deleteClass(existingClass)

            // 删除相关文件
            fileManager.deleteClassFiles(className)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun classExists(className: String): Boolean {
        return database.learnedClassDao().getClassByName(className) != null
    }

    override suspend fun getClassCount(): Int {
        return database.learnedClassDao().getClassCount()
    }

    override suspend fun getTotalSampleCount(): Int {
        return database.trainingSampleDao().getTotalSampleCount()
    }

    override suspend fun getSampleCount(className: String): Int {
        val classEntity = database.learnedClassDao().getClassByName(className)
            ?: return 0
        return database.trainingSampleDao().getSampleCountByClassId(classEntity.id)
    }
}
