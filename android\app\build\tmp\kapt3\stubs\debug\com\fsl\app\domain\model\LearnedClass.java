package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 学习类别数据类
 *
 * @property id 类别唯一标识符
 * @property name 类别名称
 * @property description 类别描述
 * @property sampleCount 样本数量
 * @property prototypeFeatures 原型特征向量
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0014\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0086\b\u0018\u00002\u00020\u0001BK\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003JQ\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u0096\u0002J\u0006\u0010$\u001a\u00020\bJ\u0006\u0010%\u001a\u00020\"J\b\u0010&\u001a\u00020\bH\u0016J\t\u0010\'\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000f\u00a8\u0006("}, d2 = {"Lcom/fsl/app/domain/model/LearnedClass;", "", "id", "", "name", "", "description", "sampleCount", "", "prototypeFeatures", "", "createdAt", "updatedAt", "(JLjava/lang/String;Ljava/lang/String;I[FJJ)V", "getCreatedAt", "()J", "getDescription", "()Ljava/lang/String;", "getId", "getName", "getPrototypeFeatures", "()[F", "getSampleCount", "()I", "getUpdatedAt", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "getFeatureDimension", "hasValidPrototype", "hashCode", "toString", "app_debug"})
public final class LearnedClass {
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String description = null;
    private final int sampleCount = 0;
    @org.jetbrains.annotations.Nullable
    private final float[] prototypeFeatures = null;
    private final long createdAt = 0L;
    private final long updatedAt = 0L;
    
    /**
     * 学习类别数据类
     *
     * @property id 类别唯一标识符
     * @property name 类别名称
     * @property description 类别描述
     * @property sampleCount 样本数量
     * @property prototypeFeatures 原型特征向量
     * @property createdAt 创建时间戳
     * @property updatedAt 更新时间戳
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.LearnedClass copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String description, int sampleCount, @org.jetbrains.annotations.Nullable
    float[] prototypeFeatures, long createdAt, long updatedAt) {
        return null;
    }
    
    /**
     * 学习类别数据类
     *
     * @property id 类别唯一标识符
     * @property name 类别名称
     * @property description 类别描述
     * @property sampleCount 样本数量
     * @property prototypeFeatures 原型特征向量
     * @property createdAt 创建时间戳
     * @property updatedAt 更新时间戳
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public LearnedClass(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String description, int sampleCount, @org.jetbrains.annotations.Nullable
    float[] prototypeFeatures, long createdAt, long updatedAt) {
        super();
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDescription() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int getSampleCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final float[] component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final float[] getPrototypeFeatures() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final long getUpdatedAt() {
        return 0L;
    }
    
    /**
     * 重写equals方法，排除prototypeFeatures的比较
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 重写hashCode方法，排除prototypeFeatures
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 检查是否有有效的原型特征
     */
    public final boolean hasValidPrototype() {
        return false;
    }
    
    /**
     * 获取特征向量维度
     */
    public final int getFeatureDimension() {
        return 0;
    }
}