ninja: Entering directory `F:\geek\fsl\android\app\.cxx\Debug\6w13315g\armeabi-v7a'
[1/8] Building CXX object CMakeFiles/fsl_native.dir/image_processor.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[2/8] Building CXX object CMakeFiles/fsl_native.dir/jni_interface.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[3/8] Building CXX object CMakeFiles/fsl_native.dir/feature_extractor.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[4/8] Building CXX object CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp:177:57: warning: implicit conversion from 'int' to 'float' changes value from 2147483647 to 2147483648 [-Wimplicit-const-int-float-conversion]
  177 |         conv1Weights[i] = (static_cast<float>(rand()) / RAND_MAX - 0.5f) * 0.1f;
      |                                                       ~ ^~~~~~~~
D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/stdlib.h:132:18: note: expanded from macro 'RAND_MAX'
  132 | #define RAND_MAX 0x7fffffff
      |                  ^~~~~~~~~~
2 warnings generated.
[5/8] Building CXX object CMakeFiles/fsl_native.dir/prototypical_network.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[6/8] Building CXX object CMakeFiles/fsl_native.dir/object_tracker.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[7/8] Building CXX object CMakeFiles/fsl_native.dir/fsl_inference.cpp.o
In file included from <built-in>:468:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 29
      |         ^
<built-in>:459:9: note: previous definition is here
  459 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
F:/geek/fsl/android/app/src/main/cpp/fsl_inference.cpp:106:57: warning: format specifies type 'long' but the argument has type 'rep' (aka 'long long') [-Wformat]
  105 |         LOGI("Classification result: %s (%.2f%%) in %ldms",
      |                                                     ~~~
      |                                                     %lld
  106 |              result.first.c_str(), result.second * 100, duration.count());
      |                                                         ^~~~~~~~~~~~~~~~
F:/geek/fsl/android/app/src/main/cpp/fsl_inference.cpp:23:66: note: expanded from macro 'LOGI'
   23 | #define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
      |                                                                  ^~~~~~~~~~~
2 warnings generated.
[8/8] Linking CXX shared library F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.so
