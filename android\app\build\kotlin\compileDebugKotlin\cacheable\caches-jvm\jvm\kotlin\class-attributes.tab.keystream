com.fsl.app.FSLApplicationcom.fsl.app.MainActivity%com.fsl.app.data.database.AppDatabase/com.fsl.app.data.database.AppDatabase.Companion)com.fsl.app.data.database.LearnedClassDao+com.fsl.app.data.database.TrainingSampleDao,com.fsl.app.data.database.LearnedClassEntity.com.fsl.app.data.database.TrainingSampleEntity0com.fsl.app.data.inference.NativeInferenceEngine:com.fsl.app.data.inference.NativeInferenceEngine.Companion1com.fsl.app.data.inference.PyTorchInferenceEngine;com.fsl.app.data.inference.PyTorchInferenceEngine.Companion1com.fsl.app.data.repository.GalleryRepositoryImpl;com.fsl.app.data.repository.GalleryRepositoryImpl.Companion?com.fsl.app.data.repository.GalleryRepositoryImpl.ImageMetadata5com.fsl.app.data.repository.ImageProcessingRepository+com.fsl.app.data.repository.ModelRepository!com.fsl.app.data.utils.AssetUtils"com.fsl.app.data.utils.FileManager,com.fsl.app.data.utils.FileManager.Companion"com.fsl.app.data.utils.StorageInfo%com.fsl.app.data.utils.ImageProcessor/com.fsl.app.data.utils.ImageProcessor.Companioncom.fsl.app.di.AppModulecom.fsl.app.di.RepositoryModule-com.fsl.app.domain.model.ClassificationResult%com.fsl.app.domain.model.QualityLevel2com.fsl.app.domain.model.BatchClassificationResult,com.fsl.app.domain.model.ClassificationStats%com.fsl.app.domain.model.GalleryImage%com.fsl.app.domain.model.LearnedClass/com.fsl.app.domain.repository.GalleryRepository8com.fsl.app.domain.repository.IImageProcessingRepository2com.fsl.app.domain.repository.IInferenceRepository.com.fsl.app.domain.repository.IModelRepository0com.fsl.app.domain.usecase.ClassificationUseCase1com.fsl.app.domain.usecase.ImageProcessingUseCase5com.fsl.app.domain.usecase.IncrementalLearningUseCase(com.fsl.app.domain.usecase.LearningStats+com.fsl.app.domain.usecase.ValidationResult1com.fsl.app.domain.usecase.ValidationResult.Valid3com.fsl.app.domain.usecase.ValidationResult.Invalid3com.fsl.app.domain.usecase.ValidationResult.Warning1com.fsl.app.domain.usecase.ModelManagementUseCase$com.fsl.app.presentation.MainUiState&com.fsl.app.presentation.MainViewModel+com.fsl.app.presentation.camera.CameraState0com.fsl.app.presentation.camera.CameraState.Idle6com.fsl.app.presentation.camera.CameraState.Processing1com.fsl.app.presentation.camera.CameraState.Error/com.fsl.app.presentation.camera.CameraViewModel,com.fsl.app.presentation.camera.CameraConfig-com.fsl.app.presentation.gallery.GalleryState-com.fsl.app.presentation.gallery.GalleryStats1com.fsl.app.presentation.gallery.GalleryViewModel/com.fsl.app.presentation.learning.LearningState.com.fsl.app.presentation.learning.LearnedClass3com.fsl.app.presentation.learning.LearningViewModel7com.fsl.app.presentation.learning.SampleCollectionState1com.fsl.app.presentation.learning.CollectedSample;com.fsl.app.presentation.learning.SampleCollectionViewModel3com.fsl.app.presentation.settings.SettingsViewModel(com.fsl.app.ui.components.NavigationItem)com.fsl.app.ui.components.DetectionResultKcom.fsl.app.di.AppModule_ProvideNativeInferenceEngineFactory.InstanceHolder<com.fsl.app.di.AppModule_ProvideNativeInferenceEngineFactory'com.fsl.app.domain.model.TrackingResult7com.fsl.app.domain.model.TrackingResult.ConfidenceLevel)com.fsl.app.domain.model.PixelBoundingBox                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 