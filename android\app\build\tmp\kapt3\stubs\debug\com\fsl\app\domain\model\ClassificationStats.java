package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 分类统计信息
 *
 * @property totalClassifications 总分类次数
 * @property successfulClassifications 成功分类次数
 * @property averageConfidence 平均置信度
 * @property averageInferenceTime 平均推理时间
 * @property classDistribution 类别分布统计
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0007\b\u0086\b\u0018\u00002\u00020\u0001B9\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\t\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0006H\u00c6\u0003J\u0015\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\tH\u00c6\u0003JG\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\u0014\b\u0002\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\tH\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\u001d\u001a\u00020\u0003J\b\u0010\u001e\u001a\u0004\u0018\u00010\nJ\u0006\u0010\u001f\u001a\u00020\u0006J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001J\t\u0010!\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u001d\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012\u00a8\u0006\""}, d2 = {"Lcom/fsl/app/domain/model/ClassificationStats;", "", "totalClassifications", "", "successfulClassifications", "averageConfidence", "", "averageInferenceTime", "classDistribution", "", "", "(IIFFLjava/util/Map;)V", "getAverageConfidence", "()F", "getAverageInferenceTime", "getClassDistribution", "()Ljava/util/Map;", "getSuccessfulClassifications", "()I", "getTotalClassifications", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "getClassDiversity", "getMostCommonClass", "getSuccessRate", "hashCode", "toString", "app_debug"})
public final class ClassificationStats {
    private final int totalClassifications = 0;
    private final int successfulClassifications = 0;
    private final float averageConfidence = 0.0F;
    private final float averageInferenceTime = 0.0F;
    @org.jetbrains.annotations.NotNull
    private final java.util.Map<java.lang.String, java.lang.Integer> classDistribution = null;
    
    /**
     * 分类统计信息
     *
     * @property totalClassifications 总分类次数
     * @property successfulClassifications 成功分类次数
     * @property averageConfidence 平均置信度
     * @property averageInferenceTime 平均推理时间
     * @property classDistribution 类别分布统计
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.ClassificationStats copy(int totalClassifications, int successfulClassifications, float averageConfidence, float averageInferenceTime, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.Integer> classDistribution) {
        return null;
    }
    
    /**
     * 分类统计信息
     *
     * @property totalClassifications 总分类次数
     * @property successfulClassifications 成功分类次数
     * @property averageConfidence 平均置信度
     * @property averageInferenceTime 平均推理时间
     * @property classDistribution 类别分布统计
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 分类统计信息
     *
     * @property totalClassifications 总分类次数
     * @property successfulClassifications 成功分类次数
     * @property averageConfidence 平均置信度
     * @property averageInferenceTime 平均推理时间
     * @property classDistribution 类别分布统计
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 分类统计信息
     *
     * @property totalClassifications 总分类次数
     * @property successfulClassifications 成功分类次数
     * @property averageConfidence 平均置信度
     * @property averageInferenceTime 平均推理时间
     * @property classDistribution 类别分布统计
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ClassificationStats(int totalClassifications, int successfulClassifications, float averageConfidence, float averageInferenceTime, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.Integer> classDistribution) {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int getTotalClassifications() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int getSuccessfulClassifications() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getAverageConfidence() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float getAverageInferenceTime() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.Integer> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.Integer> getClassDistribution() {
        return null;
    }
    
    /**
     * 获取成功率
     */
    public final float getSuccessRate() {
        return 0.0F;
    }
    
    /**
     * 获取最常见的类别
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getMostCommonClass() {
        return null;
    }
    
    /**
     * 获取类别多样性（不同类别的数量）
     */
    public final int getClassDiversity() {
        return 0;
    }
}