package com.fsl.app;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = FSLApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface FSLApplication_GeneratedInjector {
  void injectFSLApplication(FSLApplication fSLApplication);
}
