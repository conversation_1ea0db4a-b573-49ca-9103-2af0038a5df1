/**
 * 图库仓库接口
 *
 * 定义图库相关的数据操作接口
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.repository

import com.fsl.app.domain.model.GalleryImage
import kotlinx.coroutines.flow.Flow

/**
 * 图库仓库接口
 */
interface GalleryRepository {
    
    /**
     * 获取所有图库图像
     */
    suspend fun getAllImages(): Result<List<GalleryImage>>
    
    /**
     * 获取图库图像流
     */
    fun getImagesFlow(): Flow<List<GalleryImage>>
    
    /**
     * 根据分类标签获取图像
     */
    suspend fun getImagesByLabel(label: String): Result<List<GalleryImage>>
    
    /**
     * 获取学习样本图像
     */
    suspend fun getLearnedImages(): Result<List<GalleryImage>>
    
    /**
     * 删除图像
     */
    suspend fun deleteImage(image: GalleryImage): Result<Unit>
    
    /**
     * 标记图像为学习样本
     */
    suspend fun markAsLearned(image: GalleryImage, className: String): Result<Unit>
    
    /**
     * 刷新图库
     */
    suspend fun refreshGallery(): Result<Unit>
    
    /**
     * 获取图库统计信息
     */
    suspend fun getGalleryStats(): Result<Map<String, Int>>
}
