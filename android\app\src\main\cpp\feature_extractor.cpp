/**
 * 特征提取器实现
 * 
 * 图像特征提取的C++实现
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "include/feature_extractor.h"
#include <cmath>
#include <random>
#include <algorithm>
#include <android/log.h>

#define LOG_TAG "FeatureExtractor"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

namespace fsl {

FeatureExtractor::FeatureExtractor() : initialized_(false), featureDim_(512) {}

FeatureExtractor::~FeatureExtractor() = default;

bool FeatureExtractor::initialize() {
    LOGI("Initializing feature extractor...");
    
    try {
        // 定义网络架构
        // Conv1: 3 -> 64, 3x3, stride=1, padding=1
        convLayers_.emplace_back(3, 64, 3, 1, 1);
        
        // Conv2: 64 -> 128, 3x3, stride=1, padding=1  
        convLayers_.emplace_back(64, 128, 3, 1, 1);
        
        // Conv3: 128 -> 256, 3x3, stride=1, padding=1
        convLayers_.emplace_back(128, 256, 3, 1, 1);
        
        // Conv4: 256 -> 512, 3x3, stride=1, padding=1
        convLayers_.emplace_back(256, 512, 3, 1, 1);
        
        // 初始化权重
        initializeWeights();
        
        initialized_ = true;
        LOGI("Feature extractor initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        LOGE("Exception during initialization: %s", e.what());
        return false;
    }
}

FeatureVector FeatureExtractor::extractFeatures(const float* imageData, int width, int height, int channels) {
    if (!initialized_) {
        LOGE("Feature extractor not initialized");
        return FeatureVector();
    }
    
    if (width != 224 || height != 224 || channels != 3) {
        LOGE("Invalid input dimensions: %dx%dx%d (expected 224x224x3)", width, height, channels);
        return FeatureVector();
    }
    
    try {
        // 输入数据大小
        int currentWidth = width;
        int currentHeight = height;
        int currentChannels = channels;
        
        // 分配中间特征图内存
        std::vector<float> input(imageData, imageData + width * height * channels);
        std::vector<float> output;
        
        // 逐层前向传播
        for (size_t layerIdx = 0; layerIdx < convLayers_.size(); ++layerIdx) {
            const auto& layer = convLayers_[layerIdx];
            
            // 计算输出尺寸
            int outputWidth = calculateOutputSize(currentWidth, layer.kernelSize, layer.stride, layer.padding);
            int outputHeight = calculateOutputSize(currentHeight, layer.kernelSize, layer.stride, layer.padding);
            int outputChannels = layer.outputChannels;
            
            // 分配输出内存
            output.resize(outputWidth * outputHeight * outputChannels);
            
            // 卷积操作
            convolution(input.data(), currentWidth, currentHeight, currentChannels,
                       layer, output.data(), outputWidth, outputHeight);
            
            // ReLU激活
            relu(output.data(), output.size());
            
            // 每两层进行一次最大池化
            if (layerIdx % 2 == 1) {
                std::vector<float> pooledOutput;
                int pooledWidth = outputWidth / 2;
                int pooledHeight = outputHeight / 2;
                
                pooledOutput.resize(pooledWidth * pooledHeight * outputChannels);
                
                maxPooling(output.data(), outputWidth, outputHeight, outputChannels,
                          2, 2, pooledOutput.data(), pooledWidth, pooledHeight);
                
                output = std::move(pooledOutput);
                outputWidth = pooledWidth;
                outputHeight = pooledHeight;
            }
            
            // 更新当前尺寸
            currentWidth = outputWidth;
            currentHeight = outputHeight;
            currentChannels = outputChannels;
            
            // 输入变为输出
            input = output;
        }
        
        // 全局平均池化
        FeatureVector features(featureDim_);
        globalAveragePooling(output.data(), currentWidth, currentHeight, currentChannels, features.data());
        
        LOGI("Extracted features: %dx%dx%d -> %d", width, height, channels, featureDim_);
        return features;
        
    } catch (const std::exception& e) {
        LOGE("Exception during feature extraction: %s", e.what());
        return FeatureVector();
    }
}

void FeatureExtractor::convolution(const float* input, int inputWidth, int inputHeight, int inputChannels,
                                  const ConvLayerParams& params, float* output, int outputWidth, int outputHeight) {
    
    int kernelSize = params.kernelSize;
    int stride = params.stride;
    int padding = params.padding;
    int outputChannels = params.outputChannels;
    
    // 简化的卷积实现（实际应用中需要优化）
    for (int oc = 0; oc < outputChannels; ++oc) {
        for (int oh = 0; oh < outputHeight; ++oh) {
            for (int ow = 0; ow < outputWidth; ++ow) {
                float sum = 0.0f;
                
                // 卷积核计算
                for (int ic = 0; ic < inputChannels; ++ic) {
                    for (int kh = 0; kh < kernelSize; ++kh) {
                        for (int kw = 0; kw < kernelSize; ++kw) {
                            int ih = oh * stride - padding + kh;
                            int iw = ow * stride - padding + kw;
                            
                            // 边界检查
                            if (ih >= 0 && ih < inputHeight && iw >= 0 && iw < inputWidth) {
                                int inputIdx = ic * inputHeight * inputWidth + ih * inputWidth + iw;
                                int weightIdx = oc * inputChannels * kernelSize * kernelSize + 
                                               ic * kernelSize * kernelSize + kh * kernelSize + kw;
                                
                                if (weightIdx < static_cast<int>(params.weights.size())) {
                                    sum += input[inputIdx] * params.weights[weightIdx];
                                }
                            }
                        }
                    }
                }
                
                // 添加偏置
                if (oc < static_cast<int>(params.bias.size())) {
                    sum += params.bias[oc];
                }
                
                int outputIdx = oc * outputHeight * outputWidth + oh * outputWidth + ow;
                output[outputIdx] = sum;
            }
        }
    }
}

void FeatureExtractor::relu(float* data, int size) {
    for (int i = 0; i < size; ++i) {
        data[i] = std::max(0.0f, data[i]);
    }
}

void FeatureExtractor::maxPooling(const float* input, int inputWidth, int inputHeight, int channels,
                                 int poolSize, int stride, float* output, int outputWidth, int outputHeight) {
    
    for (int c = 0; c < channels; ++c) {
        for (int oh = 0; oh < outputHeight; ++oh) {
            for (int ow = 0; ow < outputWidth; ++ow) {
                float maxVal = -std::numeric_limits<float>::infinity();
                
                // 池化窗口
                for (int ph = 0; ph < poolSize; ++ph) {
                    for (int pw = 0; pw < poolSize; ++pw) {
                        int ih = oh * stride + ph;
                        int iw = ow * stride + pw;
                        
                        if (ih < inputHeight && iw < inputWidth) {
                            int inputIdx = c * inputHeight * inputWidth + ih * inputWidth + iw;
                            maxVal = std::max(maxVal, input[inputIdx]);
                        }
                    }
                }
                
                int outputIdx = c * outputHeight * outputWidth + oh * outputWidth + ow;
                output[outputIdx] = maxVal;
            }
        }
    }
}

void FeatureExtractor::globalAveragePooling(const float* input, int width, int height, int channels, float* output) {
    for (int c = 0; c < channels; ++c) {
        float sum = 0.0f;
        int spatialSize = width * height;
        
        for (int h = 0; h < height; ++h) {
            for (int w = 0; w < width; ++w) {
                int idx = c * height * width + h * width + w;
                sum += input[idx];
            }
        }
        
        output[c] = sum / static_cast<float>(spatialSize);
    }
}

void FeatureExtractor::batchNormalization(float* data, int size, float mean, float variance, float gamma, float beta) {
    float epsilon = 1e-5f;
    float invStd = 1.0f / std::sqrt(variance + epsilon);
    
    for (int i = 0; i < size; ++i) {
        data[i] = gamma * (data[i] - mean) * invStd + beta;
    }
}

void FeatureExtractor::initializeWeights() {
    std::random_device rd;
    std::mt19937 gen(rd());
    
    for (auto& layer : convLayers_) {
        int fanIn = layer.inputChannels * layer.kernelSize * layer.kernelSize;
        int fanOut = layer.outputChannels * layer.kernelSize * layer.kernelSize;
        
        // 权重初始化
        int weightCount = layer.outputChannels * layer.inputChannels * layer.kernelSize * layer.kernelSize;
        layer.weights.resize(weightCount);
        xavierInitialization(layer.weights, fanIn, fanOut);
        
        // 偏置初始化为0
        layer.bias.resize(layer.outputChannels, 0.0f);
    }
    
    LOGI("Initialized weights for %zu layers", convLayers_.size());
}

void FeatureExtractor::xavierInitialization(std::vector<float>& weights, int fanIn, int fanOut) {
    std::random_device rd;
    std::mt19937 gen(rd());
    
    float limit = std::sqrt(6.0f / (fanIn + fanOut));
    std::uniform_real_distribution<float> dist(-limit, limit);
    
    for (float& weight : weights) {
        weight = dist(gen);
    }
}

int FeatureExtractor::calculateOutputSize(int inputSize, int kernelSize, int stride, int padding) {
    return (inputSize + 2 * padding - kernelSize) / stride + 1;
}

void FeatureExtractor::addPadding(const float* input, int inputWidth, int inputHeight, int channels,
                                 int padding, float* output, int outputWidth, int outputHeight) {
    
    // 初始化输出为0
    std::fill(output, output + outputWidth * outputHeight * channels, 0.0f);
    
    // 复制输入数据到中心区域
    for (int c = 0; c < channels; ++c) {
        for (int h = 0; h < inputHeight; ++h) {
            for (int w = 0; w < inputWidth; ++w) {
                int inputIdx = c * inputHeight * inputWidth + h * inputWidth + w;
                int outputIdx = c * outputHeight * outputWidth + (h + padding) * outputWidth + (w + padding);
                output[outputIdx] = input[inputIdx];
            }
        }
    }
}

} // namespace fsl
