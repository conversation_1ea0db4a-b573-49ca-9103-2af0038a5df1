/**
 * 特征提取器头文件
 * 
 * 图像特征提取的C++实现
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef FEATURE_EXTRACTOR_H
#define FEATURE_EXTRACTOR_H

#include <vector>
#include <memory>

namespace fsl {

using FeatureVector = std::vector<float>;

/**
 * 卷积层参数
 */
struct ConvLayerParams {
    int inputChannels;
    int outputChannels;
    int kernelSize;
    int stride;
    int padding;
    std::vector<float> weights;
    std::vector<float> bias;
    
    ConvLayerParams(int in_ch, int out_ch, int k_size, int s = 1, int p = 0)
        : inputChannels(in_ch), outputChannels(out_ch), kernelSize(k_size), 
          stride(s), padding(p) {}
};

/**
 * 特征提取器类
 * 
 * 实现简化的CNN特征提取网络
 */
class FeatureExtractor {
public:
    FeatureExtractor();
    ~FeatureExtractor();
    
    /**
     * 初始化特征提取器
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * 提取图像特征
     * @param imageData 图像数据 (RGB, 224x224x3)
     * @param width 图像宽度
     * @param height 图像高度
     * @param channels 图像通道数
     * @return 特征向量
     */
    FeatureVector extractFeatures(const float* imageData, int width, int height, int channels = 3);
    
    /**
     * 获取输出特征维度
     * @return 特征维度
     */
    int getFeatureDim() const { return featureDim_; }
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return initialized_; }

private:
    bool initialized_;
    int featureDim_;
    
    // 网络层参数
    std::vector<ConvLayerParams> convLayers_;
    
    /**
     * 卷积操作
     * @param input 输入特征图
     * @param inputWidth 输入宽度
     * @param inputHeight 输入高度
     * @param inputChannels 输入通道数
     * @param params 卷积层参数
     * @param output 输出特征图
     * @param outputWidth 输出宽度
     * @param outputHeight 输出高度
     */
    void convolution(const float* input, int inputWidth, int inputHeight, int inputChannels,
                    const ConvLayerParams& params, float* output, int outputWidth, int outputHeight);
    
    /**
     * ReLU激活函数
     * @param data 输入数据
     * @param size 数据大小
     */
    void relu(float* data, int size);
    
    /**
     * 最大池化
     * @param input 输入特征图
     * @param inputWidth 输入宽度
     * @param inputHeight 输入高度
     * @param channels 通道数
     * @param poolSize 池化窗口大小
     * @param stride 步长
     * @param output 输出特征图
     * @param outputWidth 输出宽度
     * @param outputHeight 输出高度
     */
    void maxPooling(const float* input, int inputWidth, int inputHeight, int channels,
                   int poolSize, int stride, float* output, int outputWidth, int outputHeight);
    
    /**
     * 全局平均池化
     * @param input 输入特征图
     * @param width 特征图宽度
     * @param height 特征图高度
     * @param channels 通道数
     * @param output 输出向量
     */
    void globalAveragePooling(const float* input, int width, int height, int channels, float* output);
    
    /**
     * 批归一化
     * @param data 输入数据
     * @param size 数据大小
     * @param mean 均值
     * @param variance 方差
     * @param gamma 缩放参数
     * @param beta 偏移参数
     */
    void batchNormalization(float* data, int size, float mean, float variance, float gamma, float beta);
    
    /**
     * 初始化网络权重
     */
    void initializeWeights();
    
    /**
     * Xavier权重初始化
     * @param weights 权重向量
     * @param fanIn 输入神经元数量
     * @param fanOut 输出神经元数量
     */
    void xavierInitialization(std::vector<float>& weights, int fanIn, int fanOut);
    
    /**
     * 计算卷积输出尺寸
     * @param inputSize 输入尺寸
     * @param kernelSize 卷积核尺寸
     * @param stride 步长
     * @param padding 填充
     * @return 输出尺寸
     */
    int calculateOutputSize(int inputSize, int kernelSize, int stride, int padding);
    
    /**
     * 添加填充
     * @param input 输入数据
     * @param inputWidth 输入宽度
     * @param inputHeight 输入高度
     * @param channels 通道数
     * @param padding 填充大小
     * @param output 输出数据
     * @param outputWidth 输出宽度
     * @param outputHeight 输出高度
     */
    void addPadding(const float* input, int inputWidth, int inputHeight, int channels,
                   int padding, float* output, int outputWidth, int outputHeight);
};

} // namespace fsl

#endif // FEATURE_EXTRACTOR_H
