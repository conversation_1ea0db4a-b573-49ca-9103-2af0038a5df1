/**
 * 相机预览组件
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.components

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import androidx.camera.core.ImageProxy
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import kotlinx.coroutines.delay
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.random.Random

/**
 * 相机预览组件
 *
 * @param modifier 修饰符
 * @param onImageCaptured 图像捕获回调（实时模式）
 * @param onManualCapture 手动拍照回调
 * @param useMockCamera 是否使用模拟相机
 * @param isFrontCamera 是否使用前置相机
 * @param captureMode 捕获模式：continuous(连续) 或 manual(手动)
 */
@Composable
fun CameraPreview(
    modifier: Modifier = Modifier,
    onImageCaptured: ((ImageProxy?) -> Unit)? = null,
    onManualCapture: ((ImageProxy?) -> Unit)? = null,
    useMockCamera: Boolean = false,
    isFrontCamera: Boolean = false,
    captureMode: String = "continuous" // "continuous" 或 "manual"
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    // 移除isSimulating变量，避免任何闪烁
    var cameraError by remember { mutableStateOf<String?>(null) }
    var shouldUseMockCamera by remember { mutableStateOf(useMockCamera) }

    // 手动拍照触发器
    var manualCaptureTrigger by remember { mutableStateOf(0) }

    // 如果真实相机失败，自动切换到模拟相机
    if (shouldUseMockCamera || cameraError != null) {
        // 真正的流式推理 - 连续不间断
        LaunchedEffect(onImageCaptured) {
            if (onImageCaptured != null) {
                while (true) {
                    // 连续推理，不暂停画面
                    onImageCaptured(null)

                    // 短暂延迟以控制推理频率，但不影响预览
                    delay(500) // 每500ms推理一次，保持流畅
                }
            }
        }

        // 显示模拟相机界面
        MockCameraPreview(modifier, cameraError, isFrontCamera)
    } else {
        // 真实相机预览
        RealCameraPreview(
            modifier = modifier,
            onImageCaptured = onImageCaptured,
            onManualCapture = onManualCapture,
            context = context,
            lifecycleOwner = lifecycleOwner,
            isFrontCamera = isFrontCamera,
            captureMode = captureMode,
            onError = { error ->
                cameraError = error
                shouldUseMockCamera = true
            }
        )
    }
}

/**
 * 真实相机预览
 */
@Composable
private fun RealCameraPreview(
    modifier: Modifier,
    onImageCaptured: ((ImageProxy?) -> Unit)?,
    onManualCapture: ((ImageProxy?) -> Unit)?,
    context: Context,
    lifecycleOwner: androidx.lifecycle.LifecycleOwner,
    isFrontCamera: Boolean,
    captureMode: String,
    onError: (String) -> Unit
) {
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }

    // 当相机方向改变时重新初始化相机
    LaunchedEffect(isFrontCamera) {
        android.util.Log.i("CameraPreview", "相机切换: ${if (isFrontCamera) "前置" else "后置"}")
    }

    AndroidView(
        factory = { ctx ->
            val previewView = PreviewView(ctx)
            val cameraProviderFuture = ProcessCameraProvider.getInstance(ctx)

            cameraProviderFuture.addListener({
                val cameraProvider = cameraProviderFuture.get()

                val preview = Preview.Builder().build().also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

                val imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build()
                    .also {
                        it.setAnalyzer(cameraExecutor) { imageProxy ->
                            // 根据捕获模式决定是否调用回调
                            when (captureMode) {
                                "continuous" -> {
                                    // 连续模式：持续调用回调（用于首页实时推理）
                                    onImageCaptured?.invoke(imageProxy)
                                }
                                "manual" -> {
                                    // 手动模式：只在需要时调用（用于样本收集）
                                    // 这里暂存最新的帧，等待手动触发
                                    // 注意：这里需要配合外部的触发机制
                                    onImageCaptured?.invoke(imageProxy)
                                }
                                else -> {
                                    // 默认连续模式
                                    onImageCaptured?.invoke(imageProxy)
                                }
                            }
                        }
                    }

                // 根据状态选择相机
                val cameraSelector = if (isFrontCamera) {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                } else {
                    CameraSelector.DEFAULT_BACK_CAMERA
                }

                try {
                    cameraProvider.unbindAll()
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageAnalyzer
                    )
                    android.util.Log.i("CameraPreview", "相机绑定成功: ${if (isFrontCamera) "前置" else "后置"}")
                } catch (exc: Exception) {
                    android.util.Log.e("CameraPreview", "相机绑定失败", exc)
                    onError("相机初始化失败: ${exc.message}")
                }

            }, ContextCompat.getMainExecutor(ctx))

            previewView
        },
        modifier = modifier,
        update = { previewView ->
            // 当isFrontCamera改变时，重新绑定相机
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            cameraProviderFuture.addListener({
                val cameraProvider = cameraProviderFuture.get()

                val preview = Preview.Builder().build().also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

                val imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build()
                    .also {
                        it.setAnalyzer(cameraExecutor) { imageProxy ->
                            // 根据捕获模式决定是否调用回调
                            when (captureMode) {
                                "continuous" -> {
                                    // 连续模式：持续调用回调（用于首页实时推理）
                                    onImageCaptured?.invoke(imageProxy)
                                }
                                "manual" -> {
                                    // 手动模式：只在需要时调用（用于样本收集）
                                    // 这里暂存最新的帧，等待手动触发
                                    // 注意：这里需要配合外部的触发机制
                                    onImageCaptured?.invoke(imageProxy)
                                }
                                else -> {
                                    // 默认连续模式
                                    onImageCaptured?.invoke(imageProxy)
                                }
                            }
                        }
                    }

                val cameraSelector = if (isFrontCamera) {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                } else {
                    CameraSelector.DEFAULT_BACK_CAMERA
                }

                try {
                    cameraProvider.unbindAll()
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageAnalyzer
                    )
                    android.util.Log.i("CameraPreview", "相机重新绑定成功: ${if (isFrontCamera) "前置" else "后置"}")
                } catch (exc: Exception) {
                    android.util.Log.e("CameraPreview", "相机重新绑定失败", exc)
                    onError("相机切换失败: ${exc.message}")
                }
            }, ContextCompat.getMainExecutor(context))
        }
    )

    DisposableEffect(Unit) {
        onDispose {
            cameraExecutor.shutdown()
        }
    }
}

/**
 * 模拟相机预览
 */
@Composable
private fun MockCameraPreview(
    modifier: Modifier,
    errorMessage: String? = null,
    isFrontCamera: Boolean = false
) {

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(androidx.compose.ui.graphics.Color.Black)
    ) {
        // 模拟相机预览画面 - 显示更真实的场景
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            // 绘制背景渐变
            drawRect(
                brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                    colors = listOf(
                        androidx.compose.ui.graphics.Color(0xFF87CEEB), // 天空蓝
                        androidx.compose.ui.graphics.Color(0xFF98FB98)  // 草绿
                    )
                ),
                size = size
            )

            // 绘制模拟物体（用于分类测试）
            val objectTypes = listOf("苹果", "香蕉", "橙子", "杯子", "书本")
            val colors = listOf(
                androidx.compose.ui.graphics.Color.Red,
                androidx.compose.ui.graphics.Color.Yellow,
                androidx.compose.ui.graphics.Color(0xFFFFA500), // 橙色
                androidx.compose.ui.graphics.Color.Blue,
                androidx.compose.ui.graphics.Color(0xFF8B4513) // 棕色
            )

            // 在中心区域绘制一个主要物体
            val centerX = size.width / 2
            val centerY = size.height / 2
            val objectIndex = (System.currentTimeMillis() / 3000 % objectTypes.size).toInt()

            drawCircle(
                color = colors[objectIndex],
                radius = 80f,
                center = androidx.compose.ui.geometry.Offset(centerX, centerY)
            )

            // 绘制物体标签 - 使用Compose的方式
            // 注意：这里简化处理，实际应用中可以使用Text组件覆盖在Canvas上
        }

        // 显示当前物体标签
        val objectTypes = listOf("苹果", "香蕉", "橙子", "杯子", "书本")
        val objectIndex = (System.currentTimeMillis() / 3000 % objectTypes.size).toInt()

        Text(
            text = objectTypes[objectIndex],
            color = androidx.compose.ui.graphics.Color.White,
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier
                .align(Alignment.Center)
                .offset(y = (-120).dp)
                .background(
                    androidx.compose.ui.graphics.Color.Black.copy(alpha = 0.7f),
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 16.dp, vertical = 8.dp)
        )

        // 预览信息
        Column(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        ) {
            Text(
                text = if (errorMessage != null) {
                    "相机预览 (模拟 - 相机不可用)"
                } else {
                    "相机预览 (模拟 - ${if (isFrontCamera) "前置" else "后置"})"
                },
                color = androidx.compose.ui.graphics.Color.White,
                style = MaterialTheme.typography.bodyMedium
            )

            if (errorMessage != null) {
                Text(
                    text = errorMessage,
                    color = androidx.compose.ui.graphics.Color.Red,
                    style = MaterialTheme.typography.bodySmall
                )
            }

            // 移除闪烁的分析指示器，改为持续显示流式推理状态
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = androidx.compose.ui.graphics.Color.Green,
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "流式推理中...",
                    color = androidx.compose.ui.graphics.Color.Green,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }

        // 中心十字线
        Canvas(
            modifier = Modifier
                .size(100.dp)
                .align(Alignment.Center)
        ) {
            val strokeWidth = 2.dp.toPx()
            val color = androidx.compose.ui.graphics.Color.White

            // 绘制十字线
            drawLine(
                color = color,
                start = androidx.compose.ui.geometry.Offset(size.width / 2, 0f),
                end = androidx.compose.ui.geometry.Offset(size.width / 2, size.height),
                strokeWidth = strokeWidth
            )
            drawLine(
                color = color,
                start = androidx.compose.ui.geometry.Offset(0f, size.height / 2),
                end = androidx.compose.ui.geometry.Offset(size.width, size.height / 2),
                strokeWidth = strokeWidth
            )
        }
    }
}

/**
 * 创建模拟的ImageProxy用于测试
 * 生成真实的测试图像数据
 */
private fun createMockImageProxy(context: Context): ImageProxy? {
    try {
        // 创建测试用的Bitmap
        val testBitmap = createMockBitmap()

        // 将Bitmap转换为字节数组
        val stream = java.io.ByteArrayOutputStream()
        testBitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream)
        val imageBytes = stream.toByteArray()

        // 由于CameraX版本兼容性问题，暂时返回null
        // 在实际应用中，这会触发回退到模拟实现
        android.util.Log.w("CameraPreview", "Mock ImageProxy创建失败，使用模拟数据")
        return null
    } catch (e: Exception) {
        android.util.Log.e("CameraPreview", "创建Mock ImageProxy失败", e)
        return null
    }
}

/**
 * 创建模拟图像用于测试
 * 生成具有特定类别特征的测试图像
 */
private fun createMockBitmap(): Bitmap {
    val bitmap = Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    val paint = Paint()

    // 绘制背景
    paint.color = Color.rgb(240, 240, 240)
    canvas.drawRect(0f, 0f, 224f, 224f, paint)

    // 根据时间选择不同的物体类型
    val objectTypes = arrayOf("apple", "banana", "orange", "cup", "book")
    val currentType = objectTypes[(System.currentTimeMillis() / 3000 % objectTypes.size).toInt()]

    when (currentType) {
        "apple" -> {
            // 绘制苹果 - 红色圆形
            paint.color = Color.rgb(220, 20, 60)
            canvas.drawCircle(112f, 112f, 60f, paint)
            // 苹果柄
            paint.color = Color.rgb(139, 69, 19)
            canvas.drawRect(108f, 52f, 116f, 72f, paint)
        }
        "banana" -> {
            // 绘制香蕉 - 黄色弯曲形状
            paint.color = Color.rgb(255, 255, 0)
            canvas.drawOval(60f, 80f, 164f, 144f, paint)
            paint.color = Color.rgb(139, 69, 19)
            canvas.drawRect(58f, 108f, 68f, 116f, paint)
        }
        "orange" -> {
            // 绘制橙子 - 橙色圆形
            paint.color = Color.rgb(255, 165, 0)
            canvas.drawCircle(112f, 112f, 55f, paint)
            // 橙子纹理
            paint.color = Color.rgb(255, 140, 0)
            for (i in 0..8) {
                val angle = i * 40f
                val x = 112f + 30f * kotlin.math.cos(Math.toRadians(angle.toDouble())).toFloat()
                val y = 112f + 30f * kotlin.math.sin(Math.toRadians(angle.toDouble())).toFloat()
                canvas.drawCircle(x, y, 3f, paint)
            }
        }
        "cup" -> {
            // 绘制杯子 - 蓝色矩形
            paint.color = Color.rgb(70, 130, 180)
            canvas.drawRect(80f, 90f, 144f, 160f, paint)
            // 杯子把手
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 8f
            canvas.drawArc(144f, 110f, 170f, 140f, -90f, 180f, false, paint)
            paint.style = Paint.Style.FILL
        }
        "book" -> {
            // 绘制书本 - 棕色矩形
            paint.color = Color.rgb(139, 69, 19)
            canvas.drawRect(70f, 80f, 154f, 144f, paint)
            // 书页
            paint.color = Color.rgb(255, 255, 255)
            canvas.drawRect(74f, 84f, 150f, 140f, paint)
            // 书脊线
            paint.color = Color.rgb(0, 0, 0)
            paint.strokeWidth = 2f
            canvas.drawLine(112f, 84f, 112f, 140f, paint)
        }
    }

    return bitmap
}
