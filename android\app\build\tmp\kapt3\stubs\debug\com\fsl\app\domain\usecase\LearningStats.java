package com.fsl.app.domain.usecase;

import java.lang.System;

/**
 * 学习统计信息
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\tH\u00c6\u0003J;\u0010\u0018\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\tH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\u001c\u001a\u00020\u001dJ\u0006\u0010\u001e\u001a\u00020\u001dJ\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001J\t\u0010 \u001a\u00020\u001dH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011\u00a8\u0006!"}, d2 = {"Lcom/fsl/app/domain/usecase/LearningStats;", "", "totalClasses", "", "totalSamples", "averageAccuracy", "", "averageSamplesPerClass", "modelSize", "", "(IIFFJ)V", "getAverageAccuracy", "()F", "getAverageSamplesPerClass", "getModelSize", "()J", "getTotalClasses", "()I", "getTotalSamples", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "getAccuracyPercentage", "", "getFormattedModelSize", "hashCode", "toString", "app_debug"})
public final class LearningStats {
    private final int totalClasses = 0;
    private final int totalSamples = 0;
    private final float averageAccuracy = 0.0F;
    private final float averageSamplesPerClass = 0.0F;
    private final long modelSize = 0L;
    
    /**
     * 学习统计信息
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.usecase.LearningStats copy(int totalClasses, int totalSamples, float averageAccuracy, float averageSamplesPerClass, long modelSize) {
        return null;
    }
    
    /**
     * 学习统计信息
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 学习统计信息
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 学习统计信息
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public LearningStats(int totalClasses, int totalSamples, float averageAccuracy, float averageSamplesPerClass, long modelSize) {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int getTotalClasses() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int getTotalSamples() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getAverageAccuracy() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float getAverageSamplesPerClass() {
        return 0.0F;
    }
    
    public final long component5() {
        return 0L;
    }
    
    public final long getModelSize() {
        return 0L;
    }
    
    /**
     * 格式化模型大小
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFormattedModelSize() {
        return null;
    }
    
    /**
     * 获取准确率百分比
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getAccuracyPercentage() {
        return null;
    }
}