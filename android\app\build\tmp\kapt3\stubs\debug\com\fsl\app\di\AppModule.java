package com.fsl.app.di;

import java.lang.System;

@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\t\u001a\u00020\n2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u000b\u001a\u00020\fH\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0004H\u0007J\n\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0007J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\u0004H\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/fsl/app/di/AppModule;", "", "()V", "provideAppDatabase", "Lcom/fsl/app/data/database/AppDatabase;", "context", "Landroid/content/Context;", "provideAssetUtils", "Lcom/fsl/app/data/utils/AssetUtils;", "provideFileManager", "Lcom/fsl/app/data/utils/FileManager;", "provideImageProcessor", "Lcom/fsl/app/data/utils/ImageProcessor;", "provideLearnedClassDao", "Lcom/fsl/app/data/database/LearnedClassDao;", "database", "provideNativeInferenceEngine", "Lcom/fsl/app/data/inference/NativeInferenceEngine;", "provideTrainingSampleDao", "Lcom/fsl/app/data/database/TrainingSampleDao;", "app_debug"})
@dagger.Module
public final class AppModule {
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.di.AppModule INSTANCE = null;
    
    private AppModule() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.utils.ImageProcessor provideImageProcessor() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.utils.AssetUtils provideAssetUtils(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.utils.FileManager provideFileManager(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.database.AppDatabase provideAppDatabase(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.database.LearnedClassDao provideLearnedClassDao(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.AppDatabase database) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.database.TrainingSampleDao provideTrainingSampleDao(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.AppDatabase database) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    @javax.inject.Singleton
    @dagger.Provides
    public final com.fsl.app.data.inference.NativeInferenceEngine provideNativeInferenceEngine() {
        return null;
    }
}