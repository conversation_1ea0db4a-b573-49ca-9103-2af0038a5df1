/**
 * 跟踪结果数据类
 * 
 * 表示对象跟踪和分类的结果
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 跟踪结果
 * 
 * @param trackId 跟踪ID
 * @param className 分类名称
 * @param confidence 置信度 (0.0-1.0)
 * @param x 边界框左上角X坐标 (归一化坐标 0.0-1.0)
 * @param y 边界框左上角Y坐标 (归一化坐标 0.0-1.0)
 * @param width 边界框宽度 (归一化坐标 0.0-1.0)
 * @param height 边界框高度 (归一化坐标 0.0-1.0)
 * @param timestamp 时间戳 (毫秒)
 */
@Parcelize
data class TrackingResult(
    val trackId: Int,
    val className: String,
    val confidence: Float,
    val x: Float,
    val y: Float,
    val width: Float,
    val height: Float,
    val timestamp: Long
) : Parcelable {
    
    /**
     * 获取边界框中心点坐标
     */
    fun getCenterX(): Float = x + width / 2f
    
    /**
     * 获取边界框中心点坐标
     */
    fun getCenterY(): Float = y + height / 2f
    
    /**
     * 获取边界框面积
     */
    fun getArea(): Float = width * height
    
    /**
     * 转换为像素坐标
     * 
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 像素坐标的边界框
     */
    fun toPixelCoordinates(imageWidth: Int, imageHeight: Int): PixelBoundingBox {
        return PixelBoundingBox(
            left = (x * imageWidth).toInt(),
            top = (y * imageHeight).toInt(),
            right = ((x + width) * imageWidth).toInt(),
            bottom = ((y + height) * imageHeight).toInt()
        )
    }
    
    /**
     * 检查是否为有效的跟踪结果
     */
    fun isValid(): Boolean {
        return trackId >= 0 &&
                className.isNotEmpty() &&
                confidence in 0f..1f &&
                x in 0f..1f &&
                y in 0f..1f &&
                width > 0f &&
                height > 0f &&
                (x + width) <= 1f &&
                (y + height) <= 1f &&
                timestamp > 0
    }
    
    /**
     * 计算与另一个跟踪结果的IoU (Intersection over Union)
     */
    fun calculateIoU(other: TrackingResult): Float {
        val intersectionLeft = maxOf(x, other.x)
        val intersectionTop = maxOf(y, other.y)
        val intersectionRight = minOf(x + width, other.x + other.width)
        val intersectionBottom = minOf(y + height, other.y + other.height)
        
        if (intersectionRight <= intersectionLeft || intersectionBottom <= intersectionTop) {
            return 0f
        }
        
        val intersectionArea = (intersectionRight - intersectionLeft) * (intersectionBottom - intersectionTop)
        val unionArea = getArea() + other.getArea() - intersectionArea
        
        return if (unionArea > 0f) intersectionArea / unionArea else 0f
    }
    
    /**
     * 获取置信度等级
     */
    fun getConfidenceLevel(): ConfidenceLevel {
        return when {
            confidence >= 0.8f -> ConfidenceLevel.HIGH
            confidence >= 0.6f -> ConfidenceLevel.MEDIUM
            confidence >= 0.4f -> ConfidenceLevel.LOW
            else -> ConfidenceLevel.VERY_LOW
        }
    }
    
    /**
     * 置信度等级枚举
     */
    enum class ConfidenceLevel {
        VERY_LOW,
        LOW,
        MEDIUM,
        HIGH
    }
    
    override fun toString(): String {
        return "TrackingResult(id=$trackId, class='$className', conf=${String.format("%.2f", confidence)}, " +
                "bbox=[${String.format("%.3f", x)}, ${String.format("%.3f", y)}, " +
                "${String.format("%.3f", width)}, ${String.format("%.3f", height)}], " +
                "time=$timestamp)"
    }
}

/**
 * 像素坐标边界框
 */
@Parcelize
data class PixelBoundingBox(
    val left: Int,
    val top: Int,
    val right: Int,
    val bottom: Int
) : Parcelable {
    
    val width: Int get() = right - left
    val height: Int get() = bottom - top
    val centerX: Int get() = left + width / 2
    val centerY: Int get() = top + height / 2
    val area: Int get() = width * height
    
    /**
     * 检查边界框是否有效
     */
    fun isValid(): Boolean {
        return left >= 0 && top >= 0 && right > left && bottom > top
    }
    
    /**
     * 转换为归一化坐标
     */
    fun toNormalizedCoordinates(imageWidth: Int, imageHeight: Int): TrackingResult? {
        if (!isValid() || imageWidth <= 0 || imageHeight <= 0) {
            return null
        }
        
        return TrackingResult(
            trackId = -1, // 临时ID
            className = "",
            confidence = 0f,
            x = left.toFloat() / imageWidth,
            y = top.toFloat() / imageHeight,
            width = width.toFloat() / imageWidth,
            height = height.toFloat() / imageHeight,
            timestamp = System.currentTimeMillis()
        )
    }
}
