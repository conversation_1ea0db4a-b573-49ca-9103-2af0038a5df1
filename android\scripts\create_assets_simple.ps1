# 简化的PyTorch资源文件创建脚本

Write-Host "=== 创建PyTorch Mobile资源文件 ===" -ForegroundColor Green

# 创建assets目录
$assetsDir = "../app/src/main/assets"
if (-not (Test-Path $assetsDir)) {
    New-Item -ItemType Directory -Path $assetsDir -Force | Out-Null
}
Write-Host "Assets目录: $assetsDir" -ForegroundColor Yellow

# 创建模拟模型文件
$modelPath = "$assetsDir/prototypical_network.ptl"
"Mock PyTorch Lite Model File" | Out-File -FilePath $modelPath -Encoding UTF8
Write-Host "✅ 模型文件: $modelPath" -ForegroundColor Green

# 创建类别名称文件
$classNamesPath = "$assetsDir/class_names.json"
$classNamesJson = '["cat", "dog", "bird", "flower", "car", "person", "bicycle", "airplane", "boat", "train"]'
$classNamesJson | Out-File -FilePath $classNamesPath -Encoding UTF8
Write-Host "✅ 类别文件: $classNamesPath" -ForegroundColor Green

# 创建模型信息文件
$modelInfoPath = "$assetsDir/model_info.json"
$modelInfoJson = @'
{
  "model_name": "prototypical_network",
  "version": "1.0.0",
  "framework": "pytorch_mobile",
  "input_size": [224, 224, 3],
  "feature_dim": 512,
  "num_classes": 10,
  "model_file": "prototypical_network.ptl",
  "preprocessing": {
    "mean": [0.485, 0.456, 0.406],
    "std": [0.229, 0.224, 0.225],
    "resize": 224,
    "normalize": true
  }
}
'@
$modelInfoJson | Out-File -FilePath $modelInfoPath -Encoding UTF8
Write-Host "✅ 信息文件: $modelInfoPath" -ForegroundColor Green

# 验证文件
Write-Host "`n验证生成的文件:" -ForegroundColor Yellow
$files = @($modelPath, $classNamesPath, $modelInfoPath)
foreach ($file in $files) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "  ✅ $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
    }
}

Write-Host "`nPyTorch assets created successfully!" -ForegroundColor Green
