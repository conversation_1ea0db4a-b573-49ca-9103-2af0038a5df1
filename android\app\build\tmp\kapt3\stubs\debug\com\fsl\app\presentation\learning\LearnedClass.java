package com.fsl.app.presentation.learning;

import java.lang.System;

/**
 * 已学习的类别
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0018"}, d2 = {"Lcom/fsl/app/presentation/learning/LearnedClass;", "", "name", "", "sampleCount", "", "accuracy", "", "(Ljava/lang/String;IF)V", "getAccuracy", "()F", "getName", "()Ljava/lang/String;", "getSampleCount", "()I", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class LearnedClass {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    private final int sampleCount = 0;
    private final float accuracy = 0.0F;
    
    /**
     * 已学习的类别
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.learning.LearnedClass copy(@org.jetbrains.annotations.NotNull
    java.lang.String name, int sampleCount, float accuracy) {
        return null;
    }
    
    /**
     * 已学习的类别
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 已学习的类别
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 已学习的类别
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public LearnedClass(@org.jetbrains.annotations.NotNull
    java.lang.String name, int sampleCount, float accuracy) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int getSampleCount() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getAccuracy() {
        return 0.0F;
    }
}