package com.fsl.app.domain.usecase;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J*\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rJ0\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0013J4\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u0015\u001a\u00020\u0016H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0017\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\t2\u0006\u0010\u001b\u001a\u00020\tJ\u000e\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\tJ\u0018\u0010\u001f\u001a\u00020 2\u0006\u0010\u001e\u001a\u00020\t2\b\b\u0002\u0010!\u001a\u00020\"R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006#"}, d2 = {"Lcom/fsl/app/domain/usecase/ClassificationUseCase;", "", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "imageProcessingRepository", "Lcom/fsl/app/domain/repository/IImageProcessingRepository;", "(Lcom/fsl/app/domain/repository/IInferenceRepository;Lcom/fsl/app/domain/repository/IImageProcessingRepository;)V", "classify", "Lkotlin/Result;", "Lcom/fsl/app/domain/model/ClassificationResult;", "image", "Landroid/graphics/Bitmap;", "classify-gIAlu-s", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "classifyBatch", "Lcom/fsl/app/domain/model/BatchClassificationResult;", "images", "", "classifyBatch-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "classifyWithTopN", "topN", "", "classifyWithTopN-0E7RQCE", "(Landroid/graphics/Bitmap;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "compareClassificationResults", "result1", "result2", "getClassificationSuggestion", "", "result", "validateClassificationResult", "", "threshold", "", "app_debug"})
@javax.inject.Singleton
public final class ClassificationUseCase {
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final com.fsl.app.domain.repository.IImageProcessingRepository imageProcessingRepository = null;
    
    @javax.inject.Inject
    public ClassificationUseCase(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IImageProcessingRepository imageProcessingRepository) {
        super();
    }
    
    /**
     * 验证分类结果的可信度
     *
     * @param result 分类结果
     * @param threshold 置信度阈值
     * @return 是否可信
     */
    public final boolean validateClassificationResult(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult result, float threshold) {
        return false;
    }
    
    /**
     * 比较两个分类结果
     *
     * @param result1 第一个结果
     * @param result2 第二个结果
     * @return 比较结果（正数表示result1更好，负数表示result2更好，0表示相等）
     */
    public final int compareClassificationResults(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult result1, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult result2) {
        return 0;
    }
    
    /**
     * 获取分类建议
     *
     * @param result 分类结果
     * @return 建议文本
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getClassificationSuggestion(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult result) {
        return null;
    }
}