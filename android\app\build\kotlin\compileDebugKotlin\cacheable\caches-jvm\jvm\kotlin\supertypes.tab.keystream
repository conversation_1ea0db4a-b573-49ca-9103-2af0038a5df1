com.fsl.app.FSLApplicationcom.fsl.app.MainActivity%com.fsl.app.data.database.AppDatabase1com.fsl.app.data.inference.PyTorchInferenceEngine1com.fsl.app.data.repository.GalleryRepositoryImpl5com.fsl.app.data.repository.ImageProcessingRepository+com.fsl.app.data.repository.ModelRepository-com.fsl.app.domain.model.ClassificationResult%com.fsl.app.domain.model.QualityLevel2com.fsl.app.domain.model.BatchClassificationResult'com.fsl.app.domain.model.TrackingResult7com.fsl.app.domain.model.TrackingResult.ConfidenceLevel)com.fsl.app.domain.model.PixelBoundingBox1com.fsl.app.domain.usecase.ValidationResult.Valid3com.fsl.app.domain.usecase.ValidationResult.Invalid3com.fsl.app.domain.usecase.ValidationResult.Warning&com.fsl.app.presentation.MainViewModel0com.fsl.app.presentation.camera.CameraState.Idle6com.fsl.app.presentation.camera.CameraState.Processing1com.fsl.app.presentation.camera.CameraState.Error/com.fsl.app.presentation.camera.CameraViewModel1com.fsl.app.presentation.gallery.GalleryViewModel3com.fsl.app.presentation.learning.LearningViewModel;com.fsl.app.presentation.learning.SampleCollectionViewModel3com.fsl.app.presentation.settings.SettingsViewModelCcom.fsl.app.presentation.learning.SampleCollectionViewModel_Factory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          