// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.di;

import com.fsl.app.data.database.AppDatabase;
import com.fsl.app.data.database.TrainingSampleDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AppModule_ProvideTrainingSampleDaoFactory implements Factory<TrainingSampleDao> {
  private final Provider<AppDatabase> databaseProvider;

  public AppModule_ProvideTrainingSampleDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public TrainingSampleDao get() {
    return provideTrainingSampleDao(databaseProvider.get());
  }

  public static AppModule_ProvideTrainingSampleDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new AppModule_ProvideTrainingSampleDaoFactory(databaseProvider);
  }

  public static TrainingSampleDao provideTrainingSampleDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideTrainingSampleDao(database));
  }
}
