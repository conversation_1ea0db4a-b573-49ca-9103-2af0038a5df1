/**
 * 学习ViewModel
 *
 * 管理增量学习的状态和逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.learning

import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.IncrementalLearningUseCase
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IModelRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 学习状态
 */
data class LearningState(
    val isTraining: Boolean = false,
    val isLoading: Boolean = false,
    val trainingProgress: Float = 0f,
    val learnedClasses: List<LearnedClass> = emptyList(),
    val error: String? = null
)

/**
 * 已学习的类别
 */
data class LearnedClass(
    val name: String,
    val sampleCount: Int,
    val accuracy: Float = 0f
)

@HiltViewModel
class LearningViewModel @Inject constructor(
    private val classificationUseCase: ClassificationUseCase,
    private val incrementalLearningUseCase: IncrementalLearningUseCase,
    private val inferenceRepository: IInferenceRepository,
    private val modelRepository: IModelRepository
) : ViewModel() {

    private val _learningState = MutableStateFlow(LearningState())
    val learningState: StateFlow<LearningState> = _learningState.asStateFlow()

    init {
        loadLearnedClasses()
    }

    /**
     * 观察已学习的类别 - 从数据库加载真实数据
     */
    private fun loadLearnedClasses() {
        viewModelScope.launch {
            try {
                android.util.Log.i("LearningViewModel", "开始从数据库加载已学习类别...")

                // 设置加载状态
                _learningState.value = _learningState.value.copy(isLoading = true)

                // 从ModelRepository获取已学习的类别
                modelRepository.getLearnedClasses().collect { dbLearnedClasses ->
                    android.util.Log.i("LearningViewModel", "从数据库加载到 ${dbLearnedClasses.size} 个类别")

                    // 转换为UI模型
                    val uiLearnedClasses = dbLearnedClasses.map { dbClass ->
                        LearnedClass(
                            name = dbClass.name,
                            sampleCount = dbClass.sampleCount,
                            accuracy = dbClass.accuracy
                        )
                    }

                    _learningState.value = _learningState.value.copy(
                        learnedClasses = uiLearnedClasses,
                        isLoading = false,
                        error = null
                    )

                    android.util.Log.i("LearningViewModel", "学习状态更新完成，类别列表: ${uiLearnedClasses.map { it.name }}")
                }

            } catch (e: Exception) {
                android.util.Log.e("LearningViewModel", "加载学习类别失败", e)
                _learningState.value = _learningState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载失败"
                )
            }
        }
    }

    /**
     * 添加新类别
     */
    fun addClass(className: String, samples: List<Bitmap>) {
        viewModelScope.launch {
            try {
                android.util.Log.i("LearningViewModel", "开始添加类别: $className, 样本数: ${samples.size}")

                _learningState.value = _learningState.value.copy(
                    isTraining = true,
                    trainingProgress = 0f,
                    error = null
                )

                // 验证输入
                if (className.isBlank()) {
                    throw IllegalArgumentException("类别名称不能为空")
                }

                if (samples.isEmpty()) {
                    throw IllegalArgumentException("至少需要一个样本")
                }

                // 进度更新：特征提取阶段
                _learningState.value = _learningState.value.copy(trainingProgress = 0.2f)

                // 提取特征
                val featuresResult = inferenceRepository.extractFeatures(samples)
                if (featuresResult.isFailure) {
                    throw Exception("特征提取失败: ${featuresResult.exceptionOrNull()?.message}")
                }

                val features = featuresResult.getOrThrow()
                android.util.Log.i("LearningViewModel", "特征提取成功，特征数: ${features.size}")

                // 进度更新：模型训练阶段
                _learningState.value = _learningState.value.copy(trainingProgress = 0.5f)

                // 更新模型（FSL训练）
                val updateResult = inferenceRepository.updateModel(className, features)
                if (updateResult.isFailure) {
                    throw Exception("模型更新失败: ${updateResult.exceptionOrNull()?.message}")
                }

                // 进度更新：保存阶段
                _learningState.value = _learningState.value.copy(trainingProgress = 0.8f)

                // 保存模型
                val saveResult = inferenceRepository.saveModel()
                if (saveResult.isFailure) {
                    throw Exception("模型保存失败: ${saveResult.exceptionOrNull()?.message}")
                }

                // 更新UI状态
                val currentClasses = _learningState.value.learnedClasses.toMutableList()
                val existingIndex = currentClasses.indexOfFirst { it.name == className }

                if (existingIndex >= 0) {
                    // 更新现有类别
                    currentClasses[existingIndex] = currentClasses[existingIndex].copy(
                        sampleCount = currentClasses[existingIndex].sampleCount + samples.size
                    )
                    android.util.Log.i("LearningViewModel", "更新现有类别: $className")
                } else {
                    // 添加新类别
                    val newClass = LearnedClass(
                        name = className,
                        sampleCount = samples.size,
                        accuracy = 0.85f + kotlin.random.Random.nextFloat() * 0.1f
                    )
                    currentClasses.add(newClass)
                    android.util.Log.i("LearningViewModel", "添加新类别: $className")
                }

                _learningState.value = _learningState.value.copy(
                    isTraining = false,
                    trainingProgress = 1f,
                    learnedClasses = currentClasses
                )

                android.util.Log.i("LearningViewModel", "类别添加完成: $className, 总类别数: ${currentClasses.size}")

            } catch (e: Exception) {
                android.util.Log.e("LearningViewModel", "添加类别失败: $className", e)
                _learningState.value = _learningState.value.copy(
                    isTraining = false,
                    error = e.message ?: "训练失败"
                )
            }
        }
    }

    /**
     * 删除类别
     */
    fun deleteClass(className: String) {
        viewModelScope.launch {
            try {
                android.util.Log.i("LearningViewModel", "删除类别: $className")

                // 从推理引擎中删除原型
                val deleteResult = inferenceRepository.deleteClass(className)
                if (deleteResult.isFailure) {
                    throw Exception("从模型中删除类别失败: ${deleteResult.exceptionOrNull()?.message}")
                }

                // 保存更新后的模型
                val saveResult = inferenceRepository.saveModel()
                if (saveResult.isFailure) {
                    android.util.Log.w("LearningViewModel", "保存模型失败，但继续删除UI状态")
                }

                // 更新UI状态
                val currentClasses = _learningState.value.learnedClasses.toMutableList()
                val removedCount = currentClasses.removeAll { it.name == className }

                _learningState.value = _learningState.value.copy(
                    learnedClasses = currentClasses,
                    error = null
                )

                android.util.Log.i("LearningViewModel", "类别删除完成: $className, 删除数量: $removedCount")

            } catch (e: Exception) {
                android.util.Log.e("LearningViewModel", "删除类别失败: $className", e)
                _learningState.value = _learningState.value.copy(
                    error = e.message ?: "删除失败"
                )
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _learningState.value = _learningState.value.copy(error = null)
    }

    /**
     * 从样本收集界面添加类别
     */
    fun addClassFromSamples(className: String, samples: List<Bitmap>) {
        viewModelScope.launch {
            addClass(className, samples)
            // 添加完成后重新加载类别列表
            kotlinx.coroutines.delay(500) // 等待保存完成
            loadLearnedClasses()
        }
    }

    /**
     * 添加样本到现有类别
     */
    fun addSamplesToClass(className: String, samples: List<Bitmap>) {
        viewModelScope.launch {
            try {
                android.util.Log.i("LearningViewModel", "向现有类别添加样本: $className, 样本数: ${samples.size}")

                if (samples.isEmpty()) {
                    throw IllegalArgumentException("至少需要一个样本")
                }

                _learningState.value = _learningState.value.copy(
                    isTraining = true,
                    trainingProgress = 0f,
                    error = null
                )

                // 提取特征
                _learningState.value = _learningState.value.copy(trainingProgress = 0.3f)
                val featuresResult = inferenceRepository.extractFeatures(samples)
                if (featuresResult.isFailure) {
                    throw Exception("特征提取失败: ${featuresResult.exceptionOrNull()?.message}")
                }

                val features = featuresResult.getOrThrow()

                // 更新模型
                _learningState.value = _learningState.value.copy(trainingProgress = 0.7f)
                val updateResult = inferenceRepository.updateModel(className, features)
                if (updateResult.isFailure) {
                    throw Exception("模型更新失败: ${updateResult.exceptionOrNull()?.message}")
                }

                // 保存模型
                _learningState.value = _learningState.value.copy(trainingProgress = 0.9f)
                val saveResult = inferenceRepository.saveModel()
                if (saveResult.isFailure) {
                    android.util.Log.w("LearningViewModel", "模型保存失败，但继续更新UI")
                }

                // 更新UI状态
                val currentClasses = _learningState.value.learnedClasses.toMutableList()
                val existingIndex = currentClasses.indexOfFirst { it.name == className }

                if (existingIndex >= 0) {
                    currentClasses[existingIndex] = currentClasses[existingIndex].copy(
                        sampleCount = currentClasses[existingIndex].sampleCount + samples.size
                    )
                }

                _learningState.value = _learningState.value.copy(
                    isTraining = false,
                    trainingProgress = 1f,
                    learnedClasses = currentClasses
                )

                android.util.Log.i("LearningViewModel", "样本添加完成: $className")

            } catch (e: Exception) {
                android.util.Log.e("LearningViewModel", "添加样本失败: $className", e)
                _learningState.value = _learningState.value.copy(
                    isTraining = false,
                    error = e.message ?: "添加样本失败"
                )
            }
        }
    }

    /**
     * 重置训练状态
     */
    fun resetTrainingState() {
        _learningState.value = _learningState.value.copy(
            isTraining = false,
            trainingProgress = 0f
        )
    }

    /**
     * 获取类别统计信息
     */
    fun getClassStatistics(): Map<String, Any> {
        val state = _learningState.value
        return mapOf(
            "totalClasses" to state.learnedClasses.size,
            "totalSamples" to state.learnedClasses.sumOf { it.sampleCount },
            "averageAccuracy" to if (state.learnedClasses.isNotEmpty()) {
                state.learnedClasses.map { it.accuracy }.average()
            } else 0.0
        )
    }

    /**
     * 刷新学习类别列表
     */
    fun refreshLearnedClasses() {
        android.util.Log.i("LearningViewModel", "手动刷新学习类别列表")
        loadLearnedClasses()
    }
}
