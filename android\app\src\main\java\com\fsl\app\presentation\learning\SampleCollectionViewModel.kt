/**
 * 样本收集ViewModel
 *
 * 负责收集训练样本的逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.learning

import android.graphics.Bitmap
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.ImageProcessingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 样本收集状态
 */
data class SampleCollectionState(
    val className: String = "",
    val collectedSamples: List<CollectedSample> = emptyList(),
    val isCollecting: Boolean = false,
    val targetSampleCount: Int = 5,
    val error: String? = null
)

/**
 * 收集的样本数据
 */
data class CollectedSample(
    val bitmap: Bitmap,
    val timestamp: Long = System.currentTimeMillis(),
    val features: FloatArray? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as CollectedSample
        return timestamp == other.timestamp
    }

    override fun hashCode(): Int {
        return timestamp.hashCode()
    }
}

@HiltViewModel
class SampleCollectionViewModel @Inject constructor(
    application: Application,
    private val inferenceRepository: IInferenceRepository,
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase,
    private val galleryRepository: com.fsl.app.data.repository.GalleryRepositoryImpl
) : AndroidViewModel(application) {

    private val _collectionState = MutableStateFlow(SampleCollectionState())
    val collectionState: StateFlow<SampleCollectionState> = _collectionState.asStateFlow()

    /**
     * 设置要收集的类别名称
     */
    fun setClassName(className: String) {
        _collectionState.value = _collectionState.value.copy(
            className = className,
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 设置目标样本数量
     */
    fun setTargetSampleCount(count: Int) {
        _collectionState.value = _collectionState.value.copy(
            targetSampleCount = count.coerceIn(1, 20)
        )
    }

    /**
     * 添加样本
     */
    fun addSample(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                val currentState = _collectionState.value

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 提取特征
                val featuresResult = inferenceRepository.extractFeatures(listOf(bitmap))
                val features = if (featuresResult.isSuccess) {
                    featuresResult.getOrThrow().firstOrNull()
                } else {
                    null
                }

                val newSample = CollectedSample(
                    bitmap = bitmap,
                    features = features
                )

                val updatedSamples = currentState.collectedSamples + newSample

                // 保存样本到图库
                saveToGallery(bitmap, currentState.className)

                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false
                )

                android.util.Log.i("SampleCollection",
                    "添加样本成功: ${currentState.className}, 当前样本数: ${updatedSamples.size}")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "添加样本失败", e)
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "添加样本失败"
                )
            }
        }
    }

    /**
     * 移除样本
     */
    fun removeSample(sample: CollectedSample) {
        val currentState = _collectionState.value
        val updatedSamples = currentState.collectedSamples.filter { it != sample }

        _collectionState.value = currentState.copy(
            collectedSamples = updatedSamples
        )
    }

    /**
     * 清除所有样本
     */
    fun clearSamples() {
        _collectionState.value = _collectionState.value.copy(
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 检查是否可以完成收集
     */
    fun canCompleteCollection(): Boolean {
        val state = _collectionState.value
        return state.className.isNotBlank() &&
               state.collectedSamples.isNotEmpty() &&
               state.collectedSamples.size >= 3 // 至少需要3个样本
    }

    /**
     * 获取收集的样本位图列表
     */
    fun getCollectedBitmaps(): List<Bitmap> {
        return _collectionState.value.collectedSamples.map { it.bitmap }
    }

    /**
     * 获取收集的特征列表
     */
    fun getCollectedFeatures(): List<FloatArray> {
        return _collectionState.value.collectedSamples.mapNotNull { it.features }
    }

    /**
     * 重置收集状态
     */
    fun reset() {
        _collectionState.value = SampleCollectionState()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _collectionState.value = _collectionState.value.copy(error = null)
    }

    /**
     * 获取收集进度
     */
    fun getCollectionProgress(): Float {
        val state = _collectionState.value
        return if (state.targetSampleCount > 0) {
            state.collectedSamples.size.toFloat() / state.targetSampleCount.toFloat()
        } else {
            0f
        }
    }

    /**
     * 是否收集完成
     */
    fun isCollectionComplete(): Boolean {
        val state = _collectionState.value
        return state.collectedSamples.size >= state.targetSampleCount
    }

    /**
     * 捕获当前帧（模拟相机拍照）
     * 在真实应用中，这里应该与CameraPreview组件集成
     */
    fun captureCurrentFrame() {
        viewModelScope.launch {
            try {
                val currentState = _collectionState.value

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 创建测试样本（在真实应用中，这里应该从相机获取当前帧）
                val capturedBitmap = createTestSample(currentState.className)

                // 提取特征
                val featuresResult = inferenceRepository.extractFeatures(listOf(capturedBitmap))
                val features = if (featuresResult.isSuccess) {
                    featuresResult.getOrThrow().firstOrNull()
                } else {
                    null
                }

                val newSample = CollectedSample(
                    bitmap = capturedBitmap,
                    features = features
                )

                val updatedSamples = currentState.collectedSamples + newSample

                // 保存样本到图库
                saveToGallery(capturedBitmap, currentState.className)

                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false
                )

                android.util.Log.i("SampleCollection",
                    "拍照样本成功: ${currentState.className}, 当前样本数: ${updatedSamples.size}")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "拍照失败", e)
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "拍照失败"
                )
            }
        }
    }

    /**
     * 创建测试样本（模拟拍照结果）
     */
    private fun createTestSample(className: String): Bitmap {
        return Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
            val canvas = android.graphics.Canvas(this)
            val paint = android.graphics.Paint()

            // 根据类别名称生成不同的样本
            val classHash = className.hashCode()
            val baseColor = when (classHash % 5) {
                0 -> android.graphics.Color.rgb(220, 20, 60)   // 红色系
                1 -> android.graphics.Color.rgb(255, 255, 0)   // 黄色系
                2 -> android.graphics.Color.rgb(255, 165, 0)   // 橙色系
                3 -> android.graphics.Color.rgb(70, 130, 180)  // 蓝色系
                else -> android.graphics.Color.rgb(139, 69, 19) // 棕色系
            }

            // 绘制背景
            paint.color = android.graphics.Color.rgb(240, 240, 240)
            canvas.drawRect(0f, 0f, 224f, 224f, paint)

            // 绘制主要形状
            paint.color = baseColor
            val shapeType = classHash % 3
            when (shapeType) {
                0 -> canvas.drawCircle(112f, 112f, 80f, paint)
                1 -> canvas.drawRect(50f, 50f, 174f, 174f, paint)
                else -> canvas.drawOval(40f, 80f, 184f, 144f, paint)
            }

            // 添加类别标签
            paint.color = android.graphics.Color.BLACK
            paint.textSize = 24f
            paint.textAlign = android.graphics.Paint.Align.CENTER
            paint.typeface = android.graphics.Typeface.DEFAULT_BOLD
            canvas.drawText(className, 112f, 200f, paint)

            // 添加时间戳以确保每次都不同
            paint.textSize = 12f
            canvas.drawText("${System.currentTimeMillis() % 10000}", 112f, 220f, paint)
        }
    }

    /**
     * 保存样本到图库
     */
    private suspend fun saveToGallery(bitmap: Bitmap, className: String) {
        try {
            // 创建分类结果
            val classificationResult = com.fsl.app.domain.model.ClassificationResult(
                className = className,
                confidence = 1.0f, // 学习样本的置信度设为1.0
                allScores = mapOf(className to 1.0f),
                inferenceTime = 0L
            )

            // 保存到图库
            val timestamp = System.currentTimeMillis()
            val filename = "FSL_${className}_${timestamp}.jpg"

            // 保存图像文件
            val context = getApplication<Application>()
            val imagesDir = java.io.File(context.getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES), "FSL_Gallery")
            if (!imagesDir.exists()) {
                imagesDir.mkdirs()
            }

            val imageFile = java.io.File(imagesDir, filename)
            val outputStream = java.io.FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            outputStream.flush()
            outputStream.close()

            // 保存元数据，标记为学习样本
            galleryRepository.saveImageMetadata(filename, classificationResult, timestamp)

            // 标记为学习样本
            val galleryImage = com.fsl.app.domain.model.GalleryImage(
                file = imageFile,
                classificationResult = classificationResult,
                timestamp = timestamp,
                isLearned = false
            )
            galleryRepository.markAsLearned(galleryImage, className)

            android.util.Log.i("SampleCollection", "样本已保存到图库: $filename")

        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
        }
    }


}
