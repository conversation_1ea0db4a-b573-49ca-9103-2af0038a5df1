/**
 * 样本收集ViewModel
 *
 * 负责收集训练样本的逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.learning

import android.graphics.Bitmap
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IModelRepository
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.ImageProcessingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlin.math.min
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import androidx.camera.core.ImageProxy

/**
 * 样本收集状态
 */
data class SampleCollectionState(
    val className: String = "",
    val collectedSamples: List<CollectedSample> = emptyList(),
    val isCollecting: Boolean = false,
    val targetSampleCount: Int = 5,
    val error: String? = null,
    // GPU加速学习进度相关
    val isLearning: Boolean = false,
    val learningProgress: Float = 0f,
    val learningStage: String = "",
    val estimatedTimeRemaining: Long = 0L,
    val learningStartTime: Long = 0L,
    val useGpuAcceleration: Boolean = true,
    // 学习完成状态
    val isLearningCompleted: Boolean = false,
    val learningSuccess: Boolean = false
)

/**
 * 收集的样本数据
 */
data class CollectedSample(
    val bitmap: Bitmap,
    val timestamp: Long = System.currentTimeMillis(),
    val features: FloatArray? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as CollectedSample
        return timestamp == other.timestamp
    }

    override fun hashCode(): Int {
        return timestamp.hashCode()
    }
}

@HiltViewModel
class SampleCollectionViewModel @Inject constructor(
    application: Application,
    private val inferenceRepository: IInferenceRepository,
    private val modelRepository: IModelRepository,
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase,
    private val galleryRepository: com.fsl.app.data.repository.GalleryRepositoryImpl
) : AndroidViewModel(application) {

    private val _collectionState = MutableStateFlow(SampleCollectionState())
    val collectionState: StateFlow<SampleCollectionState> = _collectionState.asStateFlow()

    // 相机帧捕获回调
    private var currentImageProxy: ImageProxy? = null
    private val _shouldCaptureFrame = MutableStateFlow(false)
    val shouldCaptureFrame: StateFlow<Boolean> = _shouldCaptureFrame.asStateFlow()

    /**
     * 设置当前相机帧
     * 由CameraPreview组件调用，提供实时相机帧
     */
    fun setCurrentImageProxy(imageProxy: ImageProxy?) {
        currentImageProxy?.close() // 释放之前的ImageProxy
        currentImageProxy = imageProxy
    }

    /**
     * 请求捕获相机帧
     * 触发相机组件提供当前帧
     */
    fun requestCameraFrame() {
        android.util.Log.i("SampleCollectionViewModel", "=== 请求相机帧 ===")
        android.util.Log.i("SampleCollectionViewModel", "设置shouldCaptureFrame = true")
        _shouldCaptureFrame.value = true
        android.util.Log.i("SampleCollectionViewModel", "shouldCaptureFrame已设置为: ${_shouldCaptureFrame.value}")
    }

    /**
     * 重置帧捕获请求
     */
    fun resetCaptureRequest() {
        android.util.Log.i("SampleCollectionViewModel", "=== 重置帧捕获请求 ===")
        android.util.Log.i("SampleCollectionViewModel", "设置shouldCaptureFrame = false")
        _shouldCaptureFrame.value = false
        android.util.Log.i("SampleCollectionViewModel", "shouldCaptureFrame已重置为: ${_shouldCaptureFrame.value}")
    }

    /**
     * 检查相机和存储权限
     */
    fun checkPermissions(context: android.content.Context): Boolean {
        val cameraPermission = android.Manifest.permission.CAMERA
        val storagePermission = android.Manifest.permission.WRITE_EXTERNAL_STORAGE

        val hasCameraPermission = androidx.core.content.ContextCompat.checkSelfPermission(
            context, cameraPermission
        ) == android.content.pm.PackageManager.PERMISSION_GRANTED

        val hasStoragePermission = androidx.core.content.ContextCompat.checkSelfPermission(
            context, storagePermission
        ) == android.content.pm.PackageManager.PERMISSION_GRANTED

        android.util.Log.i("SampleCollection", "权限检查 - 相机: $hasCameraPermission, 存储: $hasStoragePermission")

        if (!hasCameraPermission) {
            _collectionState.value = _collectionState.value.copy(
                error = "需要相机权限才能拍摄样本，请在设置中授予权限"
            )
            return false
        }

        if (!hasStoragePermission) {
            _collectionState.value = _collectionState.value.copy(
                error = "需要存储权限才能保存样本，请在设置中授予权限"
            )
            return false
        }

        return true
    }

    /**
     * 切换GPU加速
     */
    fun toggleGpuAcceleration() {
        _collectionState.value = _collectionState.value.copy(
            useGpuAcceleration = !_collectionState.value.useGpuAcceleration
        )
    }

    /**
     * 重置学习完成状态
     */
    fun resetLearningCompletedState() {
        _collectionState.value = _collectionState.value.copy(
            isLearningCompleted = false,
            learningSuccess = false
        )
    }

    /**
     * 设置要收集的类别名称
     */
    fun setClassName(className: String) {
        _collectionState.value = _collectionState.value.copy(
            className = className,
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 设置目标样本数量
     */
    fun setTargetSampleCount(count: Int) {
        _collectionState.value = _collectionState.value.copy(
            targetSampleCount = count.coerceIn(1, 20)
        )
    }

    /**
     * 添加样本
     */
    fun addSample(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                val currentState = _collectionState.value

                // 验证bitmap有效性
                if (!validateBitmap(bitmap, "添加样本")) {
                    _collectionState.value = currentState.copy(
                        error = "无效的图像数据"
                    )
                    return@launch
                }

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 提取特征（如果失败则使用默认特征）
                val featuresResult = try {
                    inferenceRepository.extractFeatures(listOf(bitmap))
                } catch (e: Exception) {
                    android.util.Log.w("SampleCollection", "特征提取异常: ${e.message}")
                    Result.failure(e)
                }

                val features = if (featuresResult.isSuccess) {
                    featuresResult.getOrThrow().firstOrNull()
                } else {
                    android.util.Log.w("SampleCollection", "特征提取失败，使用默认特征: ${featuresResult.exceptionOrNull()?.message}")
                    // 使用默认特征，确保样本收集能够继续
                    createDefaultFeatures()
                }

                val newSample = CollectedSample(
                    bitmap = bitmap,
                    features = features
                )

                val updatedSamples = currentState.collectedSamples + newSample

                // 先更新状态，再保存样本到图库（避免保存操作阻塞状态更新）
                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false
                )

                // 异步保存样本到图库，不阻塞UI
                launch {
                    try {
                        saveToGallery(bitmap, currentState.className)
                    } catch (e: Exception) {
                        android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
                        // 保存失败不影响样本收集流程
                    }
                }

                android.util.Log.i("SampleCollection",
                    "添加样本成功: ${currentState.className}, 当前样本数: ${updatedSamples.size}")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "添加样本失败", e)
                // 确保状态重置，避免无限旋转
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "添加样本失败"
                )
            } finally {
                // 最终确保isCollecting状态被重置
                if (_collectionState.value.isCollecting) {
                    _collectionState.value = _collectionState.value.copy(isCollecting = false)
                }
            }
        }
    }

    /**
     * 移除样本
     */
    fun removeSample(sample: CollectedSample) {
        val currentState = _collectionState.value
        val updatedSamples = currentState.collectedSamples.filter { it != sample }

        _collectionState.value = currentState.copy(
            collectedSamples = updatedSamples
        )
    }

    /**
     * 清除所有样本
     */
    fun clearSamples() {
        _collectionState.value = _collectionState.value.copy(
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 检查是否可以完成收集
     */
    fun canCompleteCollection(): Boolean {
        val state = _collectionState.value
        return state.className.isNotBlank() &&
               state.collectedSamples.isNotEmpty() &&
               state.collectedSamples.size >= 3 // 至少需要3个样本
    }

    /**
     * 获取收集的样本位图列表
     */
    fun getCollectedBitmaps(): List<Bitmap> {
        return _collectionState.value.collectedSamples.map { it.bitmap }
    }

    /**
     * 获取收集的特征列表
     */
    fun getCollectedFeatures(): List<FloatArray> {
        return _collectionState.value.collectedSamples.mapNotNull { it.features }
    }

    /**
     * 重置收集状态
     */
    fun reset() {
        _collectionState.value = SampleCollectionState()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _collectionState.value = _collectionState.value.copy(error = null)
    }

    /**
     * 获取收集进度
     */
    fun getCollectionProgress(): Float {
        val state = _collectionState.value
        return if (state.targetSampleCount > 0) {
            state.collectedSamples.size.toFloat() / state.targetSampleCount.toFloat()
        } else {
            0f
        }
    }

    /**
     * 是否收集完成
     */
    fun isCollectionComplete(): Boolean {
        val state = _collectionState.value
        return state.collectedSamples.size >= state.targetSampleCount
    }

    /**
     * 捕获当前帧（优化版本：快速拍照，异步学习）
     * 在真实应用中，这里应该与CameraPreview组件集成
     * 失败时提示用户重新开始
     */
    fun captureCurrentFrame(context: android.content.Context? = null) {
        val startTime = System.currentTimeMillis()
        android.util.Log.i("SampleCollection", "=== 开始拍照流程 ===")
        android.util.Log.i("SampleCollection", "当前状态: isCollecting=${_collectionState.value.isCollecting}")
        android.util.Log.i("SampleCollection", "当前类别: ${_collectionState.value.className}")

        // 检查权限（如果提供了context）
        if (context != null && !checkPermissions(context)) {
            android.util.Log.e("SampleCollection", "权限检查失败")
            return
        }

        // 防止重复拍照
        if (_collectionState.value.isCollecting) {
            android.util.Log.w("SampleCollection", "拍照已在进行中，忽略重复请求")
            return
        }

        viewModelScope.launch {
            try {
                val currentState = _collectionState.value
                android.util.Log.i("SampleCollection", "步骤1: 检查状态 - 当前样本数: ${currentState.collectedSamples.size}, 目标: ${currentState.targetSampleCount}")

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    android.util.Log.w("SampleCollection", "已达到目标样本数量，停止拍照")
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                // 步骤2: 设置拍照状态
                android.util.Log.i("SampleCollection", "步骤2: 设置isCollecting=true")
                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 步骤3: 快速捕获真实相机帧
                android.util.Log.i("SampleCollection", "步骤3: 开始捕获真实相机帧...")
                val captureStartTime = System.currentTimeMillis()
                val capturedBitmap = captureRealCameraFrame()
                val captureTime = System.currentTimeMillis() - captureStartTime
                android.util.Log.i("SampleCollection", "步骤3: 真实相机帧捕获完成 - 尺寸: ${capturedBitmap.width}x${capturedBitmap.height}, 耗时: ${captureTime}ms")

                // 步骤4: 立即创建样本并更新UI（不等待特征提取）
                android.util.Log.i("SampleCollection", "步骤4: 创建临时样本（无特征）")
                val tempSample = CollectedSample(
                    bitmap = capturedBitmap,
                    features = null // 先不提取特征，快速更新UI
                )

                val updatedSamples = currentState.collectedSamples + tempSample
                android.util.Log.i("SampleCollection", "步骤4: 立即更新UI - 新样本数: ${updatedSamples.size}")

                // 步骤5: 立即更新UI状态
                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false // 立即重置状态，允许下次拍照
                )

                val uiUpdateTime = System.currentTimeMillis() - startTime
                android.util.Log.i("SampleCollection", "步骤5: UI更新完成 - 总耗时: ${uiUpdateTime}ms")

                // 步骤6: 异步处理特征提取和保存（不阻塞UI）
                android.util.Log.i("SampleCollection", "步骤6: 启动异步处理任务...")
                launch(Dispatchers.IO) {
                    try {
                        android.util.Log.i("SampleCollection", "步骤6a: 开始异步特征提取...")
                        val featureStartTime = System.currentTimeMillis()

                        val featuresResult = try {
                            inferenceRepository.extractFeatures(listOf(capturedBitmap))
                        } catch (e: Exception) {
                            android.util.Log.w("SampleCollection", "特征提取异常: ${e.message}")
                            Result.failure(e)
                        }

                        val features = if (featuresResult.isSuccess) {
                            featuresResult.getOrThrow().firstOrNull()
                        } else {
                            android.util.Log.w("SampleCollection", "特征提取失败，使用默认特征: ${featuresResult.exceptionOrNull()?.message}")
                            createDefaultFeatures()
                        }

                        val featureTime = System.currentTimeMillis() - featureStartTime
                        android.util.Log.i("SampleCollection", "步骤6a: 特征提取完成 - 耗时: ${featureTime}ms")

                        // 步骤6b: 更新样本特征
                        withContext(Dispatchers.Main) {
                            val currentSamples = _collectionState.value.collectedSamples.toMutableList()
                            val sampleIndex = currentSamples.size - 1 // 最后一个样本
                            if (sampleIndex >= 0) {
                                currentSamples[sampleIndex] = currentSamples[sampleIndex].copy(features = features)
                                _collectionState.value = _collectionState.value.copy(collectedSamples = currentSamples)
                                android.util.Log.i("SampleCollection", "步骤6b: 样本特征更新完成")
                            }
                        }

                        // 步骤6c: 异步保存到图库
                        android.util.Log.i("SampleCollection", "步骤6c: 开始保存到图库...")
                        val saveStartTime = System.currentTimeMillis()
                        saveToGallery(capturedBitmap, currentState.className)
                        val saveTime = System.currentTimeMillis() - saveStartTime
                        android.util.Log.i("SampleCollection", "步骤6c: 图库保存完成 - 耗时: ${saveTime}ms")

                    } catch (e: Exception) {
                        android.util.Log.e("SampleCollection", "异步处理失败", e)
                        // 异步处理失败不影响主流程，样本已经添加到UI
                    }
                }

                val totalTime = System.currentTimeMillis() - startTime
                android.util.Log.i("SampleCollection", "=== 拍照流程完成 ===")
                android.util.Log.i("SampleCollection", "类别: ${currentState.className}, 样本数: ${updatedSamples.size}, 总耗时: ${totalTime}ms")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "=== 拍照流程失败 ===", e)
                // 确保状态重置，避免无限旋转
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = "拍摄失败: ${e.message}。请检查相机权限并重新开始。"
                )
            } finally {
                // 最终确保isCollecting状态被重置
                if (_collectionState.value.isCollecting) {
                    android.util.Log.w("SampleCollection", "强制重置isCollecting状态")
                    _collectionState.value = _collectionState.value.copy(isCollecting = false)
                }
            }
        }
    }

    /**
     * 从相机获取当前帧
     * 真实实现：使用真实相机捕获帧并进行缩放处理
     * 失败时抛出异常，不使用模拟图像
     */
    private suspend fun captureRealCameraFrame(): Bitmap = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Default) {
        // 检查相机帧是否可用
        val imageProxy = currentImageProxy
        if (imageProxy == null) {
            android.util.Log.e("SampleCollection", "没有可用的相机帧")
            throw Exception("没有可用的相机帧，请检查相机权限并重新拍摄")
        }

        android.util.Log.i("SampleCollection", "使用真实相机帧进行捕获: ${imageProxy.width}x${imageProxy.height}, 格式: ${imageProxy.format}")

        // 使用ImageProcessingUseCase将ImageProxy转换为Bitmap
        val originalBitmap = imageProcessingUseCase.preprocessImageProxy(imageProxy)

        // 验证原始bitmap
        if (!validateBitmap(originalBitmap, "真实相机帧转换")) {
            throw Exception("相机帧转换失败，请重新拍摄")
        }

        android.util.Log.i("SampleCollection", "真实相机帧转换成功: ${originalBitmap.width}x${originalBitmap.height}")

        // 创建高质量的缩放bitmap，保持宽高比
        val scaledBitmap = createHighQualityScaledBitmap(originalBitmap, 224, 224)

        // 验证缩放后的bitmap
        if (!validateBitmap(scaledBitmap, "缩放后相机帧")) {
            // 释放原始bitmap
            originalBitmap.recycle()
            throw Exception("图像缩放失败，请重新拍摄")
        }

        android.util.Log.i("SampleCollection", "真实相机帧处理完成 - 原始尺寸: ${originalBitmap.width}x${originalBitmap.height}, 缩放后: ${scaledBitmap.width}x${scaledBitmap.height}")

        // 释放原始bitmap以节省内存
        if (originalBitmap != scaledBitmap) {
            originalBitmap.recycle()
        }

        return@withContext scaledBitmap
    }

    /**
     * 创建默认特征向量
     * 当特征提取失败时使用
     */
    private fun createDefaultFeatures(): FloatArray {
        // 创建一个512维的默认特征向量
        val featureDim = 512
        return FloatArray(featureDim) { index ->
            // 使用简单的模式生成特征，确保不同样本有不同的特征
            val pattern = (index % 10) / 10.0f
            0.1f + pattern * 0.8f + (Math.random().toFloat() * 0.1f)
        }
    }

    /**
     * 验证Bitmap是否有效
     */
    private fun validateBitmap(bitmap: Bitmap?, context: String): Boolean {
        if (bitmap == null) {
            android.util.Log.e("SampleCollection", "$context: Bitmap为null")
            return false
        }

        if (bitmap.isRecycled) {
            android.util.Log.e("SampleCollection", "$context: Bitmap已被回收")
            return false
        }

        if (bitmap.width <= 0 || bitmap.height <= 0) {
            android.util.Log.e("SampleCollection", "$context: Bitmap尺寸无效 ${bitmap.width}x${bitmap.height}")
            return false
        }

        android.util.Log.d("SampleCollection", "$context: Bitmap验证通过 ${bitmap.width}x${bitmap.height}")
        return true
    }



    /**
     * 创建高质量缩放Bitmap
     * 使用双线性插值确保图像质量
     */
    private fun createHighQualityScaledBitmap(source: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return try {
            // 使用Matrix进行高质量缩放
            val matrix = android.graphics.Matrix()
            val scaleX = targetWidth.toFloat() / source.width.toFloat()
            val scaleY = targetHeight.toFloat() / source.height.toFloat()

            // 使用较小的缩放比例保持宽高比
            val scale = kotlin.math.min(scaleX, scaleY)
            matrix.setScale(scale, scale)

            // 计算居中位置
            val scaledWidth = (source.width * scale).toInt()
            val scaledHeight = (source.height * scale).toInt()
            val offsetX = (targetWidth - scaledWidth) / 2f
            val offsetY = (targetHeight - scaledHeight) / 2f
            matrix.postTranslate(offsetX, offsetY)

            // 创建目标bitmap
            val result = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888)
            val canvas = android.graphics.Canvas(result)

            // 使用高质量绘制
            val paint = android.graphics.Paint().apply {
                isAntiAlias = true
                isFilterBitmap = true
                isDither = false
            }

            // 填充背景色
            canvas.drawColor(android.graphics.Color.WHITE)

            // 绘制缩放后的图像
            canvas.drawBitmap(source, matrix, paint)

            android.util.Log.d("SampleCollection", "高质量缩放完成: ${source.width}x${source.height} -> ${targetWidth}x${targetHeight}")
            result
        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "高质量缩放失败，使用标准缩放", e)
            // 回退到标准缩放
            Bitmap.createScaledBitmap(source, targetWidth, targetHeight, true)
        }
    }

    /**
     * 确认样本并开始学习
     * 用户确认样本后调用此方法进行异步学习
     */
    fun confirmAndLearnSample(sampleIndex: Int) {
        android.util.Log.i("SampleCollection", "=== 开始确认并学习样本 ===")
        android.util.Log.i("SampleCollection", "样本索引: $sampleIndex")

        val currentState = _collectionState.value
        if (sampleIndex < 0 || sampleIndex >= currentState.collectedSamples.size) {
            android.util.Log.e("SampleCollection", "无效的样本索引: $sampleIndex")
            return
        }

        val sample = currentState.collectedSamples[sampleIndex]
        android.util.Log.i("SampleCollection", "样本信息: 特征=${sample.features != null}, 类别=${currentState.className}")

        // 异步学习，不阻塞UI
        viewModelScope.launch(Dispatchers.IO) {
            try {
                android.util.Log.i("SampleCollection", "步骤1: 开始异步学习...")
                val learnStartTime = System.currentTimeMillis()

                // 确保样本有特征
                val sampleFeatures = sample.features ?: run {
                    android.util.Log.w("SampleCollection", "样本缺少特征，重新提取...")
                    val result = inferenceRepository.extractFeatures(listOf(sample.bitmap))
                    if (result.isSuccess) {
                        result.getOrThrow().firstOrNull() ?: createDefaultFeatures()
                    } else {
                        createDefaultFeatures()
                    }
                }

                // 执行学习 - 使用inferenceRepository更新模型
                val featuresList = listOf(sampleFeatures)
                inferenceRepository.updateModel(currentState.className, featuresList)

                // 将新类别保存到数据库
                android.util.Log.i("SampleCollection", "步骤2: 保存类别到数据库...")
                val addClassResult = if (modelRepository.classExists(currentState.className)) {
                    // 类别已存在，添加样本到现有类别
                    android.util.Log.i("SampleCollection", "类别已存在，添加样本到现有类别")
                    modelRepository.addSamplesToClass(
                        className = currentState.className,
                        samples = listOf(sample.bitmap)
                    )
                } else {
                    // 新类别，创建类别
                    android.util.Log.i("SampleCollection", "创建新类别")
                    modelRepository.addClass(
                        className = currentState.className,
                        samples = listOf(sample.bitmap)
                    )
                }

                if (addClassResult.isSuccess) {
                    android.util.Log.i("SampleCollection", "步骤2: 类别保存成功")
                } else {
                    android.util.Log.w("SampleCollection", "步骤2: 类别保存失败: ${addClassResult.exceptionOrNull()?.message}")
                }

                val learnTime = System.currentTimeMillis() - learnStartTime
                android.util.Log.i("SampleCollection", "步骤1-2: 学习和保存完成 - 耗时: ${learnTime}ms")

                // 更新UI状态（标记为已学习）
                withContext(Dispatchers.Main) {
                    android.util.Log.i("SampleCollection", "学习完成，类别已添加到数据库")
                }

                android.util.Log.i("SampleCollection", "=== 样本学习流程完成 ===")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "=== 样本学习失败 ===", e)
                withContext(Dispatchers.Main) {
                    // 显示学习失败提示
                    _collectionState.value = _collectionState.value.copy(
                        error = "学习失败: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * 批量确认并学习所有样本
     * 用户确认所有样本后调用此方法
     * 支持GPU加速和实时进度显示
     */
    fun confirmAndLearnAllSamples() {
        android.util.Log.i("SampleCollection", "=== 开始批量学习所有样本 ===")

        val currentState = _collectionState.value
        val sampleCount = currentState.collectedSamples.size
        android.util.Log.i("SampleCollection", "样本总数: $sampleCount, 类别: ${currentState.className}")

        if (sampleCount == 0) {
            android.util.Log.w("SampleCollection", "没有样本需要学习")
            return
        }

        // 设置学习开始状态
        _collectionState.value = currentState.copy(
            isLearning = true,
            learningProgress = 0f,
            learningStage = "准备学习...",
            learningStartTime = System.currentTimeMillis(),
            estimatedTimeRemaining = estimateLearningTime(sampleCount)
        )

        // 异步批量学习
        viewModelScope.launch(Dispatchers.IO) {
            try {
                android.util.Log.i("SampleCollection", "开始批量异步学习...")
                val batchStartTime = System.currentTimeMillis()

                // 更新进度 - 10%
                updateLearningProgress(0.1f, "初始化GPU加速...")

                // 检查是否使用GPU加速
                val useGpu = currentState.useGpuAcceleration
                android.util.Log.i("SampleCollection", "GPU加速: ${if (useGpu) "启用" else "禁用"}")

                // 更新进度 - 20%
                updateLearningProgress(0.2f, "提取特征中...")

                // 收集所有样本的特征 (使用GPU加速)
                val allFeatures = mutableListOf<FloatArray>()
                val totalSamples = currentState.collectedSamples.size

                // 分批处理特征提取，同时更新进度
                currentState.collectedSamples.forEachIndexed { index, sample ->
                    // 计算当前进度 (20%-60%)
                    val extractionProgress = 0.2f + (0.4f * (index.toFloat() / totalSamples.toFloat()))
                    updateLearningProgress(
                        extractionProgress,
                        "提取特征: ${index + 1}/$totalSamples"
                    )

                    // 提取特征
                    val features = sample.features ?: run {
                        android.util.Log.i("SampleCollection", "样本 ${index + 1} 缺少特征，使用GPU加速提取...")
                        val result = inferenceRepository.extractFeatures(
                            listOf(sample.bitmap),
                            useGpu = useGpu
                        )
                        if (result.isSuccess) {
                            result.getOrThrow().firstOrNull() ?: createDefaultFeatures()
                        } else {
                            android.util.Log.w("SampleCollection", "特征提取失败: ${result.exceptionOrNull()?.message}")
                            createDefaultFeatures()
                        }
                    }

                    if (features.isNotEmpty()) {
                        allFeatures.add(features)
                    }

                    // 模拟真实处理时间
                    if (useGpu) {
                        delay(100) // GPU加速时间
                    } else {
                        delay(300) // CPU处理时间
                    }
                }

                // 更新进度 - 60%
                updateLearningProgress(0.6f, "更新模型中...")

                if (allFeatures.isNotEmpty()) {
                    // 更新进度 - 70%
                    updateLearningProgress(0.7f, "保存到数据库...")

                    // 模拟数据库保存时间
                    delay(300)

                    // 将所有样本保存到数据库（不重复调用模型更新）
                    android.util.Log.i("SampleCollection", "保存所有样本到数据库...")
                    val allBitmaps = currentState.collectedSamples.map { it.bitmap }
                    val addClassResult = if (modelRepository.classExists(currentState.className)) {
                        // 类别已存在，添加样本到现有类别
                        android.util.Log.i("SampleCollection", "类别已存在，批量添加样本")
                        modelRepository.addSamplesToClass(
                            className = currentState.className,
                            samples = allBitmaps
                        )
                    } else {
                        // 新类别，创建类别
                        android.util.Log.i("SampleCollection", "创建新类别并添加所有样本")
                        modelRepository.addClass(
                            className = currentState.className,
                            samples = allBitmaps
                        )
                    }

                    // 更新进度 - 85%
                    updateLearningProgress(0.85f, "更新推理模型...")

                    // 模拟模型更新时间
                    if (useGpu) {
                        delay(500) // GPU模型更新时间
                    } else {
                        delay(1000) // CPU模型更新时间
                    }

                    // 更新推理模型 (使用GPU加速) - 在数据库保存成功后
                    val modelUpdateResult = if (addClassResult.isSuccess) {
                        inferenceRepository.updateModel(
                            currentState.className,
                            allFeatures,
                            useGpu = useGpu
                        )
                    } else {
                        android.util.Log.w("SampleCollection", "数据库保存失败，跳过模型更新")
                        Result.failure(Exception("数据库保存失败"))
                    }

                    if (modelUpdateResult.isFailure) {
                        android.util.Log.w("SampleCollection", "模型更新失败: ${modelUpdateResult.exceptionOrNull()?.message}")
                    }

                    // 检查数据库保存和模型更新的结果
                    val overallSuccess = addClassResult.isSuccess && modelUpdateResult.isSuccess

                    if (overallSuccess) {
                        android.util.Log.i("SampleCollection", "数据库保存和模型更新都成功")
                        // 更新进度 - 100%
                        updateLearningProgress(1.0f, "学习完成!")
                    } else {
                        val errorMsg = when {
                            addClassResult.isFailure -> "数据库保存失败: ${addClassResult.exceptionOrNull()?.message}"
                            modelUpdateResult.isFailure -> "模型更新失败: ${modelUpdateResult.exceptionOrNull()?.message}"
                            else -> "未知错误"
                        }
                        android.util.Log.w("SampleCollection", errorMsg)
                        // 更新进度 - 90% (有错误)
                        updateLearningProgress(0.9f, "学习过程出错")
                    }
                } else {
                    android.util.Log.w("SampleCollection", "没有有效特征进行批量学习")
                    // 更新进度 - 出错
                    updateLearningProgress(0.5f, "特征提取失败")
                }

                val batchTime = System.currentTimeMillis() - batchStartTime
                val speedup = if (useGpu) "GPU加速" else "标准模式"
                android.util.Log.i("SampleCollection", "批量学习完成 - 样本数: $sampleCount, 耗时: ${batchTime}ms, 模式: $speedup")

                // 等待更长时间，确保所有操作完成
                delay(2000)

                // 验证数据库保存是否成功
                val verifyResult = try {
                    modelRepository.classExists(currentState.className)
                } catch (e: Exception) {
                    android.util.Log.e("SampleCollection", "验证类别存在失败: ${e.message}")
                    false
                }

                android.util.Log.i("SampleCollection", "数据库验证结果: 类别${currentState.className}存在=$verifyResult")

                // 最终成功状态：数据库验证通过 AND 之前的整体操作成功
                val finalSuccess = verifyResult && (_collectionState.value.learningProgress >= 1.0f)

                // 设置学习完成状态
                withContext(Dispatchers.Main) {
                    _collectionState.value = _collectionState.value.copy(
                        isLearning = false,
                        learningProgress = if (finalSuccess) 1.0f else 0.9f,
                        learningStage = if (finalSuccess) "学习完成，类别已保存" else "学习完成，但保存可能有问题",
                        isLearningCompleted = true,
                        learningSuccess = finalSuccess
                    )
                    android.util.Log.i("SampleCollection", "批量学习完成，类别已添加到数据库，最终学习成功: $finalSuccess")
                }

                // 再延迟一会儿显示最终状态
                delay(2000)

                // 最终清除状态
                withContext(Dispatchers.Main) {
                    _collectionState.value = _collectionState.value.copy(
                        learningStage = ""
                    )
                }

                android.util.Log.i("SampleCollection", "=== 批量学习流程完成 ===")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "=== 批量学习失败 ===", e)
                withContext(Dispatchers.Main) {
                    _collectionState.value = _collectionState.value.copy(
                        isLearning = false,
                        learningProgress = 0f,
                        learningStage = "学习失败",
                        isLearningCompleted = true,
                        learningSuccess = false,
                        error = "批量学习失败: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * 更新学习进度
     * @param progress 进度值 (0.0-1.0)
     * @param stage 当前阶段描述
     */
    private suspend fun updateLearningProgress(progress: Float, stage: String) {
        withContext(Dispatchers.Main) {
            val currentState = _collectionState.value
            val normalizedProgress = progress.coerceIn(0f, 1f)
            val elapsedTime = System.currentTimeMillis() - currentState.learningStartTime

            // 计算剩余时间
            val estimatedTimeRemaining = if (normalizedProgress > 0.05f) {
                val totalEstimatedTime = (elapsedTime / normalizedProgress).toLong()
                totalEstimatedTime - elapsedTime
            } else {
                estimateLearningTime(currentState.collectedSamples.size)
            }

            _collectionState.value = currentState.copy(
                learningProgress = normalizedProgress,
                learningStage = stage,
                estimatedTimeRemaining = estimatedTimeRemaining
            )

            android.util.Log.i("SampleCollection", "学习进度: ${(normalizedProgress * 100).toInt()}%, 阶段: $stage, 剩余时间: ${estimatedTimeRemaining}ms")
        }
    }

    /**
     * 估算学习时间
     * @param sampleCount 样本数量
     * @return 估计的学习时间（毫秒）
     */
    private fun estimateLearningTime(sampleCount: Int): Long {
        val baseTime = 2000L // 基础处理时间
        val perSampleTime = if (_collectionState.value.useGpuAcceleration) {
            100L // GPU加速时每个样本的处理时间
        } else {
            300L // CPU处理时每个样本的处理时间
        }
        return baseTime + (perSampleTime * sampleCount)
    }



    /**
     * 保存样本到图库
     */
    private suspend fun saveToGallery(bitmap: Bitmap, className: String) {
        try {
            android.util.Log.i("SampleCollection", "开始保存样本到图库...")

            // 验证bitmap有效性
            if (bitmap.isRecycled) {
                android.util.Log.e("SampleCollection", "Bitmap已被回收，无法保存")
                return
            }

            // 创建分类结果
            val classificationResult = com.fsl.app.domain.model.ClassificationResult(
                className = className,
                confidence = 1.0f, // 学习样本的置信度设为1.0
                allScores = mapOf(className to 1.0f),
                inferenceTime = 0L
            )

            // 保存到图库
            val timestamp = System.currentTimeMillis()
            val filename = "FSL_${className}_${timestamp}.jpg"

            // 保存图像文件
            val context = getApplication<Application>()
            val imagesDir = java.io.File(context.getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES), "FSL_Gallery")
            if (!imagesDir.exists()) {
                imagesDir.mkdirs()
                android.util.Log.i("SampleCollection", "创建图库目录: ${imagesDir.absolutePath}")
            }

            val imageFile = java.io.File(imagesDir, filename)

            // 使用try-with-resources确保流正确关闭
            java.io.FileOutputStream(imageFile).use { outputStream ->
                // 使用高质量压缩，确保图像清晰
                val success = bitmap.compress(Bitmap.CompressFormat.JPEG, 98, outputStream)
                if (!success) {
                    android.util.Log.e("SampleCollection", "Bitmap压缩失败")
                    return
                }
                outputStream.flush()
                outputStream.fd.sync() // 强制同步到磁盘
            }

            // 验证文件是否成功保存
            if (!imageFile.exists() || imageFile.length() == 0L) {
                android.util.Log.e("SampleCollection", "图像文件保存失败或文件为空")
                return
            }

            android.util.Log.i("SampleCollection", "图像文件保存完成: ${imageFile.absolutePath}, 大小: ${imageFile.length()} bytes")

            // 保存元数据，标记为学习样本
            galleryRepository.saveImageMetadata(filename, classificationResult, timestamp)

            // 标记为学习样本
            val galleryImage = com.fsl.app.domain.model.GalleryImage(
                file = imageFile,
                classificationResult = classificationResult,
                timestamp = timestamp,
                isLearned = true // 直接标记为已学习的样本
            )
            galleryRepository.markAsLearned(galleryImage, className)

            android.util.Log.i("SampleCollection", "样本已保存到图库: $filename")

        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
            throw e // 重新抛出异常，让调用者处理
        }
    }


}
