/**
 * 样本收集ViewModel
 *
 * 负责收集训练样本的逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.learning

import android.graphics.Bitmap
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.ImageProcessingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 样本收集状态
 */
data class SampleCollectionState(
    val className: String = "",
    val collectedSamples: List<CollectedSample> = emptyList(),
    val isCollecting: Boolean = false,
    val targetSampleCount: Int = 5,
    val error: String? = null
)

/**
 * 收集的样本数据
 */
data class CollectedSample(
    val bitmap: Bitmap,
    val timestamp: Long = System.currentTimeMillis(),
    val features: FloatArray? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as CollectedSample
        return timestamp == other.timestamp
    }

    override fun hashCode(): Int {
        return timestamp.hashCode()
    }
}

@HiltViewModel
class SampleCollectionViewModel @Inject constructor(
    application: Application,
    private val inferenceRepository: IInferenceRepository,
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase,
    private val galleryRepository: com.fsl.app.data.repository.GalleryRepositoryImpl
) : AndroidViewModel(application) {

    private val _collectionState = MutableStateFlow(SampleCollectionState())
    val collectionState: StateFlow<SampleCollectionState> = _collectionState.asStateFlow()

    /**
     * 设置要收集的类别名称
     */
    fun setClassName(className: String) {
        _collectionState.value = _collectionState.value.copy(
            className = className,
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 设置目标样本数量
     */
    fun setTargetSampleCount(count: Int) {
        _collectionState.value = _collectionState.value.copy(
            targetSampleCount = count.coerceIn(1, 20)
        )
    }

    /**
     * 添加样本
     */
    fun addSample(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                val currentState = _collectionState.value

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 提取特征（如果失败则使用默认特征）
                val featuresResult = try {
                    inferenceRepository.extractFeatures(listOf(bitmap))
                } catch (e: Exception) {
                    android.util.Log.w("SampleCollection", "特征提取异常: ${e.message}")
                    Result.failure(e)
                }

                val features = if (featuresResult.isSuccess) {
                    featuresResult.getOrThrow().firstOrNull()
                } else {
                    android.util.Log.w("SampleCollection", "特征提取失败，使用默认特征: ${featuresResult.exceptionOrNull()?.message}")
                    // 使用默认特征，确保样本收集能够继续
                    createDefaultFeatures()
                }

                val newSample = CollectedSample(
                    bitmap = bitmap,
                    features = features
                )

                val updatedSamples = currentState.collectedSamples + newSample

                // 先更新状态，再保存样本到图库（避免保存操作阻塞状态更新）
                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false
                )

                // 异步保存样本到图库，不阻塞UI
                launch {
                    try {
                        saveToGallery(bitmap, currentState.className)
                    } catch (e: Exception) {
                        android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
                        // 保存失败不影响样本收集流程
                    }
                }

                android.util.Log.i("SampleCollection",
                    "添加样本成功: ${currentState.className}, 当前样本数: ${updatedSamples.size}")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "添加样本失败", e)
                // 确保状态重置，避免无限旋转
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "添加样本失败"
                )
            } finally {
                // 最终确保isCollecting状态被重置
                if (_collectionState.value.isCollecting) {
                    _collectionState.value = _collectionState.value.copy(isCollecting = false)
                }
            }
        }
    }

    /**
     * 移除样本
     */
    fun removeSample(sample: CollectedSample) {
        val currentState = _collectionState.value
        val updatedSamples = currentState.collectedSamples.filter { it != sample }

        _collectionState.value = currentState.copy(
            collectedSamples = updatedSamples
        )
    }

    /**
     * 清除所有样本
     */
    fun clearSamples() {
        _collectionState.value = _collectionState.value.copy(
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 检查是否可以完成收集
     */
    fun canCompleteCollection(): Boolean {
        val state = _collectionState.value
        return state.className.isNotBlank() &&
               state.collectedSamples.isNotEmpty() &&
               state.collectedSamples.size >= 3 // 至少需要3个样本
    }

    /**
     * 获取收集的样本位图列表
     */
    fun getCollectedBitmaps(): List<Bitmap> {
        return _collectionState.value.collectedSamples.map { it.bitmap }
    }

    /**
     * 获取收集的特征列表
     */
    fun getCollectedFeatures(): List<FloatArray> {
        return _collectionState.value.collectedSamples.mapNotNull { it.features }
    }

    /**
     * 重置收集状态
     */
    fun reset() {
        _collectionState.value = SampleCollectionState()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _collectionState.value = _collectionState.value.copy(error = null)
    }

    /**
     * 获取收集进度
     */
    fun getCollectionProgress(): Float {
        val state = _collectionState.value
        return if (state.targetSampleCount > 0) {
            state.collectedSamples.size.toFloat() / state.targetSampleCount.toFloat()
        } else {
            0f
        }
    }

    /**
     * 是否收集完成
     */
    fun isCollectionComplete(): Boolean {
        val state = _collectionState.value
        return state.collectedSamples.size >= state.targetSampleCount
    }

    /**
     * 捕获当前帧（模拟相机拍照）
     * 在真实应用中，这里应该与CameraPreview组件集成
     */
    fun captureCurrentFrame() {
        android.util.Log.i("SampleCollection", "开始拍照，当前状态: isCollecting=${_collectionState.value.isCollecting}")

        viewModelScope.launch {
            try {
                val currentState = _collectionState.value
                android.util.Log.i("SampleCollection", "当前样本数: ${currentState.collectedSamples.size}, 目标: ${currentState.targetSampleCount}")

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    android.util.Log.w("SampleCollection", "已达到目标样本数量")
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                android.util.Log.i("SampleCollection", "设置isCollecting=true")
                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 从相机获取当前帧（真实实现）
                android.util.Log.i("SampleCollection", "从相机获取当前帧")
                val capturedBitmap = captureRealCameraFrame()

                // 提取特征（如果失败则使用默认特征）
                android.util.Log.i("SampleCollection", "开始特征提取")
                val featuresResult = try {
                    inferenceRepository.extractFeatures(listOf(capturedBitmap))
                } catch (e: Exception) {
                    android.util.Log.w("SampleCollection", "特征提取异常: ${e.message}")
                    Result.failure(e)
                }

                android.util.Log.i("SampleCollection", "特征提取结果: ${featuresResult.isSuccess}")
                val features = if (featuresResult.isSuccess) {
                    featuresResult.getOrThrow().firstOrNull()
                } else {
                    android.util.Log.w("SampleCollection", "特征提取失败，使用默认特征: ${featuresResult.exceptionOrNull()?.message}")
                    // 使用默认特征，确保样本收集能够继续
                    createDefaultFeatures()
                }

                android.util.Log.i("SampleCollection", "创建新样本")
                val newSample = CollectedSample(
                    bitmap = capturedBitmap,
                    features = features
                )

                val updatedSamples = currentState.collectedSamples + newSample
                android.util.Log.i("SampleCollection", "更新样本列表，新数量: ${updatedSamples.size}")

                // 先更新状态，再保存样本到图库（避免保存操作阻塞状态更新）
                android.util.Log.i("SampleCollection", "设置isCollecting=false")
                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false
                )

                // 异步保存样本到图库，不阻塞UI
                android.util.Log.i("SampleCollection", "异步保存样本到图库")
                launch {
                    try {
                        saveToGallery(capturedBitmap, currentState.className)
                    } catch (e: Exception) {
                        android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
                        // 保存失败不影响样本收集流程
                    }
                }

                android.util.Log.i("SampleCollection",
                    "拍照样本成功: ${currentState.className}, 当前样本数: ${updatedSamples.size}")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "拍照失败", e)
                // 确保状态重置，避免无限旋转
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "拍照失败"
                )
            } finally {
                // 最终确保isCollecting状态被重置
                if (_collectionState.value.isCollecting) {
                    _collectionState.value = _collectionState.value.copy(isCollecting = false)
                }
            }
        }
    }

    /**
     * 从相机获取当前帧
     * 真实实现：应该与相机预览组件集成
     */
    private suspend fun captureRealCameraFrame(): Bitmap = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Default) {
        try {
            // TODO: 集成真实相机拍照
            // 目前使用简化的实现，创建一个基本的样本图像
            // 在真实应用中，这里应该：
            // 1. 从CameraX或Camera2 API获取当前预览帧
            // 2. 将ImageProxy转换为Bitmap
            // 3. 进行必要的图像预处理（裁剪、缩放、旋转等）

            android.util.Log.i("SampleCollection", "创建基础样本图像（待集成真实相机）")

            // 创建一个基础的224x224图像
            Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
                val canvas = android.graphics.Canvas(this)
                val paint = android.graphics.Paint()

                // 使用当前时间创建不同的样本
                val timestamp = System.currentTimeMillis()
                val variation = (timestamp % 100).toInt()

                // 创建渐变背景
                val colors = intArrayOf(
                    android.graphics.Color.rgb(200 + variation % 55, 200 + (variation * 2) % 55, 200 + (variation * 3) % 55),
                    android.graphics.Color.rgb(150 + variation % 105, 150 + (variation * 2) % 105, 150 + (variation * 3) % 105)
                )

                val gradient = android.graphics.LinearGradient(
                    0f, 0f, 224f, 224f,
                    colors[0], colors[1],
                    android.graphics.Shader.TileMode.CLAMP
                )
                paint.shader = gradient
                canvas.drawRect(0f, 0f, 224f, 224f, paint)

                // 重置shader
                paint.shader = null
                paint.color = android.graphics.Color.WHITE
                paint.alpha = 150

                // 添加一些几何形状来模拟真实物体
                val centerX = 112f
                val centerY = 112f
                val size = 40f + (variation % 30)

                when (variation % 3) {
                    0 -> canvas.drawCircle(centerX, centerY, size, paint)
                    1 -> canvas.drawRect(centerX - size, centerY - size, centerX + size, centerY + size, paint)
                    2 -> canvas.drawOval(centerX - size, centerY - size/2, centerX + size, centerY + size/2, paint)
                }

                // 添加时间戳确保唯一性
                paint.color = android.graphics.Color.BLACK
                paint.alpha = 255
                paint.textSize = 10f
                canvas.drawText("T:${timestamp % 10000}", 5f, 15f, paint)
            }
        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "获取相机帧失败", e)
            // 返回一个错误指示图像
            Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
                val canvas = android.graphics.Canvas(this)
                val paint = android.graphics.Paint()
                paint.color = android.graphics.Color.RED
                canvas.drawRect(0f, 0f, 224f, 224f, paint)
                paint.color = android.graphics.Color.WHITE
                paint.textSize = 16f
                canvas.drawText("ERROR", 80f, 120f, paint)
            }
        }
    }

    /**
     * 创建默认特征向量
     * 当特征提取失败时使用
     */
    private fun createDefaultFeatures(): FloatArray {
        // 创建一个512维的默认特征向量
        val featureDim = 512
        return FloatArray(featureDim) { index ->
            // 使用简单的模式生成特征，确保不同样本有不同的特征
            val pattern = (index % 10) / 10.0f
            0.1f + pattern * 0.8f + (Math.random().toFloat() * 0.1f)
        }
    }

    /**
     * 保存样本到图库
     */
    private suspend fun saveToGallery(bitmap: Bitmap, className: String) {
        try {
            // 创建分类结果
            val classificationResult = com.fsl.app.domain.model.ClassificationResult(
                className = className,
                confidence = 1.0f, // 学习样本的置信度设为1.0
                allScores = mapOf(className to 1.0f),
                inferenceTime = 0L
            )

            // 保存到图库
            val timestamp = System.currentTimeMillis()
            val filename = "FSL_${className}_${timestamp}.jpg"

            // 保存图像文件
            val context = getApplication<Application>()
            val imagesDir = java.io.File(context.getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES), "FSL_Gallery")
            if (!imagesDir.exists()) {
                imagesDir.mkdirs()
            }

            val imageFile = java.io.File(imagesDir, filename)
            val outputStream = java.io.FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            outputStream.flush()
            outputStream.close()

            // 保存元数据，标记为学习样本
            galleryRepository.saveImageMetadata(filename, classificationResult, timestamp)

            // 标记为学习样本
            val galleryImage = com.fsl.app.domain.model.GalleryImage(
                file = imageFile,
                classificationResult = classificationResult,
                timestamp = timestamp,
                isLearned = false
            )
            galleryRepository.markAsLearned(galleryImage, className)

            android.util.Log.i("SampleCollection", "样本已保存到图库: $filename")

        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
        }
    }


}
