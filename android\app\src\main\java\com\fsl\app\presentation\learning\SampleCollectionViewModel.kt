/**
 * 样本收集ViewModel
 *
 * 负责收集训练样本的逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.learning

import android.graphics.Bitmap
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IModelRepository
import com.fsl.app.domain.usecase.ClassificationUseCase
import com.fsl.app.domain.usecase.ImageProcessingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 样本收集状态
 */
data class SampleCollectionState(
    val className: String = "",
    val collectedSamples: List<CollectedSample> = emptyList(),
    val isCollecting: Boolean = false,
    val targetSampleCount: Int = 5,
    val error: String? = null
)

/**
 * 收集的样本数据
 */
data class CollectedSample(
    val bitmap: Bitmap,
    val timestamp: Long = System.currentTimeMillis(),
    val features: FloatArray? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as CollectedSample
        return timestamp == other.timestamp
    }

    override fun hashCode(): Int {
        return timestamp.hashCode()
    }
}

@HiltViewModel
class SampleCollectionViewModel @Inject constructor(
    application: Application,
    private val inferenceRepository: IInferenceRepository,
    private val modelRepository: IModelRepository,
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase,
    private val galleryRepository: com.fsl.app.data.repository.GalleryRepositoryImpl
) : AndroidViewModel(application) {

    private val _collectionState = MutableStateFlow(SampleCollectionState())
    val collectionState: StateFlow<SampleCollectionState> = _collectionState.asStateFlow()

    /**
     * 设置要收集的类别名称
     */
    fun setClassName(className: String) {
        _collectionState.value = _collectionState.value.copy(
            className = className,
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 设置目标样本数量
     */
    fun setTargetSampleCount(count: Int) {
        _collectionState.value = _collectionState.value.copy(
            targetSampleCount = count.coerceIn(1, 20)
        )
    }

    /**
     * 添加样本
     */
    fun addSample(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                val currentState = _collectionState.value

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 提取特征（如果失败则使用默认特征）
                val featuresResult = try {
                    inferenceRepository.extractFeatures(listOf(bitmap))
                } catch (e: Exception) {
                    android.util.Log.w("SampleCollection", "特征提取异常: ${e.message}")
                    Result.failure(e)
                }

                val features = if (featuresResult.isSuccess) {
                    featuresResult.getOrThrow().firstOrNull()
                } else {
                    android.util.Log.w("SampleCollection", "特征提取失败，使用默认特征: ${featuresResult.exceptionOrNull()?.message}")
                    // 使用默认特征，确保样本收集能够继续
                    createDefaultFeatures()
                }

                val newSample = CollectedSample(
                    bitmap = bitmap,
                    features = features
                )

                val updatedSamples = currentState.collectedSamples + newSample

                // 先更新状态，再保存样本到图库（避免保存操作阻塞状态更新）
                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false
                )

                // 异步保存样本到图库，不阻塞UI
                launch {
                    try {
                        saveToGallery(bitmap, currentState.className)
                    } catch (e: Exception) {
                        android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
                        // 保存失败不影响样本收集流程
                    }
                }

                android.util.Log.i("SampleCollection",
                    "添加样本成功: ${currentState.className}, 当前样本数: ${updatedSamples.size}")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "添加样本失败", e)
                // 确保状态重置，避免无限旋转
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "添加样本失败"
                )
            } finally {
                // 最终确保isCollecting状态被重置
                if (_collectionState.value.isCollecting) {
                    _collectionState.value = _collectionState.value.copy(isCollecting = false)
                }
            }
        }
    }

    /**
     * 移除样本
     */
    fun removeSample(sample: CollectedSample) {
        val currentState = _collectionState.value
        val updatedSamples = currentState.collectedSamples.filter { it != sample }

        _collectionState.value = currentState.copy(
            collectedSamples = updatedSamples
        )
    }

    /**
     * 清除所有样本
     */
    fun clearSamples() {
        _collectionState.value = _collectionState.value.copy(
            collectedSamples = emptyList(),
            error = null
        )
    }

    /**
     * 检查是否可以完成收集
     */
    fun canCompleteCollection(): Boolean {
        val state = _collectionState.value
        return state.className.isNotBlank() &&
               state.collectedSamples.isNotEmpty() &&
               state.collectedSamples.size >= 3 // 至少需要3个样本
    }

    /**
     * 获取收集的样本位图列表
     */
    fun getCollectedBitmaps(): List<Bitmap> {
        return _collectionState.value.collectedSamples.map { it.bitmap }
    }

    /**
     * 获取收集的特征列表
     */
    fun getCollectedFeatures(): List<FloatArray> {
        return _collectionState.value.collectedSamples.mapNotNull { it.features }
    }

    /**
     * 重置收集状态
     */
    fun reset() {
        _collectionState.value = SampleCollectionState()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _collectionState.value = _collectionState.value.copy(error = null)
    }

    /**
     * 获取收集进度
     */
    fun getCollectionProgress(): Float {
        val state = _collectionState.value
        return if (state.targetSampleCount > 0) {
            state.collectedSamples.size.toFloat() / state.targetSampleCount.toFloat()
        } else {
            0f
        }
    }

    /**
     * 是否收集完成
     */
    fun isCollectionComplete(): Boolean {
        val state = _collectionState.value
        return state.collectedSamples.size >= state.targetSampleCount
    }

    /**
     * 捕获当前帧（优化版本：快速拍照，异步学习）
     * 在真实应用中，这里应该与CameraPreview组件集成
     */
    fun captureCurrentFrame() {
        val startTime = System.currentTimeMillis()
        android.util.Log.i("SampleCollection", "=== 开始拍照流程 ===")
        android.util.Log.i("SampleCollection", "当前状态: isCollecting=${_collectionState.value.isCollecting}")
        android.util.Log.i("SampleCollection", "当前类别: ${_collectionState.value.className}")

        // 防止重复拍照
        if (_collectionState.value.isCollecting) {
            android.util.Log.w("SampleCollection", "拍照已在进行中，忽略重复请求")
            return
        }

        viewModelScope.launch {
            try {
                val currentState = _collectionState.value
                android.util.Log.i("SampleCollection", "步骤1: 检查状态 - 当前样本数: ${currentState.collectedSamples.size}, 目标: ${currentState.targetSampleCount}")

                if (currentState.collectedSamples.size >= currentState.targetSampleCount) {
                    android.util.Log.w("SampleCollection", "已达到目标样本数量，停止拍照")
                    _collectionState.value = currentState.copy(
                        error = "已达到目标样本数量"
                    )
                    return@launch
                }

                // 步骤2: 设置拍照状态
                android.util.Log.i("SampleCollection", "步骤2: 设置isCollecting=true")
                _collectionState.value = currentState.copy(
                    isCollecting = true,
                    error = null
                )

                // 步骤3: 快速捕获相机帧
                android.util.Log.i("SampleCollection", "步骤3: 开始捕获相机帧...")
                val captureStartTime = System.currentTimeMillis()
                val capturedBitmap = captureRealCameraFrame()
                val captureTime = System.currentTimeMillis() - captureStartTime
                android.util.Log.i("SampleCollection", "步骤3: 相机帧捕获完成 - 尺寸: ${capturedBitmap.width}x${capturedBitmap.height}, 耗时: ${captureTime}ms")

                // 步骤4: 立即创建样本并更新UI（不等待特征提取）
                android.util.Log.i("SampleCollection", "步骤4: 创建临时样本（无特征）")
                val tempSample = CollectedSample(
                    bitmap = capturedBitmap,
                    features = null // 先不提取特征，快速更新UI
                )

                val updatedSamples = currentState.collectedSamples + tempSample
                android.util.Log.i("SampleCollection", "步骤4: 立即更新UI - 新样本数: ${updatedSamples.size}")

                // 步骤5: 立即更新UI状态
                _collectionState.value = currentState.copy(
                    collectedSamples = updatedSamples,
                    isCollecting = false // 立即重置状态，允许下次拍照
                )

                val uiUpdateTime = System.currentTimeMillis() - startTime
                android.util.Log.i("SampleCollection", "步骤5: UI更新完成 - 总耗时: ${uiUpdateTime}ms")

                // 步骤6: 异步处理特征提取和保存（不阻塞UI）
                android.util.Log.i("SampleCollection", "步骤6: 启动异步处理任务...")
                launch(Dispatchers.IO) {
                    try {
                        android.util.Log.i("SampleCollection", "步骤6a: 开始异步特征提取...")
                        val featureStartTime = System.currentTimeMillis()

                        val featuresResult = try {
                            inferenceRepository.extractFeatures(listOf(capturedBitmap))
                        } catch (e: Exception) {
                            android.util.Log.w("SampleCollection", "特征提取异常: ${e.message}")
                            Result.failure(e)
                        }

                        val features = if (featuresResult.isSuccess) {
                            featuresResult.getOrThrow().firstOrNull()
                        } else {
                            android.util.Log.w("SampleCollection", "特征提取失败，使用默认特征: ${featuresResult.exceptionOrNull()?.message}")
                            createDefaultFeatures()
                        }

                        val featureTime = System.currentTimeMillis() - featureStartTime
                        android.util.Log.i("SampleCollection", "步骤6a: 特征提取完成 - 耗时: ${featureTime}ms")

                        // 步骤6b: 更新样本特征
                        withContext(Dispatchers.Main) {
                            val currentSamples = _collectionState.value.collectedSamples.toMutableList()
                            val sampleIndex = currentSamples.size - 1 // 最后一个样本
                            if (sampleIndex >= 0) {
                                currentSamples[sampleIndex] = currentSamples[sampleIndex].copy(features = features)
                                _collectionState.value = _collectionState.value.copy(collectedSamples = currentSamples)
                                android.util.Log.i("SampleCollection", "步骤6b: 样本特征更新完成")
                            }
                        }

                        // 步骤6c: 异步保存到图库
                        android.util.Log.i("SampleCollection", "步骤6c: 开始保存到图库...")
                        val saveStartTime = System.currentTimeMillis()
                        saveToGallery(capturedBitmap, currentState.className)
                        val saveTime = System.currentTimeMillis() - saveStartTime
                        android.util.Log.i("SampleCollection", "步骤6c: 图库保存完成 - 耗时: ${saveTime}ms")

                    } catch (e: Exception) {
                        android.util.Log.e("SampleCollection", "异步处理失败", e)
                        // 异步处理失败不影响主流程，样本已经添加到UI
                    }
                }

                val totalTime = System.currentTimeMillis() - startTime
                android.util.Log.i("SampleCollection", "=== 拍照流程完成 ===")
                android.util.Log.i("SampleCollection", "类别: ${currentState.className}, 样本数: ${updatedSamples.size}, 总耗时: ${totalTime}ms")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "=== 拍照流程失败 ===", e)
                // 确保状态重置，避免无限旋转
                _collectionState.value = _collectionState.value.copy(
                    isCollecting = false,
                    error = e.message ?: "拍照失败"
                )
            } finally {
                // 最终确保isCollecting状态被重置
                if (_collectionState.value.isCollecting) {
                    android.util.Log.w("SampleCollection", "强制重置isCollecting状态")
                    _collectionState.value = _collectionState.value.copy(isCollecting = false)
                }
            }
        }
    }

    /**
     * 从相机获取当前帧
     * 真实实现：应该与相机预览组件集成
     */
    private suspend fun captureRealCameraFrame(): Bitmap = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Default) {
        try {
            // TODO: 集成真实相机拍照
            // 目前使用简化的实现，创建一个基本的样本图像
            // 在真实应用中，这里应该：
            // 1. 从CameraX或Camera2 API获取当前预览帧
            // 2. 将ImageProxy转换为Bitmap
            // 3. 进行必要的图像预处理（裁剪、缩放、旋转等）

            android.util.Log.i("SampleCollection", "创建基础样本图像（待集成真实相机）")

            // 创建一个基础的224x224图像
            Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
                val canvas = android.graphics.Canvas(this)
                val paint = android.graphics.Paint()

                // 使用当前时间创建不同的样本
                val timestamp = System.currentTimeMillis()
                val variation = (timestamp % 100).toInt()

                // 创建渐变背景
                val colors = intArrayOf(
                    android.graphics.Color.rgb(200 + variation % 55, 200 + (variation * 2) % 55, 200 + (variation * 3) % 55),
                    android.graphics.Color.rgb(150 + variation % 105, 150 + (variation * 2) % 105, 150 + (variation * 3) % 105)
                )

                val gradient = android.graphics.LinearGradient(
                    0f, 0f, 224f, 224f,
                    colors[0], colors[1],
                    android.graphics.Shader.TileMode.CLAMP
                )
                paint.shader = gradient
                canvas.drawRect(0f, 0f, 224f, 224f, paint)

                // 重置shader
                paint.shader = null
                paint.color = android.graphics.Color.WHITE
                paint.alpha = 150

                // 添加一些几何形状来模拟真实物体
                val centerX = 112f
                val centerY = 112f
                val size = 40f + (variation % 30)

                when (variation % 3) {
                    0 -> canvas.drawCircle(centerX, centerY, size, paint)
                    1 -> canvas.drawRect(centerX - size, centerY - size, centerX + size, centerY + size, paint)
                    2 -> canvas.drawOval(centerX - size, centerY - size/2, centerX + size, centerY + size/2, paint)
                }

                // 添加时间戳确保唯一性
                paint.color = android.graphics.Color.BLACK
                paint.alpha = 255
                paint.textSize = 10f
                canvas.drawText("T:${timestamp % 10000}", 5f, 15f, paint)
            }
        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "获取相机帧失败", e)
            // 返回一个错误指示图像
            Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
                val canvas = android.graphics.Canvas(this)
                val paint = android.graphics.Paint()
                paint.color = android.graphics.Color.RED
                canvas.drawRect(0f, 0f, 224f, 224f, paint)
                paint.color = android.graphics.Color.WHITE
                paint.textSize = 16f
                canvas.drawText("ERROR", 80f, 120f, paint)
            }
        }
    }

    /**
     * 创建默认特征向量
     * 当特征提取失败时使用
     */
    private fun createDefaultFeatures(): FloatArray {
        // 创建一个512维的默认特征向量
        val featureDim = 512
        return FloatArray(featureDim) { index ->
            // 使用简单的模式生成特征，确保不同样本有不同的特征
            val pattern = (index % 10) / 10.0f
            0.1f + pattern * 0.8f + (Math.random().toFloat() * 0.1f)
        }
    }

    /**
     * 确认样本并开始学习
     * 用户确认样本后调用此方法进行异步学习
     */
    fun confirmAndLearnSample(sampleIndex: Int) {
        android.util.Log.i("SampleCollection", "=== 开始确认并学习样本 ===")
        android.util.Log.i("SampleCollection", "样本索引: $sampleIndex")

        val currentState = _collectionState.value
        if (sampleIndex < 0 || sampleIndex >= currentState.collectedSamples.size) {
            android.util.Log.e("SampleCollection", "无效的样本索引: $sampleIndex")
            return
        }

        val sample = currentState.collectedSamples[sampleIndex]
        android.util.Log.i("SampleCollection", "样本信息: 特征=${sample.features != null}, 类别=${currentState.className}")

        // 异步学习，不阻塞UI
        viewModelScope.launch(Dispatchers.IO) {
            try {
                android.util.Log.i("SampleCollection", "步骤1: 开始异步学习...")
                val learnStartTime = System.currentTimeMillis()

                // 确保样本有特征
                val sampleFeatures = sample.features ?: run {
                    android.util.Log.w("SampleCollection", "样本缺少特征，重新提取...")
                    val result = inferenceRepository.extractFeatures(listOf(sample.bitmap))
                    if (result.isSuccess) {
                        result.getOrThrow().firstOrNull() ?: createDefaultFeatures()
                    } else {
                        createDefaultFeatures()
                    }
                }

                // 执行学习 - 使用inferenceRepository更新模型
                val featuresList = listOf(sampleFeatures)
                inferenceRepository.updateModel(currentState.className, featuresList)

                // 将新类别保存到数据库
                android.util.Log.i("SampleCollection", "步骤2: 保存类别到数据库...")
                val addClassResult = if (modelRepository.classExists(currentState.className)) {
                    // 类别已存在，添加样本到现有类别
                    android.util.Log.i("SampleCollection", "类别已存在，添加样本到现有类别")
                    modelRepository.addSamplesToClass(
                        className = currentState.className,
                        samples = listOf(sample.bitmap)
                    )
                } else {
                    // 新类别，创建类别
                    android.util.Log.i("SampleCollection", "创建新类别")
                    modelRepository.addClass(
                        className = currentState.className,
                        samples = listOf(sample.bitmap)
                    )
                }

                if (addClassResult.isSuccess) {
                    android.util.Log.i("SampleCollection", "步骤2: 类别保存成功")
                } else {
                    android.util.Log.w("SampleCollection", "步骤2: 类别保存失败: ${addClassResult.exceptionOrNull()?.message}")
                }

                val learnTime = System.currentTimeMillis() - learnStartTime
                android.util.Log.i("SampleCollection", "步骤1-2: 学习和保存完成 - 耗时: ${learnTime}ms")

                // 更新UI状态（标记为已学习）
                withContext(Dispatchers.Main) {
                    android.util.Log.i("SampleCollection", "学习完成，类别已添加到数据库")
                }

                android.util.Log.i("SampleCollection", "=== 样本学习流程完成 ===")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "=== 样本学习失败 ===", e)
                withContext(Dispatchers.Main) {
                    // 显示学习失败提示
                    _collectionState.value = _collectionState.value.copy(
                        error = "学习失败: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * 批量确认并学习所有样本
     * 用户确认所有样本后调用此方法
     */
    fun confirmAndLearnAllSamples() {
        android.util.Log.i("SampleCollection", "=== 开始批量学习所有样本 ===")

        val currentState = _collectionState.value
        val sampleCount = currentState.collectedSamples.size
        android.util.Log.i("SampleCollection", "样本总数: $sampleCount, 类别: ${currentState.className}")

        if (sampleCount == 0) {
            android.util.Log.w("SampleCollection", "没有样本需要学习")
            return
        }

        // 异步批量学习
        viewModelScope.launch(Dispatchers.IO) {
            try {
                android.util.Log.i("SampleCollection", "开始批量异步学习...")
                val batchStartTime = System.currentTimeMillis()

                // 收集所有样本的特征
                val allFeatures = currentState.collectedSamples.mapNotNull { sample ->
                    sample.features ?: run {
                        android.util.Log.w("SampleCollection", "样本缺少特征，重新提取...")
                        val result = inferenceRepository.extractFeatures(listOf(sample.bitmap))
                        if (result.isSuccess) {
                            result.getOrThrow().firstOrNull() ?: createDefaultFeatures()
                        } else {
                            createDefaultFeatures()
                        }
                    }
                }.filter { it.isNotEmpty() }

                if (allFeatures.isNotEmpty()) {
                    // 更新推理模型
                    inferenceRepository.updateModel(currentState.className, allFeatures)

                    // 将所有样本保存到数据库
                    android.util.Log.i("SampleCollection", "保存所有样本到数据库...")
                    val allBitmaps = currentState.collectedSamples.map { it.bitmap }
                    val addClassResult = if (modelRepository.classExists(currentState.className)) {
                        // 类别已存在，添加样本到现有类别
                        android.util.Log.i("SampleCollection", "类别已存在，批量添加样本")
                        modelRepository.addSamplesToClass(
                            className = currentState.className,
                            samples = allBitmaps
                        )
                    } else {
                        // 新类别，创建类别
                        android.util.Log.i("SampleCollection", "创建新类别并添加所有样本")
                        modelRepository.addClass(
                            className = currentState.className,
                            samples = allBitmaps
                        )
                    }

                    if (addClassResult.isSuccess) {
                        android.util.Log.i("SampleCollection", "所有样本保存成功")
                    } else {
                        android.util.Log.w("SampleCollection", "样本保存失败: ${addClassResult.exceptionOrNull()?.message}")
                    }
                } else {
                    android.util.Log.w("SampleCollection", "没有有效特征进行批量学习")
                }

                val batchTime = System.currentTimeMillis() - batchStartTime
                android.util.Log.i("SampleCollection", "批量学习完成 - 样本数: $sampleCount, 耗时: ${batchTime}ms")

                // 更新UI状态
                withContext(Dispatchers.Main) {
                    android.util.Log.i("SampleCollection", "批量学习完成，类别已添加到数据库")
                }

                android.util.Log.i("SampleCollection", "=== 批量学习流程完成 ===")

            } catch (e: Exception) {
                android.util.Log.e("SampleCollection", "=== 批量学习失败 ===", e)
                withContext(Dispatchers.Main) {
                    _collectionState.value = _collectionState.value.copy(
                        error = "批量学习失败: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * 保存样本到图库
     */
    private suspend fun saveToGallery(bitmap: Bitmap, className: String) {
        try {
            android.util.Log.i("SampleCollection", "开始保存样本到图库...")

            // 创建分类结果
            val classificationResult = com.fsl.app.domain.model.ClassificationResult(
                className = className,
                confidence = 1.0f, // 学习样本的置信度设为1.0
                allScores = mapOf(className to 1.0f),
                inferenceTime = 0L
            )

            // 保存到图库
            val timestamp = System.currentTimeMillis()
            val filename = "FSL_${className}_${timestamp}.jpg"

            // 保存图像文件
            val context = getApplication<Application>()
            val imagesDir = java.io.File(context.getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES), "FSL_Gallery")
            if (!imagesDir.exists()) {
                imagesDir.mkdirs()
                android.util.Log.i("SampleCollection", "创建图库目录: ${imagesDir.absolutePath}")
            }

            val imageFile = java.io.File(imagesDir, filename)
            val outputStream = java.io.FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            outputStream.flush()
            outputStream.close()

            android.util.Log.i("SampleCollection", "图像文件保存完成: ${imageFile.absolutePath}")

            // 保存元数据，标记为学习样本
            galleryRepository.saveImageMetadata(filename, classificationResult, timestamp)

            // 标记为学习样本
            val galleryImage = com.fsl.app.domain.model.GalleryImage(
                file = imageFile,
                classificationResult = classificationResult,
                timestamp = timestamp,
                isLearned = false // 初始标记为未学习，等待用户确认
            )
            galleryRepository.markAsLearned(galleryImage, className)

            android.util.Log.i("SampleCollection", "样本已保存到图库: $filename")

        } catch (e: Exception) {
            android.util.Log.e("SampleCollection", "保存样本到图库失败", e)
            throw e // 重新抛出异常，让调用者处理
        }
    }


}
