#!/usr/bin/env python3
"""
Android项目编译检查脚本
检查Kotlin代码的语法正确性和依赖关系
"""

import os
import re
import sys
from pathlib import Path

def check_kotlin_syntax(file_path):
    """检查Kotlin文件的基本语法"""
    errors = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    # 检查基本语法
    for i, line in enumerate(lines, 1):
        line = line.strip()
        
        # 检查括号匹配
        if line.count('(') != line.count(')'):
            if not line.endswith('\\') and not any(x in line for x in ['//', '/*', '*/']):
                errors.append(f"第{i}行: 括号不匹配")
        
        # 检查大括号匹配
        if line.count('{') != line.count('}'):
            if not line.endswith('\\') and not any(x in line for x in ['//', '/*', '*/']):
                # 允许单独的开括号或闭括号
                if not (line.count('{') == 1 and line.count('}') == 0) and \
                   not (line.count('{') == 0 and line.count('}') == 1):
                    errors.append(f"第{i}行: 大括号不匹配")
    
    # 检查包声明
    package_pattern = r'package\s+[\w.]+$'
    has_package = False
    for line in lines[:10]:  # 包声明通常在前几行
        if re.match(package_pattern, line.strip()):
            has_package = True
            break
    
    if not has_package and 'package' in content:
        errors.append("包声明格式可能有误")
    
    return errors

def check_imports(file_path):
    """检查导入语句"""
    errors = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    import_pattern = r'import\s+[\w.*]+$'
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if line.startswith('import '):
            if not re.match(import_pattern, line):
                errors.append(f"第{i}行: 导入语句格式错误")
    
    return errors

def check_class_structure(file_path):
    """检查类结构"""
    errors = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查类声明
    class_pattern = r'(class|interface|object)\s+\w+'
    if not re.search(class_pattern, content):
        # 如果没有类声明，检查是否是函数文件
        if '@Composable' not in content and 'fun ' not in content:
            errors.append("文件中没有找到类、接口或对象声明")
    
    return errors

def check_android_manifest():
    """检查AndroidManifest.xml"""
    manifest_path = Path("app/src/main/AndroidManifest.xml")
    if not manifest_path.exists():
        return ["AndroidManifest.xml文件不存在"]
    
    errors = []
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本结构
        if '<manifest' not in content:
            errors.append("AndroidManifest.xml缺少manifest标签")
        
        if '<application' not in content:
            errors.append("AndroidManifest.xml缺少application标签")
        
        if 'android:name=".MainActivity"' not in content and 'MainActivity' not in content:
            errors.append("AndroidManifest.xml中没有找到MainActivity")
            
    except Exception as e:
        errors.append(f"读取AndroidManifest.xml时出错: {e}")
    
    return errors

def check_build_gradle():
    """检查build.gradle文件"""
    build_gradle_path = Path("app/build.gradle")
    if not build_gradle_path.exists():
        return ["app/build.gradle文件不存在"]
    
    errors = []
    try:
        with open(build_gradle_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本配置
        if 'android {' not in content:
            errors.append("build.gradle缺少android配置块")
        
        if 'dependencies {' not in content:
            errors.append("build.gradle缺少dependencies配置块")
        
        if 'compileSdk' not in content:
            errors.append("build.gradle缺少compileSdk配置")
            
    except Exception as e:
        errors.append(f"读取build.gradle时出错: {e}")
    
    return errors

def main():
    """主函数"""
    print("🔍 开始检查Android项目...")
    
    total_errors = 0
    
    # 检查AndroidManifest.xml
    print("\n📋 检查AndroidManifest.xml...")
    manifest_errors = check_android_manifest()
    if manifest_errors:
        print("❌ AndroidManifest.xml错误:")
        for error in manifest_errors:
            print(f"  - {error}")
        total_errors += len(manifest_errors)
    else:
        print("✅ AndroidManifest.xml检查通过")
    
    # 检查build.gradle
    print("\n🔧 检查build.gradle...")
    gradle_errors = check_build_gradle()
    if gradle_errors:
        print("❌ build.gradle错误:")
        for error in gradle_errors:
            print(f"  - {error}")
        total_errors += len(gradle_errors)
    else:
        print("✅ build.gradle检查通过")
    
    # 检查Kotlin文件
    print("\n📝 检查Kotlin源文件...")
    kotlin_files = list(Path("app/src/main/java").rglob("*.kt"))
    
    if not kotlin_files:
        print("❌ 没有找到Kotlin源文件")
        total_errors += 1
    else:
        print(f"找到 {len(kotlin_files)} 个Kotlin文件")
        
        for kt_file in kotlin_files:
            print(f"\n  检查: {kt_file.relative_to(Path.cwd())}")
            
            # 语法检查
            syntax_errors = check_kotlin_syntax(kt_file)
            import_errors = check_imports(kt_file)
            structure_errors = check_class_structure(kt_file)
            
            file_errors = syntax_errors + import_errors + structure_errors
            
            if file_errors:
                print(f"    ❌ 发现 {len(file_errors)} 个问题:")
                for error in file_errors:
                    print(f"      - {error}")
                total_errors += len(file_errors)
            else:
                print("    ✅ 检查通过")
    
    # 总结
    print(f"\n📊 检查完成!")
    print(f"总共发现 {total_errors} 个问题")
    
    if total_errors == 0:
        print("🎉 恭喜！项目结构和语法检查全部通过！")
        print("💡 项目应该可以正常编译")
        return 0
    else:
        print("⚠️  请修复上述问题后再尝试编译")
        return 1

if __name__ == "__main__":
    sys.exit(main())
