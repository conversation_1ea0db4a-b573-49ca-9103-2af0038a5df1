/**
 * 图库界面
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.gallery

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.R
import com.fsl.app.domain.model.GalleryImage
import com.fsl.app.presentation.gallery.GalleryViewModel

/**
 * 图库界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GalleryScreen(
    onNavigateToCamera: () -> Unit = {},
    viewModel: GalleryViewModel = hiltViewModel()
) {
    val galleryState by viewModel.galleryState.collectAsState()
    var showClassSelector by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题和刷新按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.gallery),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            IconButton(onClick = { viewModel.loadGallery() }) {
                Icon(Icons.Default.Refresh, contentDescription = "刷新")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 统计信息
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "总图片",
                    value = galleryState.stats.totalImages.toString()
                )
                StatItem(
                    label = "学习样本",
                    value = galleryState.stats.learnedImages.toString()
                )
                StatItem(
                    label = "分类数",
                    value = galleryState.stats.classCount.toString()
                )
                StatItem(
                    label = "平均置信度",
                    value = "${(galleryState.stats.averageConfidence * 100).toInt()}%"
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 分类选择器
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "分类筛选",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    TextButton(
                        onClick = { showClassSelector = !showClassSelector }
                    ) {
                        Text(galleryState.selectedClass ?: "全部")
                        Icon(
                            if (showClassSelector) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                            contentDescription = null
                        )
                    }
                }

                if (showClassSelector) {
                    Spacer(modifier = Modifier.height(8.dp))

                    LazyColumn(
                        modifier = Modifier.heightIn(max = 200.dp)
                    ) {
                        item {
                            FilterChip(
                                onClick = {
                                    viewModel.selectClass(null)
                                    showClassSelector = false
                                },
                                label = { Text("全部 (${galleryState.stats.totalImages})") },
                                selected = galleryState.selectedClass == null,
                                modifier = Modifier.fillMaxWidth()
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                        }

                        items(galleryState.imagesByClass.keys.toList()) { className ->
                            val count = galleryState.imagesByClass[className]?.size ?: 0
                            FilterChip(
                                onClick = {
                                    viewModel.selectClass(className)
                                    showClassSelector = false
                                },
                                label = { Text("$className ($count)") },
                                selected = galleryState.selectedClass == className,
                                modifier = Modifier.fillMaxWidth()
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 图片网格
        if (galleryState.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            val imagesToShow = viewModel.getSelectedClassImages()

            if (imagesToShow.isEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                Icons.Default.Image,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "暂无图片\n开始拍摄或学习新类别",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            } else {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    items(imagesToShow) { image ->
                        GalleryImageCard(
                            image = image,
                            onDelete = { viewModel.deleteImage(image) },
                            onMarkAsLearned = { className ->
                                viewModel.markAsLearned(image, className)
                            }
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 返回相机按钮
        OutlinedButton(
            onClick = onNavigateToCamera,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("返回相机")
        }
    }

    // 错误提示
    galleryState.error?.let { error ->
        LaunchedEffect(error) {
            // 这里可以显示Snackbar
        }
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 图库图像卡片
 */
@Composable
private fun GalleryImageCard(
    image: GalleryImage,
    onDelete: () -> Unit,
    onMarkAsLearned: (String) -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // 图像显示 - 优化的缩略图加载
            val bitmap = remember(image.file.absolutePath) {
                if (image.file.exists() && image.file.length() > 0) {
                    try {
                        android.util.Log.d("GalleryScreen", "加载缩略图: ${image.file.absolutePath}, 文件大小: ${image.file.length()} bytes")

                        // 第一步：获取图像尺寸
                        val options = android.graphics.BitmapFactory.Options().apply {
                            inJustDecodeBounds = true
                            inPreferredConfig = android.graphics.Bitmap.Config.RGB_565 // 使用更少内存
                        }
                        android.graphics.BitmapFactory.decodeFile(image.file.absolutePath, options)

                        // 检查图像是否有效
                        if (options.outWidth <= 0 || options.outHeight <= 0) {
                            android.util.Log.e("GalleryScreen", "无效的图像尺寸: ${options.outWidth}x${options.outHeight}")
                            return@remember null
                        }

                        // 计算缩放比例
                        val targetSize = 200 // 目标缩略图大小
                        val scaleFactor = kotlin.math.max(
                            1,
                            kotlin.math.max(
                                options.outWidth / targetSize,
                                options.outHeight / targetSize
                            )
                        )

                        android.util.Log.d("GalleryScreen", "原始尺寸: ${options.outWidth}x${options.outHeight}, 缩放因子: $scaleFactor")

                        // 第二步：解码缩略图
                        val decodeOptions = android.graphics.BitmapFactory.Options().apply {
                            inSampleSize = scaleFactor
                            inJustDecodeBounds = false
                            inPreferredConfig = android.graphics.Bitmap.Config.RGB_565
                            inDither = false
                            inPurgeable = true
                            inInputShareable = true
                        }

                        val decodedBitmap = android.graphics.BitmapFactory.decodeFile(image.file.absolutePath, decodeOptions)

                        if (decodedBitmap != null) {
                            android.util.Log.d("GalleryScreen", "缩略图加载成功: ${decodedBitmap.width}x${decodedBitmap.height}")
                        } else {
                            android.util.Log.e("GalleryScreen", "缩略图解码失败")
                        }

                        decodedBitmap
                    } catch (e: Exception) {
                        android.util.Log.e("GalleryScreen", "加载图像失败: ${image.file.absolutePath}", e)
                        null
                    }
                } else {
                    android.util.Log.w("GalleryScreen", "图像文件不存在或为空: ${image.file.absolutePath}")
                    null
                }
            }

            if (bitmap != null) {
                // 验证bitmap并显示
                val imageBitmap = remember(bitmap) {
                    try {
                        if (bitmap.isRecycled) {
                            android.util.Log.e("GalleryScreen", "Bitmap已被回收: ${image.file.name}")
                            null
                        } else {
                            android.util.Log.d("GalleryScreen", "显示图库图像: ${bitmap.width}x${bitmap.height}, 文件: ${image.file.name}")
                            bitmap.asImageBitmap()
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("GalleryScreen", "转换ImageBitmap失败: ${image.file.name}", e)
                        null
                    }
                }

                if (imageBitmap != null) {
                    Image(
                        bitmap = imageBitmap,
                        contentDescription = image.classLabel,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                } else {
                    ImagePlaceholder(
                        errorMessage = "图像转换失败",
                        fileName = image.file.name
                    )
                }
            } else {
                // 改进的占位符，显示错误信息
                ImagePlaceholder(
                    errorMessage = if (!image.file.exists()) "文件不存在" else "加载失败",
                    fileName = image.file.name
                )
            }

            // 学习样本标记
            if (image.isLearned) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(4.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                ) {
                    Text(
                        text = "学习",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }

            // 菜单按钮
            IconButton(
                onClick = { showMenu = true },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .background(
                        MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
                        RoundedCornerShape(50)
                    )
            ) {
                Icon(
                    Icons.Default.MoreVert,
                    contentDescription = "菜单",
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }

            // 分类信息
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .fillMaxWidth()
                    .background(
                        androidx.compose.ui.graphics.Brush.verticalGradient(
                            colors = listOf(
                                androidx.compose.ui.graphics.Color.Transparent,
                                androidx.compose.ui.graphics.Color.Black.copy(alpha = 0.7f)
                            )
                        )
                    )
                    .padding(8.dp)
            ) {
                Column {
                    Text(
                        text = image.classLabel,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        color = androidx.compose.ui.graphics.Color.White
                    )
                    Text(
                        text = image.confidencePercent,
                        style = MaterialTheme.typography.bodySmall,
                        color = androidx.compose.ui.graphics.Color.White.copy(alpha = 0.8f)
                    )
                }
            }

            // 下拉菜单
            DropdownMenu(
                expanded = showMenu,
                onDismissRequest = { showMenu = false }
            ) {
                DropdownMenuItem(
                    text = { Text("删除") },
                    onClick = {
                        onDelete()
                        showMenu = false
                    },
                    leadingIcon = {
                        Icon(Icons.Default.Delete, contentDescription = null)
                    }
                )

                if (!image.isLearned) {
                    DropdownMenuItem(
                        text = { Text("标记为学习样本") },
                        onClick = {
                            onMarkAsLearned(image.classLabel)
                            showMenu = false
                        },
                        leadingIcon = {
                            Icon(Icons.Default.School, contentDescription = null)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 图像占位符
 */
@Composable
private fun ImagePlaceholder(
    errorMessage: String = "图像加载失败",
    fileName: String = ""
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.errorContainer),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                Icons.Default.BrokenImage,
                contentDescription = "图像加载失败",
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = errorMessage,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            if (fileName.isNotEmpty()) {
                Text(
                    text = fileName,
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                    maxLines = 1,
                    overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                )
            }
        }
    }
}
