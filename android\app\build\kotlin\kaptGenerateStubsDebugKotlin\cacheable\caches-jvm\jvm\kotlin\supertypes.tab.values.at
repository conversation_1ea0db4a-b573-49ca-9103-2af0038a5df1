/ Header Record For PersistentHashMapValueStorage androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModel, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel android.app.Application$ #androidx.activity.ComponentActivity, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel0 /com.fsl.app.domain.repository.GalleryRepository9 8com.fsl.app.domain.repository.IImageProcessingRepository/ .com.fsl.app.domain.repository.IModelRepository androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository android.os.Parcelable kotlin.Enum android.os.Parcelable android.os.Parcelable kotlin.Enum android.os.Parcelable androidx.room.RoomDatabase$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult/ .com.fsl.app.domain.repository.IModelRepository$ #androidx.lifecycle.AndroidViewModel3 2com.fsl.app.domain.repository.IInferenceRepository3 2com.fsl.app.domain.repository.IInferenceRepository$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel