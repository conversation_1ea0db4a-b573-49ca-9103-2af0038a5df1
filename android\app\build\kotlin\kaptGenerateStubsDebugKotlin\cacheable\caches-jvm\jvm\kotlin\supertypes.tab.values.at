/ Header Record For PersistentHashMapValueStorage androidx.lifecycle.ViewModel, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult, +com.fsl.app.domain.usecase.ValidationResult androidx.lifecycle.ViewModel android.app.Application$ #androidx.activity.ComponentActivity, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState androidx.lifecycle.ViewModel9 8com.fsl.app.domain.repository.IImageProcessingRepository/ .com.fsl.app.domain.repository.IModelRepository androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository android.os.Parcelable kotlin.Enum android.os.Parcelable androidx.room.RoomDatabase, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState androidx.lifecycle.ViewModel, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository3 2com.fsl.app.domain.repository.IInferenceRepository0 /com.fsl.app.domain.repository.GalleryRepository, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel3 2com.fsl.app.domain.repository.IInferenceRepository android.os.Parcelable kotlin.Enum android.os.Parcelable, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel3 2com.fsl.app.domain.repository.IInferenceRepository3 2com.fsl.app.domain.repository.IInferenceRepository3 2com.fsl.app.domain.repository.IInferenceRepository$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel3 2com.fsl.app.domain.repository.IInferenceRepository3 2com.fsl.app.domain.repository.IInferenceRepository, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState, +com.fsl.app.presentation.camera.CameraState$ #androidx.lifecycle.AndroidViewModel3 2com.fsl.app.domain.repository.IInferenceRepository$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel