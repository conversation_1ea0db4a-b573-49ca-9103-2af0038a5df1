{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "fsl_native", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "fsl_native::@6890427a1f51a3e7e1df", "jsonFile": "target-fsl_native-Debug-336f30bea614fdfc3c01.json", "name": "fsl_native", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a", "source": "F:/geek/fsl/android/app/src/main/cpp"}, "version": {"major": 2, "minor": 3}}