/**
 * 分类用例
 * 
 * 处理图像分类相关的业务逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.usecase

import android.graphics.Bitmap
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.model.BatchClassificationResult
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IImageProcessingRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ClassificationUseCase @Inject constructor(
    private val inferenceRepository: IInferenceRepository,
    private val imageProcessingRepository: IImageProcessingRepository
) {
    
    /**
     * 分类单张图像
     * 
     * @param image 输入图像
     * @return 分类结果
     */
    suspend fun classify(image: Bitmap): Result<ClassificationResult> {
        return withContext(Dispatchers.Default) {
            try {
                // 确保推理引擎已初始化
                if (!inferenceRepository.isInitialized()) {
                    val initResult = inferenceRepository.initialize()
                    if (initResult.isFailure) {
                        return@withContext Result.failure(
                            initResult.exceptionOrNull() ?: Exception("推理引擎初始化失败")
                        )
                    }
                }
                
                // 预处理图像
                val preprocessedImage = imageProcessingRepository.preprocessImage(image)
                
                // 执行推理
                val result = inferenceRepository.classify(preprocessedImage)
                
                result
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * 批量分类图像
     * 
     * @param images 图像列表
     * @return 批量分类结果
     */
    suspend fun classifyBatch(images: List<Bitmap>): Result<BatchClassificationResult> {
        return withContext(Dispatchers.Default) {
            try {
                if (images.isEmpty()) {
                    return@withContext Result.failure(IllegalArgumentException("图像列表不能为空"))
                }
                
                // 确保推理引擎已初始化
                if (!inferenceRepository.isInitialized()) {
                    val initResult = inferenceRepository.initialize()
                    if (initResult.isFailure) {
                        return@withContext Result.failure(
                            initResult.exceptionOrNull() ?: Exception("推理引擎初始化失败")
                        )
                    }
                }
                
                val startTime = System.currentTimeMillis()
                val results = mutableListOf<ClassificationResult>()
                
                // 逐个处理图像
                for (image in images) {
                    val preprocessedImage = imageProcessingRepository.preprocessImage(image)
                    val result = inferenceRepository.classify(preprocessedImage)
                    
                    if (result.isSuccess) {
                        results.add(result.getOrThrow())
                    } else {
                        // 对于失败的分类，添加一个默认结果
                        results.add(
                            ClassificationResult(
                                className = "未知",
                                confidence = 0f,
                                allScores = emptyMap(),
                                inferenceTime = 0L
                            )
                        )
                    }
                }
                
                val totalTime = System.currentTimeMillis() - startTime
                val averageConfidence = results.map { it.confidence }.average().toFloat()
                
                val batchResult = BatchClassificationResult(
                    results = results,
                    totalInferenceTime = totalTime,
                    averageConfidence = averageConfidence
                )
                
                Result.success(batchResult)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * 分类图像并返回前N个结果
     * 
     * @param image 输入图像
     * @param topN 返回前N个结果
     * @return 分类结果
     */
    suspend fun classifyWithTopN(image: Bitmap, topN: Int = 3): Result<ClassificationResult> {
        return withContext(Dispatchers.Default) {
            try {
                val result = classify(image)
                
                if (result.isSuccess) {
                    val classificationResult = result.getOrThrow()
                    val topNScores = classificationResult.allScores.toList()
                        .sortedByDescending { it.second }
                        .take(topN)
                        .toMap()
                    
                    val enhancedResult = classificationResult.copy(
                        allScores = topNScores
                    )
                    
                    Result.success(enhancedResult)
                } else {
                    result
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * 验证分类结果的可信度
     * 
     * @param result 分类结果
     * @param threshold 置信度阈值
     * @return 是否可信
     */
    fun validateClassificationResult(
        result: ClassificationResult,
        threshold: Float = 0.5f
    ): Boolean {
        return result.confidence >= threshold
    }
    
    /**
     * 比较两个分类结果
     * 
     * @param result1 第一个结果
     * @param result2 第二个结果
     * @return 比较结果（正数表示result1更好，负数表示result2更好，0表示相等）
     */
    fun compareClassificationResults(
        result1: ClassificationResult,
        result2: ClassificationResult
    ): Int {
        return result1.confidence.compareTo(result2.confidence)
    }
    
    /**
     * 获取分类建议
     * 
     * @param result 分类结果
     * @return 建议文本
     */
    fun getClassificationSuggestion(result: ClassificationResult): String {
        return when {
            result.confidence >= 0.9f -> "分类结果非常可信"
            result.confidence >= 0.7f -> "分类结果较为可信"
            result.confidence >= 0.5f -> "分类结果一般，建议重新拍摄"
            else -> "分类结果不可信，请重新拍摄或添加更多训练样本"
        }
    }
}
