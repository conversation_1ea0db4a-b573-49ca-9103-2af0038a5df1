{"logs": [{"outputFile": "com.fsl.app-mergeDebugResources-69:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5037e9d4ddaa957f7ac422280b19ed1f\\transformed\\core-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "29,30,31,32,33,34,35,54", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2767,2865,2967,3070,3171,3273,3371,4984", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "2860,2962,3065,3166,3268,3366,3495,5080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72ccbacfecbbba1cbe69ffeb616187eb\\transformed\\jetified-ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "36,37,39,40,42,44,45,46,47,48,49,50,51,53,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3500,3593,3755,3847,4017,4188,4266,4364,4452,4536,4604,4673,4752,4912,5085,5165,5231", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "3588,3671,3842,3941,4100,4261,4359,4447,4531,4599,4668,4747,4828,4979,5160,5226,5344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fe4df33e34c916b774a0ecdee1cee2a\\transformed\\jetified-material3-1.0.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,205", "endColumns": "78,70,82", "endOffsets": "129,200,283"}, "to": {"startLines": "38,41,43", "startColumns": "4,4,4", "startOffsets": "3676,3946,4105", "endColumns": "78,70,82", "endOffsets": "3750,4012,4183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\154a1ba9c71ec06842c95a5522f1809d\\transformed\\appcompat-1.1.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,865,956,1048,1142,1236,1337,1430,1525,1619,1710,1801,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,77,90,91,93,93,100,92,94,93,90,90,77,109,102,95,110,101,109,158,96,78", "endOffsets": "203,300,405,491,591,704,782,860,951,1043,1137,1231,1332,1425,1520,1614,1705,1796,1874,1984,2087,2183,2294,2396,2506,2665,2762,2841"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,865,956,1048,1142,1236,1337,1430,1525,1619,1710,1801,1879,1989,2092,2188,2299,2401,2511,2670,4833", "endColumns": "102,96,104,85,99,112,77,77,90,91,93,93,100,92,94,93,90,90,77,109,102,95,110,101,109,158,96,78", "endOffsets": "203,300,405,491,591,704,782,860,951,1043,1137,1231,1332,1425,1520,1614,1705,1796,1874,1984,2087,2183,2294,2396,2506,2665,2762,4907"}}]}]}