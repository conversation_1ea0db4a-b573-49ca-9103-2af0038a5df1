// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.utils;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class ImageProcessor_Factory implements Factory<ImageProcessor> {
  @Override
  public ImageProcessor get() {
    return newInstance();
  }

  public static ImageProcessor_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ImageProcessor newInstance() {
    return new ImageProcessor();
  }

  private static final class InstanceHolder {
    private static final ImageProcessor_Factory INSTANCE = new ImageProcessor_Factory();
  }
}
