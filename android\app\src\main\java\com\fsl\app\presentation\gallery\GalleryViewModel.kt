/**
 * 图库ViewModel
 *
 * 负责图库相关的业务逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.gallery

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.model.GalleryImage
import com.fsl.app.domain.repository.GalleryRepository
import com.fsl.app.domain.repository.IInferenceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 图库状态
 */
data class GalleryState(
    val isLoading: Boolean = false,
    val allImages: List<GalleryImage> = emptyList(),
    val learnedImages: List<GalleryImage> = emptyList(),
    val imagesByClass: Map<String, List<GalleryImage>> = emptyMap(),
    val selectedClass: String? = null,
    val stats: GalleryStats = GalleryStats(),
    val error: String? = null
)

/**
 * 图库统计信息
 */
data class GalleryStats(
    val totalImages: Int = 0,
    val learnedImages: Int = 0,
    val classCount: Int = 0,
    val averageConfidence: Float = 0f
)

@HiltViewModel
class GalleryViewModel @Inject constructor(
    private val galleryRepository: GalleryRepository,
    private val inferenceRepository: IInferenceRepository
) : ViewModel() {

    private val _galleryState = MutableStateFlow(GalleryState())
    val galleryState: StateFlow<GalleryState> = _galleryState.asStateFlow()

    init {
        loadGallery()
    }

    /**
     * 加载图库
     */
    fun loadGallery() {
        viewModelScope.launch {
            try {
                _galleryState.value = _galleryState.value.copy(
                    isLoading = true,
                    error = null
                )

                // 刷新图库
                galleryRepository.refreshGallery()

                // 获取所有图像
                val allImagesResult = galleryRepository.getAllImages()
                if (allImagesResult.isFailure) {
                    throw Exception("加载图库失败: ${allImagesResult.exceptionOrNull()?.message}")
                }

                val allImages = allImagesResult.getOrThrow()

                // 获取学习样本
                val learnedImagesResult = galleryRepository.getLearnedImages()
                val learnedImages = learnedImagesResult.getOrElse { emptyList() }

                // 按分类组织图像
                val imagesByClass = allImages
                    .filter { it.classificationResult != null }
                    .groupBy { it.classificationResult!!.className }

                // 计算统计信息
                val stats = calculateStats(allImages, learnedImages)

                _galleryState.value = _galleryState.value.copy(
                    isLoading = false,
                    allImages = allImages,
                    learnedImages = learnedImages,
                    imagesByClass = imagesByClass,
                    stats = stats
                )

                android.util.Log.i("GalleryViewModel", 
                    "图库加载完成: 总图像${allImages.size}, 学习图像${learnedImages.size}, 分类${imagesByClass.size}")

            } catch (e: Exception) {
                android.util.Log.e("GalleryViewModel", "加载图库失败", e)
                _galleryState.value = _galleryState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载图库失败"
                )
            }
        }
    }

    /**
     * 选择分类
     */
    fun selectClass(className: String?) {
        _galleryState.value = _galleryState.value.copy(
            selectedClass = className
        )
    }

    /**
     * 删除图像
     */
    fun deleteImage(image: GalleryImage) {
        viewModelScope.launch {
            try {
                val deleteResult = galleryRepository.deleteImage(image)
                if (deleteResult.isFailure) {
                    throw Exception("删除图像失败: ${deleteResult.exceptionOrNull()?.message}")
                }

                // 重新加载图库
                loadGallery()

                android.util.Log.i("GalleryViewModel", "图像删除成功: ${image.fileName}")

            } catch (e: Exception) {
                android.util.Log.e("GalleryViewModel", "删除图像失败", e)
                _galleryState.value = _galleryState.value.copy(
                    error = e.message ?: "删除图像失败"
                )
            }
        }
    }

    /**
     * 标记为学习样本
     */
    fun markAsLearned(image: GalleryImage, className: String) {
        viewModelScope.launch {
            try {
                val markResult = galleryRepository.markAsLearned(image, className)
                if (markResult.isFailure) {
                    throw Exception("标记学习样本失败: ${markResult.exceptionOrNull()?.message}")
                }

                // 重新加载图库
                loadGallery()

                android.util.Log.i("GalleryViewModel", "标记学习样本成功: ${image.fileName} -> $className")

            } catch (e: Exception) {
                android.util.Log.e("GalleryViewModel", "标记学习样本失败", e)
                _galleryState.value = _galleryState.value.copy(
                    error = e.message ?: "标记学习样本失败"
                )
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _galleryState.value = _galleryState.value.copy(error = null)
    }

    /**
     * 获取当前选择的分类图像
     */
    fun getSelectedClassImages(): List<GalleryImage> {
        val state = _galleryState.value
        return if (state.selectedClass != null) {
            state.imagesByClass[state.selectedClass] ?: emptyList()
        } else {
            state.allImages
        }
    }

    /**
     * 计算统计信息
     */
    private fun calculateStats(allImages: List<GalleryImage>, learnedImages: List<GalleryImage>): GalleryStats {
        val totalImages = allImages.size
        val learnedCount = learnedImages.size
        val classCount = allImages
            .mapNotNull { it.classificationResult?.className }
            .distinct()
            .size
        
        val averageConfidence = if (allImages.isNotEmpty()) {
            allImages
                .mapNotNull { it.classificationResult?.confidence }
                .average()
                .toFloat()
        } else {
            0f
        }

        return GalleryStats(
            totalImages = totalImages,
            learnedImages = learnedCount,
            classCount = classCount,
            averageConfidence = averageConfidence
        )
    }

    /**
     * 获取学习的类别列表
     */
    fun getLearnedClasses(): List<String> {
        return _galleryState.value.learnedImages
            .mapNotNull { it.classificationResult?.className }
            .distinct()
            .sorted()
    }
}
