/**
 * Native推理引擎
 *
 * C++推理引擎的Kotlin包装器
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.inference

import android.graphics.Bitmap
import com.fsl.app.domain.model.ClassificationResult
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NativeInferenceEngine @Inject constructor() {

    companion object {
        private var nativeLibraryLoaded = false

        init {
            try {
                System.loadLibrary("fsl_native")
                nativeLibraryLoaded = true
                android.util.Log.i("NativeInferenceEngine", "Native library loaded successfully")
            } catch (e: UnsatisfiedLinkError) {
                nativeLibraryLoaded = false
                android.util.Log.w("NativeInferenceEngine", "Native library not available, using fallback mode", e)
                // 不抛出异常，允许应用继续运行
            }
        }

        fun isNativeLibraryAvailable(): Boolean = nativeLibraryLoaded
    }

    /**
     * 初始化推理引擎
     */
    external fun nativeInitialize(): Boolean

    /**
     * 分类图像
     */
    external fun nativeClassify(imageData: FloatArray, width: Int, height: Int): ClassificationResult?

    /**
     * 提取特征
     */
    external fun nativeExtractFeatures(imageData: FloatArray, width: Int, height: Int): FloatArray?

    /**
     * 添加类别
     */
    external fun nativeAddClass(className: String, features: Array<FloatArray>): Boolean

    /**
     * 更新类别
     */
    external fun nativeUpdateClass(className: String, features: Array<FloatArray>): Boolean

    /**
     * 删除类别
     */
    external fun nativeRemoveClass(className: String): Boolean

    /**
     * 获取类别名称列表
     */
    external fun nativeGetClassNames(): Array<String>

    /**
     * 保存模型
     */
    external fun nativeSaveModel(filePath: String): Boolean

    /**
     * 加载模型
     */
    external fun nativeLoadModel(filePath: String): Boolean

    /**
     * 检查是否已初始化
     */
    external fun nativeIsInitialized(): Boolean

    /**
     * 将Bitmap转换为FloatArray
     */
    fun bitmapToFloatArray(bitmap: Bitmap): FloatArray {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        val floatArray = FloatArray(width * height * 3)

        for (i in pixels.indices) {
            val pixel = pixels[i]
            val r = ((pixel shr 16) and 0xFF) / 255.0f
            val g = ((pixel shr 8) and 0xFF) / 255.0f
            val b = (pixel and 0xFF) / 255.0f

            // RGB格式
            floatArray[i * 3] = r
            floatArray[i * 3 + 1] = g
            floatArray[i * 3 + 2] = b
        }

        return floatArray
    }

    /**
     * 预处理Bitmap
     */
    fun preprocessBitmap(bitmap: Bitmap): Bitmap {
        // 调整到224x224
        return Bitmap.createScaledBitmap(bitmap, 224, 224, true)
    }
}
