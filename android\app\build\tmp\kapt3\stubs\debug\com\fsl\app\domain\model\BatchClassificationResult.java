package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 批量分类结果
 *
 * @property results 所有分类结果列表
 * @property totalInferenceTime 总推理时间
 * @property averageConfidence 平均置信度
 */
@kotlinx.parcelize.Parcelize
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0007\n\u0002\b\f\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B#\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\u000f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\bH\u00c6\u0003J-\u0010\u0013\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u00d6\u0003J\u0006\u0010\u001a\u001a\u00020\bJ\u0010\u0010\u001b\u001a\u00020\u00152\b\b\u0002\u0010\u001c\u001a\u00020\bJ\u0010\u0010\u001d\u001a\u00020\b2\b\b\u0002\u0010\u001c\u001a\u00020\bJ\t\u0010\u001e\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\u0019\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u0015H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006&"}, d2 = {"Lcom/fsl/app/domain/model/BatchClassificationResult;", "Landroid/os/Parcelable;", "results", "", "Lcom/fsl/app/domain/model/ClassificationResult;", "totalInferenceTime", "", "averageConfidence", "", "(Ljava/util/List;JF)V", "getAverageConfidence", "()F", "getResults", "()Ljava/util/List;", "getTotalInferenceTime", "()J", "component1", "component2", "component3", "copy", "describeContents", "", "equals", "", "other", "", "getAverageInferenceTime", "getSuccessCount", "threshold", "getSuccessRate", "hashCode", "toString", "", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
public final class BatchClassificationResult implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.fsl.app.domain.model.ClassificationResult> results = null;
    private final long totalInferenceTime = 0L;
    private final float averageConfidence = 0.0F;
    public static final android.os.Parcelable.Creator<com.fsl.app.domain.model.BatchClassificationResult> CREATOR = null;
    
    /**
     * 批量分类结果
     *
     * @property results 所有分类结果列表
     * @property totalInferenceTime 总推理时间
     * @property averageConfidence 平均置信度
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.BatchClassificationResult copy(@org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.ClassificationResult> results, long totalInferenceTime, float averageConfidence) {
        return null;
    }
    
    /**
     * 批量分类结果
     *
     * @property results 所有分类结果列表
     * @property totalInferenceTime 总推理时间
     * @property averageConfidence 平均置信度
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 批量分类结果
     *
     * @property results 所有分类结果列表
     * @property totalInferenceTime 总推理时间
     * @property averageConfidence 平均置信度
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 批量分类结果
     *
     * @property results 所有分类结果列表
     * @property totalInferenceTime 总推理时间
     * @property averageConfidence 平均置信度
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public BatchClassificationResult(@org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.ClassificationResult> results, long totalInferenceTime, float averageConfidence) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.ClassificationResult> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.ClassificationResult> getResults() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long getTotalInferenceTime() {
        return 0L;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getAverageConfidence() {
        return 0.0F;
    }
    
    /**
     * 获取成功分类的数量
     */
    public final int getSuccessCount(float threshold) {
        return 0;
    }
    
    /**
     * 获取成功率
     */
    public final float getSuccessRate(float threshold) {
        return 0.0F;
    }
    
    /**
     * 获取平均推理时间
     */
    public final float getAverageInferenceTime() {
        return 0.0F;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 3)
    public static final class Creator implements android.os.Parcelable.Creator<com.fsl.app.domain.model.BatchClassificationResult> {
        
        public Creator() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.BatchClassificationResult createFromParcel(@org.jetbrains.annotations.NotNull
        android.os.Parcel in) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.BatchClassificationResult[] newArray(int size) {
            return null;
        }
    }
}