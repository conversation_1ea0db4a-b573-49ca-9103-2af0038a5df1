// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.di;

import com.fsl.app.data.database.AppDatabase;
import com.fsl.app.data.database.LearnedClassDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AppModule_ProvideLearnedClassDaoFactory implements Factory<LearnedClassDao> {
  private final Provider<AppDatabase> databaseProvider;

  public AppModule_ProvideLearnedClassDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public LearnedClassDao get() {
    return provideLearnedClassDao(databaseProvider.get());
  }

  public static AppModule_ProvideLearnedClassDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new AppModule_ProvideLearnedClassDaoFactory(databaseProvider);
  }

  public static LearnedClassDao provideLearnedClassDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideLearnedClassDao(database));
  }
}
