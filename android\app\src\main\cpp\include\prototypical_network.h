/**
 * 原型网络头文件
 * 
 * 原型网络算法的C++实现
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef PROTOTYPICAL_NETWORK_H
#define PROTOTYPICAL_NETWORK_H

#include <vector>
#include <string>
#include <map>

namespace fsl {

using FeatureVector = std::vector<float>;
using FeatureMatrix = std::vector<FeatureVector>;

/**
 * 原型网络类
 * 
 * 实现基于原型的少样本学习分类算法
 */
class PrototypicalNetwork {
public:
    PrototypicalNetwork();
    ~PrototypicalNetwork();
    
    /**
     * 初始化网络
     * @param featureDim 特征维度
     * @return 是否成功
     */
    bool initialize(int featureDim);
    
    /**
     * 添加类别原型
     * @param className 类别名称
     * @param prototype 原型向量
     * @return 是否成功
     */
    bool addPrototype(const std::string& className, const FeatureVector& prototype);
    
    /**
     * 更新类别原型
     * @param className 类别名称
     * @param prototype 新的原型向量
     * @return 是否成功
     */
    bool updatePrototype(const std::string& className, const FeatureVector& prototype);
    
    /**
     * 删除类别原型
     * @param className 类别名称
     * @return 是否成功
     */
    bool removePrototype(const std::string& className);
    
    /**
     * 分类查询特征
     * @param queryFeature 查询特征向量
     * @return 分类结果 (类别名称, 置信度)
     */
    std::pair<std::string, float> classify(const FeatureVector& queryFeature);
    
    /**
     * 计算所有类别的相似度分数
     * @param queryFeature 查询特征向量
     * @return 所有类别的分数映射
     */
    std::map<std::string, float> computeAllScores(const FeatureVector& queryFeature);
    
    /**
     * 从支持样本计算原型
     * @param supportFeatures 支持样本特征矩阵
     * @return 原型向量
     */
    FeatureVector computePrototype(const FeatureMatrix& supportFeatures);
    
    /**
     * 获取所有原型
     * @return 原型映射
     */
    const std::map<std::string, FeatureVector>& getPrototypes() const;
    
    /**
     * 获取特征维度
     * @return 特征维度
     */
    int getFeatureDim() const { return featureDim_; }
    
    /**
     * 获取类别数量
     * @return 类别数量
     */
    int getClassCount() const { return prototypes_.size(); }
    
    /**
     * 清空所有原型
     */
    void clear();

private:
    int featureDim_;
    std::map<std::string, FeatureVector> prototypes_;
    
    /**
     * 计算欧几里得距离
     * @param a 向量a
     * @param b 向量b
     * @return 欧几里得距离
     */
    float euclideanDistance(const FeatureVector& a, const FeatureVector& b);
    
    /**
     * 计算余弦相似度
     * @param a 向量a
     * @param b 向量b
     * @return 余弦相似度
     */
    float cosineSimilarity(const FeatureVector& a, const FeatureVector& b);
    
    /**
     * L2归一化
     * @param vector 输入向量
     * @return 归一化后的向量
     */
    FeatureVector normalizeL2(const FeatureVector& vector);
    
    /**
     * 计算向量的L2范数
     * @param vector 输入向量
     * @return L2范数
     */
    float l2Norm(const FeatureVector& vector);
    
    /**
     * 向量点积
     * @param a 向量a
     * @param b 向量b
     * @return 点积结果
     */
    float dotProduct(const FeatureVector& a, const FeatureVector& b);
    
    /**
     * Softmax函数
     * @param scores 分数向量
     * @return Softmax后的概率分布
     */
    std::vector<float> softmax(const std::vector<float>& scores);
    
    /**
     * 验证特征向量维度
     * @param feature 特征向量
     * @return 是否有效
     */
    bool validateFeatureDim(const FeatureVector& feature);
};

} // namespace fsl

#endif // PROTOTYPICAL_NETWORK_H
