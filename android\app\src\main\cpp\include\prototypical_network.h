/**
 * SOTA原型网络头文件 - 基于easyfsl实现
 *
 * 高性能原型网络算法的C++实现，使用NEON加速
 * 严格按照easyfsl.methods.prototypical_networks实现
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef PROTOTYPICAL_NETWORK_H
#define PROTOTYPICAL_NETWORK_H

#include <vector>
#include <string>
#include <map>
#include <memory>

// ARM NEON支持
#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

namespace fsl {

using FeatureVector = std::vector<float>;
using FeatureMatrix = std::vector<FeatureVector>;

/**
 * SOTA原型网络分类器
 *
 * 基于easyfsl.methods.prototypical_networks.PrototypicalNetworks实现
 * 支持预训练权重和NEON加速
 */
class PrototypicalNetwork {
public:
    PrototypicalNetwork();
    ~PrototypicalNetwork();

    /**
     * 初始化网络 - 基于easyfsl.FewShotClassifier.__init__
     * @param featureDim 特征维度
     * @param useSoftmax 是否使用softmax
     * @param featureCentering 特征中心化向量
     * @param featureNormalization L2归一化参数
     * @return 是否成功
     */
    bool initialize(int featureDim, bool useSoftmax = false,
                   const FeatureVector* featureCentering = nullptr,
                   float featureNormalization = 2.0f);

    /**
     * 加载预训练ImageNet分类权重
     * @param classNames ImageNet类别名称列表
     * @param prototypes 预训练原型向量
     * @return 是否成功
     */
    bool loadPretrainedWeights(const std::vector<std::string>& classNames,
                              const FeatureMatrix& prototypes);

    /**
     * 添加类别原型 (Few-Shot Learning)
     * @param className 类别名称
     * @param prototype 原型特征向量
     * @return 是否成功
     */
    bool addPrototype(const std::string& className, const FeatureVector& prototype);

    /**
     * 更新类别原型
     * @param className 类别名称
     * @param prototype 新的原型特征向量
     * @return 是否成功
     */
    bool updatePrototype(const std::string& className, const FeatureVector& prototype);

    /**
     * 移除类别原型
     * @param className 类别名称
     * @return 是否成功
     */
    bool removePrototype(const std::string& className);

    /**
     * 分类查询样本 - 严格按照easyfsl.PrototypicalNetworks.forward实现
     * @param queryFeature 查询特征向量
     * @return 分类结果 (类别名, 置信度)
     */
    std::pair<std::string, float> classify(const FeatureVector& queryFeature);

    /**
     * 计算所有类别的分数 - 基于easyfsl.l2_distance_to_prototypes
     * @param queryFeature 查询特征向量
     * @return 所有类别的分数映射
     */
    std::map<std::string, float> computeAllScores(const FeatureVector& queryFeature);

    /**
     * 从支持集特征计算原型 - 基于easyfsl.methods.utils.compute_prototypes
     * @param supportFeatures 支持集特征矩阵
     * @return 原型特征向量
     */
    FeatureVector computePrototype(const FeatureMatrix& supportFeatures);

    /**
     * 检查是否有已学习的类别
     * @return 是否有FSL类别
     */
    bool hasLearnedClasses() const;

    /**
     * 检查是否有预训练权重
     * @return 是否有预训练权重
     */
    bool hasPretrainedWeights() const;

    /**
     * 获取所有原型
     * @return 原型映射
     */
    const std::map<std::string, FeatureVector>& getPrototypes() const;

    /**
     * 获取特征维度
     * @return 特征维度
     */
    int getFeatureDim() const { return featureDim_; }

    /**
     * 获取FSL类别数量
     * @return FSL类别数量
     */
    int getFSLClassCount() const { return fslPrototypes_.size(); }

    /**
     * 获取预训练类别数量
     * @return 预训练类别数量
     */
    int getPretrainedClassCount() const { return pretrainedPrototypes_.size(); }

    /**
     * 清除FSL原型（保留预训练权重）
     */
    void clearFSLPrototypes();

    /**
     * 清除所有原型
     */
    void clear();

private:
    int featureDim_;
    bool useSoftmax_;
    FeatureVector featureCentering_;
    float featureNormalization_;

    // FSL原型（用户学习的类别）
    std::map<std::string, FeatureVector> fslPrototypes_;

    // 预训练原型（ImageNet等）
    std::map<std::string, FeatureVector> pretrainedPrototypes_;

    // NEON加速的核心算法
    float l2DistanceNeon(const FeatureVector& a, const FeatureVector& b);
    float dotProductNeon(const FeatureVector& a, const FeatureVector& b);
    void normalizeL2Neon(FeatureVector& vector);
    void featureCenteringNeon(FeatureVector& features);

    // 标准算法（回退）
    float euclideanDistance(const FeatureVector& a, const FeatureVector& b);
    float cosineSimilarity(const FeatureVector& a, const FeatureVector& b);
    FeatureVector normalizeL2(const FeatureVector& vector);
    float l2Norm(const FeatureVector& vector);
    float dotProduct(const FeatureVector& a, const FeatureVector& b);
    std::vector<float> softmax(const std::vector<float>& scores);
    bool validateFeatureDim(const FeatureVector& feature);

    // 工具函数
    bool isNeonAvailable() const;
};

} // namespace fsl

#endif // PROTOTYPICAL_NETWORK_H
