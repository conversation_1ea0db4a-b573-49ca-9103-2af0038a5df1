// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.inference;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class RealTimeInferenceEngine_Factory implements Factory<RealTimeInferenceEngine> {
  private final Provider<NativeInferenceEngine> nativeEngineProvider;

  private final Provider<PyTorchInferenceEngine> pytorchEngineProvider;

  public RealTimeInferenceEngine_Factory(Provider<NativeInferenceEngine> nativeEngineProvider,
      Provider<PyTorchInferenceEngine> pytorchEngineProvider) {
    this.nativeEngineProvider = nativeEngineProvider;
    this.pytorchEngineProvider = pytorchEngineProvider;
  }

  @Override
  public RealTimeInferenceEngine get() {
    return newInstance(nativeEngineProvider.get(), pytorchEngineProvider.get());
  }

  public static RealTimeInferenceEngine_Factory create(
      Provider<NativeInferenceEngine> nativeEngineProvider,
      Provider<PyTorchInferenceEngine> pytorchEngineProvider) {
    return new RealTimeInferenceEngine_Factory(nativeEngineProvider, pytorchEngineProvider);
  }

  public static RealTimeInferenceEngine newInstance(NativeInferenceEngine nativeEngine,
      PyTorchInferenceEngine pytorchEngine) {
    return new RealTimeInferenceEngine(nativeEngine, pytorchEngine);
  }
}
