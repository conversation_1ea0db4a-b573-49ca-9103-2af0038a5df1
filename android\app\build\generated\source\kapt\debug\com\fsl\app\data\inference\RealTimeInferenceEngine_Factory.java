// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.inference;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class RealTimeInferenceEngine_Factory implements Factory<RealTimeInferenceEngine> {
  private final Provider<NativeInferenceEngine> nativeEngineProvider;

  public RealTimeInferenceEngine_Factory(Provider<NativeInferenceEngine> nativeEngineProvider) {
    this.nativeEngineProvider = nativeEngineProvider;
  }

  @Override
  public RealTimeInferenceEngine get() {
    return newInstance(nativeEngineProvider.get());
  }

  public static RealTimeInferenceEngine_Factory create(
      Provider<NativeInferenceEngine> nativeEngineProvider) {
    return new RealTimeInferenceEngine_Factory(nativeEngineProvider);
  }

  public static RealTimeInferenceEngine newInstance(NativeInferenceEngine nativeEngine) {
    return new RealTimeInferenceEngine(nativeEngine);
  }
}
