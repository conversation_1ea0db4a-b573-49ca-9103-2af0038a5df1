{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/androidsdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/androidsdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/androidsdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-166f6c34fe6629f392d7.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-74c46c9025052090d7b7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-364d6a6c7fcc05839935.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-74c46c9025052090d7b7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-364d6a6c7fcc05839935.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-166f6c34fe6629f392d7.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}