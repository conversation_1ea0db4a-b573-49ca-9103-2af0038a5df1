/**
 * 设置ViewModel
 * 
 * 管理应用设置和模型管理
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.usecase.ModelManagementUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val modelManagementUseCase: ModelManagementUseCase
) : ViewModel() {
    
    private val _modelInfo = MutableStateFlow<Map<String, Any>>(emptyMap())
    val modelInfo: StateFlow<Map<String, Any>> = _modelInfo.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _message = MutableStateFlow<String?>(null)
    val message: StateFlow<String?> = _message.asStateFlow()
    
    init {
        loadModelInfo()
    }
    
    /**
     * 加载模型信息
     */
    private fun loadModelInfo() {
        viewModelScope.launch {
            try {
                val result = modelManagementUseCase.getModelInfo()
                if (result.isSuccess) {
                    _modelInfo.value = result.getOrThrow()
                } else {
                    _message.value = "获取模型信息失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _message.value = "获取模型信息失败: ${e.message}"
            }
        }
    }
    
    /**
     * 导出模型
     */
    fun exportModel() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val result = modelManagementUseCase.saveModel()
                if (result.isSuccess) {
                    _message.value = "模型导出成功"
                } else {
                    _message.value = "模型导出失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _message.value = "模型导出失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 导入模型
     */
    fun importModel() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val result = modelManagementUseCase.loadModel()
                if (result.isSuccess) {
                    _message.value = "模型导入成功"
                    loadModelInfo() // 重新加载模型信息
                } else {
                    _message.value = "模型导入失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _message.value = "模型导入失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 重置模型
     */
    fun resetModel() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // 重新初始化模型
                val result = modelManagementUseCase.initializeModel()
                if (result.isSuccess) {
                    _message.value = "模型重置成功"
                    loadModelInfo() // 重新加载模型信息
                } else {
                    _message.value = "模型重置失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _message.value = "模型重置失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _message.value = null
    }
    
    /**
     * 刷新模型信息
     */
    fun refreshModelInfo() {
        loadModelInfo()
    }
}
