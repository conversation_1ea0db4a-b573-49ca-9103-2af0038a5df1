"""
训练工具类
包含训练配置、早停、学习率调度等工具

@author: AI Assistant
@date: 2024
"""

import json
import torch
import torch.nn as nn
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np


@dataclass
class TrainingConfig:
    """训练配置类"""
    
    # 基础训练参数
    num_epochs: int = 100
    learning_rate: float = 0.001
    weight_decay: float = 1e-4
    batch_size: int = 32
    
    # 少样本学习参数
    n_way: int = 5
    n_shot: int = 5
    n_query: int = 15
    n_tasks: int = 1000
    
    # 优化器参数
    optimizer: str = "adam"  # adam, sgd, adamw
    momentum: float = 0.9
    betas: tuple = (0.9, 0.999)
    
    # 学习率调度
    scheduler: str = "step"  # step, cosine, plateau
    step_size: int = 30
    gamma: float = 0.1
    
    # 早停参数
    early_stopping: bool = True
    patience: int = 10
    min_delta: float = 1e-4
    
    # 模型保存
    save_best: bool = True
    save_last: bool = True
    checkpoint_dir: str = "checkpoints"
    
    # 日志和监控
    log_interval: int = 10
    eval_interval: int = 1
    use_tensorboard: bool = True
    
    # 设备和随机种子
    device: str = "auto"  # auto, cpu, cuda
    random_seed: int = 42
    
    # 数据增强
    augmentation: bool = True
    augmentation_level: str = "medium"  # light, medium, heavy
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def save(self, path: str) -> None:
        """保存配置到文件"""
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load(cls, path: str) -> 'TrainingConfig':
        """从文件加载配置"""
        with open(path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        return cls(**config_dict)
    
    def update(self, **kwargs) -> None:
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"未知的配置参数: {key}")


class EarlyStopping:
    """早停机制"""
    
    def __init__(
        self,
        patience: int = 10,
        min_delta: float = 1e-4,
        mode: str = "min",
        restore_best_weights: bool = True,
    ):
        """
        初始化早停机制
        
        Args:
            patience: 容忍的epoch数
            min_delta: 最小改善阈值
            mode: 监控模式 ("min" 或 "max")
            restore_best_weights: 是否恢复最佳权重
        """
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.restore_best_weights = restore_best_weights
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
        
        if mode == "min":
            self.monitor_op = np.less
            self.min_delta *= -1
        elif mode == "max":
            self.monitor_op = np.greater
            self.min_delta *= 1
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def __call__(self, score: float, model: nn.Module) -> bool:
        """
        检查是否应该早停
        
        Args:
            score: 当前监控指标值
            model: 模型
            
        Returns:
            是否应该早停
        """
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(model)
        elif self.monitor_op(score, self.best_score + self.min_delta):
            self.best_score = score
            self.counter = 0
            self.save_checkpoint(model)
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
                if self.restore_best_weights and self.best_weights is not None:
                    model.load_state_dict(self.best_weights)
        
        return self.early_stop
    
    def save_checkpoint(self, model: nn.Module) -> None:
        """保存最佳模型权重"""
        if self.restore_best_weights:
            self.best_weights = model.state_dict().copy()
    
    def reset(self) -> None:
        """重置早停状态"""
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        self.early_stop = False


class LearningRateScheduler:
    """学习率调度器包装器"""
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        scheduler_type: str = "step",
        **kwargs
    ):
        """
        初始化学习率调度器
        
        Args:
            optimizer: 优化器
            scheduler_type: 调度器类型
            **kwargs: 调度器参数
        """
        self.optimizer = optimizer
        self.scheduler_type = scheduler_type
        
        if scheduler_type == "step":
            self.scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=kwargs.get("step_size", 30),
                gamma=kwargs.get("gamma", 0.1)
            )
        elif scheduler_type == "cosine":
            self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=kwargs.get("T_max", 100),
                eta_min=kwargs.get("eta_min", 0)
            )
        elif scheduler_type == "plateau":
            self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode=kwargs.get("mode", "min"),
                factor=kwargs.get("factor", 0.1),
                patience=kwargs.get("patience", 10),
                min_lr=kwargs.get("min_lr", 0)
            )
        elif scheduler_type == "exponential":
            self.scheduler = torch.optim.lr_scheduler.ExponentialLR(
                optimizer,
                gamma=kwargs.get("gamma", 0.95)
            )
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")
    
    def step(self, metric: Optional[float] = None) -> None:
        """执行调度步骤"""
        if self.scheduler_type == "plateau":
            if metric is not None:
                self.scheduler.step(metric)
        else:
            self.scheduler.step()
    
    def get_last_lr(self) -> List[float]:
        """获取当前学习率"""
        return self.scheduler.get_last_lr()


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics = {}
        self.history = {}
    
    def update(self, **kwargs) -> None:
        """更新指标"""
        for key, value in kwargs.items():
            if key not in self.history:
                self.history[key] = []
            self.history[key].append(value)
            self.metrics[key] = value
    
    def get_metric(self, name: str) -> Optional[float]:
        """获取指标值"""
        return self.metrics.get(name)
    
    def get_history(self, name: str) -> List[float]:
        """获取指标历史"""
        return self.history.get(name, [])
    
    def get_best(self, name: str, mode: str = "max") -> float:
        """获取最佳指标值"""
        history = self.get_history(name)
        if not history:
            return None
        
        if mode == "max":
            return max(history)
        elif mode == "min":
            return min(history)
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def reset(self) -> None:
        """重置所有指标"""
        self.metrics.clear()
        self.history.clear()
    
    def save(self, path: str) -> None:
        """保存指标历史"""
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.history, f, indent=2, ensure_ascii=False)
    
    def load(self, path: str) -> None:
        """加载指标历史"""
        with open(path, 'r', encoding='utf-8') as f:
            self.history = json.load(f)
        
        # 更新当前指标为最新值
        for key, values in self.history.items():
            if values:
                self.metrics[key] = values[-1]


class ModelCheckpoint:
    """模型检查点管理器"""
    
    def __init__(
        self,
        checkpoint_dir: str = "checkpoints",
        save_best: bool = True,
        save_last: bool = True,
        monitor: str = "val_accuracy",
        mode: str = "max",
    ):
        """
        初始化检查点管理器
        
        Args:
            checkpoint_dir: 检查点保存目录
            save_best: 是否保存最佳模型
            save_last: 是否保存最新模型
            monitor: 监控指标
            mode: 监控模式
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        self.save_best = save_best
        self.save_last = save_last
        self.monitor = monitor
        self.mode = mode
        
        self.best_score = None
        
        if mode == "max":
            self.monitor_op = np.greater
        elif mode == "min":
            self.monitor_op = np.less
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def save_checkpoint(
        self,
        model: nn.Module,
        optimizer: torch.optim.Optimizer,
        epoch: int,
        metrics: Dict[str, float],
        is_best: bool = False,
    ) -> None:
        """
        保存检查点
        
        Args:
            model: 模型
            optimizer: 优化器
            epoch: 当前epoch
            metrics: 指标字典
            is_best: 是否为最佳模型
        """
        checkpoint = {
            "epoch": epoch,
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "metrics": metrics,
        }
        
        # 保存最新模型
        if self.save_last:
            torch.save(checkpoint, self.checkpoint_dir / "last.pth")
        
        # 保存最佳模型
        if self.save_best and is_best:
            torch.save(checkpoint, self.checkpoint_dir / "best.pth")
        
        # 保存epoch检查点
        torch.save(checkpoint, self.checkpoint_dir / f"epoch_{epoch}.pth")
    
    def should_save_best(self, current_score: float) -> bool:
        """判断是否应该保存为最佳模型"""
        if self.best_score is None:
            self.best_score = current_score
            return True
        
        if self.monitor_op(current_score, self.best_score):
            self.best_score = current_score
            return True
        
        return False
    
    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """加载检查点"""
        return torch.load(checkpoint_path, map_location="cpu")
