package com.fsl.app.data.repository;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ8\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J8\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u0012J\u0019\u0010\u0015\u001a\u00020\u00162\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0017J*\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0019\u0010\u0017J\u0011\u0010\u001a\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001cJ\u0014\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u000f0\u001eH\u0016J\u0019\u0010 \u001a\u00020\u001b2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0017J\u0011\u0010!\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001cR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\""}, d2 = {"Lcom/fsl/app/data/repository/ModelRepository;", "Lcom/fsl/app/domain/repository/IModelRepository;", "database", "Lcom/fsl/app/data/database/AppDatabase;", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "fileManager", "Lcom/fsl/app/data/utils/FileManager;", "(Lcom/fsl/app/data/database/AppDatabase;Lcom/fsl/app/domain/repository/IInferenceRepository;Lcom/fsl/app/data/utils/FileManager;)V", "addClass", "Lkotlin/Result;", "", "className", "", "samples", "", "Landroid/graphics/Bitmap;", "addClass-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addSamplesToClass", "addSamplesToClass-0E7RQCE", "classExists", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteClass", "deleteClass-gIAlu-s", "getClassCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLearnedClasses", "Lkotlinx/coroutines/flow/Flow;", "Lcom/fsl/app/domain/model/LearnedClass;", "getSampleCount", "getTotalSampleCount", "app_debug"})
@javax.inject.Singleton
public final class ModelRepository implements com.fsl.app.domain.repository.IModelRepository {
    private final com.fsl.app.data.database.AppDatabase database = null;
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final com.fsl.app.data.utils.FileManager fileManager = null;
    
    @javax.inject.Inject
    public ModelRepository(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.AppDatabase database, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.utils.FileManager fileManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public kotlinx.coroutines.flow.Flow<java.util.List<com.fsl.app.domain.model.LearnedClass>> getLearnedClasses() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public java.lang.Object classExists(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> continuation) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public java.lang.Object getClassCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public java.lang.Object getTotalSampleCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public java.lang.Object getSampleCount(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation) {
        return null;
    }
}