[{"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\fsl_inference.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\fsl_inference.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\fsl_inference.cpp"}, {"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\prototypical_network.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\prototypical_network.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\prototypical_network.cpp"}, {"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\feature_extractor.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\feature_extractor.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\feature_extractor.cpp"}, {"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\image_processor.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\image_processor.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\image_processor.cpp"}, {"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\nnapi_engine.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\nnapi_engine.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\nnapi_engine.cpp"}, {"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\object_tracker.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\object_tracker.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\object_tracker.cpp"}, {"directory": "F:/geek/fsl/android/app/.cxx/Debug/3v4h1g2t/arm64-v8a", "command": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -o CMakeFiles\\fsl_native.dir\\jni_interface.cpp.o -c F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\jni_interface.cpp", "file": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\jni_interface.cpp"}]