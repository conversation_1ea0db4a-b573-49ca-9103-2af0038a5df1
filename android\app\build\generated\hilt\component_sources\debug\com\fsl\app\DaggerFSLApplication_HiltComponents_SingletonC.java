// Generated by Da<PERSON> (https://dagger.dev).
package com.fsl.app;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.fsl.app.data.database.AppDatabase;
import com.fsl.app.data.inference.NativeInferenceEngine;
import com.fsl.app.data.inference.PyTorchInferenceEngine;
import com.fsl.app.data.repository.GalleryRepositoryImpl;
import com.fsl.app.data.repository.ImageProcessingRepository;
import com.fsl.app.data.repository.ModelRepository;
import com.fsl.app.data.utils.AssetUtils;
import com.fsl.app.data.utils.FileManager;
import com.fsl.app.data.utils.ImageProcessor;
import com.fsl.app.di.AppModule;
import com.fsl.app.di.AppModule_ProvideAppDatabaseFactory;
import com.fsl.app.di.AppModule_ProvideAssetUtilsFactory;
import com.fsl.app.di.AppModule_ProvideFileManagerFactory;
import com.fsl.app.di.AppModule_ProvideImageProcessorFactory;
import com.fsl.app.domain.usecase.ClassificationUseCase;
import com.fsl.app.domain.usecase.ImageProcessingUseCase;
import com.fsl.app.domain.usecase.IncrementalLearningUseCase;
import com.fsl.app.domain.usecase.ModelManagementUseCase;
import com.fsl.app.presentation.MainViewModel;
import com.fsl.app.presentation.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.fsl.app.presentation.camera.CameraViewModel;
import com.fsl.app.presentation.camera.CameraViewModel_HiltModules_KeyModule_ProvideFactory;
import com.fsl.app.presentation.gallery.GalleryViewModel;
import com.fsl.app.presentation.gallery.GalleryViewModel_HiltModules_KeyModule_ProvideFactory;
import com.fsl.app.presentation.learning.LearningViewModel;
import com.fsl.app.presentation.learning.LearningViewModel_HiltModules_KeyModule_ProvideFactory;
import com.fsl.app.presentation.learning.SampleCollectionViewModel;
import com.fsl.app.presentation.learning.SampleCollectionViewModel_HiltModules_KeyModule_ProvideFactory;
import com.fsl.app.presentation.settings.SettingsViewModel;
import com.fsl.app.presentation.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideApplicationFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.SetBuilder;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class DaggerFSLApplication_HiltComponents_SingletonC {
  private DaggerFSLApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder appModule(AppModule appModule) {
      Preconditions.checkNotNull(appModule);
      return this;
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    public FSLApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements FSLApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public FSLApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements FSLApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public FSLApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements FSLApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public FSLApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements FSLApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public FSLApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements FSLApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public FSLApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements FSLApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public FSLApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements FSLApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public FSLApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends FSLApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends FSLApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends FSLApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends FSLApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return SetBuilder.<String>newSetBuilder(6).add(CameraViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(GalleryViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(LearningViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(MainViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SampleCollectionViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).build();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends FSLApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<CameraViewModel> cameraViewModelProvider;

    private Provider<GalleryViewModel> galleryViewModelProvider;

    private Provider<LearningViewModel> learningViewModelProvider;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<SampleCollectionViewModel> sampleCollectionViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.cameraViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.galleryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.learningViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.sampleCollectionViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return MapBuilder.<String, Provider<ViewModel>>newMapBuilder(6).put("com.fsl.app.presentation.camera.CameraViewModel", ((Provider) cameraViewModelProvider)).put("com.fsl.app.presentation.gallery.GalleryViewModel", ((Provider) galleryViewModelProvider)).put("com.fsl.app.presentation.learning.LearningViewModel", ((Provider) learningViewModelProvider)).put("com.fsl.app.presentation.MainViewModel", ((Provider) mainViewModelProvider)).put("com.fsl.app.presentation.learning.SampleCollectionViewModel", ((Provider) sampleCollectionViewModelProvider)).put("com.fsl.app.presentation.settings.SettingsViewModel", ((Provider) settingsViewModelProvider)).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.fsl.app.presentation.camera.CameraViewModel 
          return (T) new CameraViewModel(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule), singletonCImpl.classificationUseCaseProvider.get(), singletonCImpl.imageProcessingUseCaseProvider.get(), singletonCImpl.galleryRepositoryImplProvider.get());

          case 1: // com.fsl.app.presentation.gallery.GalleryViewModel 
          return (T) new GalleryViewModel(singletonCImpl.galleryRepositoryImplProvider.get(), singletonCImpl.pyTorchInferenceEngineProvider.get());

          case 2: // com.fsl.app.presentation.learning.LearningViewModel 
          return (T) new LearningViewModel(singletonCImpl.classificationUseCaseProvider.get(), singletonCImpl.incrementalLearningUseCaseProvider.get(), singletonCImpl.pyTorchInferenceEngineProvider.get());

          case 3: // com.fsl.app.presentation.MainViewModel 
          return (T) new MainViewModel(singletonCImpl.modelManagementUseCaseProvider.get());

          case 4: // com.fsl.app.presentation.learning.SampleCollectionViewModel 
          return (T) new SampleCollectionViewModel(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule), singletonCImpl.pyTorchInferenceEngineProvider.get(), singletonCImpl.classificationUseCaseProvider.get(), singletonCImpl.galleryRepositoryImplProvider.get());

          case 5: // com.fsl.app.presentation.settings.SettingsViewModel 
          return (T) new SettingsViewModel(singletonCImpl.modelManagementUseCaseProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends FSLApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends FSLApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends FSLApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<ImageProcessor> provideImageProcessorProvider;

    private Provider<AssetUtils> provideAssetUtilsProvider;

    private Provider<NativeInferenceEngine> provideNativeInferenceEngineProvider;

    private Provider<PyTorchInferenceEngine> pyTorchInferenceEngineProvider;

    private Provider<ImageProcessingRepository> imageProcessingRepositoryProvider;

    private Provider<ClassificationUseCase> classificationUseCaseProvider;

    private Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider;

    private Provider<GalleryRepositoryImpl> galleryRepositoryImplProvider;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<FileManager> provideFileManagerProvider;

    private Provider<ModelRepository> modelRepositoryProvider;

    private Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider;

    private Provider<ModelManagementUseCase> modelManagementUseCaseProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideImageProcessorProvider = DoubleCheck.provider(new SwitchingProvider<ImageProcessor>(singletonCImpl, 2));
      this.provideAssetUtilsProvider = DoubleCheck.provider(new SwitchingProvider<AssetUtils>(singletonCImpl, 3));
      this.provideNativeInferenceEngineProvider = DoubleCheck.provider(new SwitchingProvider<NativeInferenceEngine>(singletonCImpl, 4));
      this.pyTorchInferenceEngineProvider = DoubleCheck.provider(new SwitchingProvider<PyTorchInferenceEngine>(singletonCImpl, 1));
      this.imageProcessingRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ImageProcessingRepository>(singletonCImpl, 5));
      this.classificationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ClassificationUseCase>(singletonCImpl, 0));
      this.imageProcessingUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ImageProcessingUseCase>(singletonCImpl, 6));
      this.galleryRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<GalleryRepositoryImpl>(singletonCImpl, 7));
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 10));
      this.provideFileManagerProvider = DoubleCheck.provider(new SwitchingProvider<FileManager>(singletonCImpl, 11));
      this.modelRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ModelRepository>(singletonCImpl, 9));
      this.incrementalLearningUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<IncrementalLearningUseCase>(singletonCImpl, 8));
      this.modelManagementUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ModelManagementUseCase>(singletonCImpl, 12));
    }

    @Override
    public void injectFSLApplication(FSLApplication fSLApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.fsl.app.domain.usecase.ClassificationUseCase 
          return (T) new ClassificationUseCase(singletonCImpl.pyTorchInferenceEngineProvider.get(), singletonCImpl.imageProcessingRepositoryProvider.get());

          case 1: // com.fsl.app.data.inference.PyTorchInferenceEngine 
          return (T) new PyTorchInferenceEngine(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideImageProcessorProvider.get(), singletonCImpl.provideAssetUtilsProvider.get(), singletonCImpl.provideNativeInferenceEngineProvider.get());

          case 2: // com.fsl.app.data.utils.ImageProcessor 
          return (T) AppModule_ProvideImageProcessorFactory.provideImageProcessor();

          case 3: // com.fsl.app.data.utils.AssetUtils 
          return (T) AppModule_ProvideAssetUtilsFactory.provideAssetUtils(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.fsl.app.data.inference.NativeInferenceEngine 
          return (T) AppModule.INSTANCE.provideNativeInferenceEngine();

          case 5: // com.fsl.app.data.repository.ImageProcessingRepository 
          return (T) new ImageProcessingRepository(singletonCImpl.provideImageProcessorProvider.get());

          case 6: // com.fsl.app.domain.usecase.ImageProcessingUseCase 
          return (T) new ImageProcessingUseCase(singletonCImpl.imageProcessingRepositoryProvider.get());

          case 7: // com.fsl.app.data.repository.GalleryRepositoryImpl 
          return (T) new GalleryRepositoryImpl(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.fsl.app.domain.usecase.IncrementalLearningUseCase 
          return (T) new IncrementalLearningUseCase(singletonCImpl.modelRepositoryProvider.get(), singletonCImpl.pyTorchInferenceEngineProvider.get(), singletonCImpl.imageProcessingRepositoryProvider.get());

          case 9: // com.fsl.app.data.repository.ModelRepository 
          return (T) new ModelRepository(singletonCImpl.provideAppDatabaseProvider.get(), singletonCImpl.pyTorchInferenceEngineProvider.get(), singletonCImpl.provideFileManagerProvider.get());

          case 10: // com.fsl.app.data.database.AppDatabase 
          return (T) AppModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.fsl.app.data.utils.FileManager 
          return (T) AppModule_ProvideFileManagerFactory.provideFileManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 12: // com.fsl.app.domain.usecase.ModelManagementUseCase 
          return (T) new ModelManagementUseCase(singletonCImpl.pyTorchInferenceEngineProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
