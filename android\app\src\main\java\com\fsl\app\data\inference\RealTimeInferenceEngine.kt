/**
 * 实时推理引擎
 *
 * 专门处理实时推理，将推理和UI线程分离，避免卡顿和闪屏
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.inference

import android.graphics.Bitmap
import android.util.Log
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.model.TrackingResult
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 推理请求
 */
private data class InferenceRequest(
    val bitmap: Bitmap,
    val timestamp: Long,
    val requestId: Long
)

/**
 * 推理结果
 */
data class InferenceResult(
    val classificationResult: ClassificationResult?,
    val trackingResults: List<TrackingResult>,
    val timestamp: Long,
    val requestId: Long,
    val inferenceTime: Long
)

@Singleton
class RealTimeInferenceEngine @Inject constructor(
    private val nativeEngine: NativeInferenceEngine?,
    private val pytorchEngine: PyTorchInferenceEngine
) {
    companion object {
        private const val TAG = "RealTimeInferenceEngine"
        private const val MAX_QUEUE_SIZE = 1 // 只保留最新的请求，避免积压
        private const val INFERENCE_TIMEOUT_MS = 50L // 推理超时时间
        private const val TARGET_FPS = 15 // 目标帧率
        private const val FRAME_INTERVAL_MS = 1000L / TARGET_FPS // 帧间隔
    }

    // 推理线程和协程作用域
    private val inferenceScope = CoroutineScope(
        Dispatchers.Default + SupervisorJob() +
        CoroutineName("InferenceEngine")
    )

    // 推理请求队列
    private val inferenceQueue = Channel<InferenceRequest>(capacity = MAX_QUEUE_SIZE)

    // 推理结果流
    private val _inferenceResults = MutableStateFlow<InferenceResult?>(null)
    val inferenceResults: StateFlow<InferenceResult?> = _inferenceResults.asStateFlow()

    // 性能统计
    private val _performanceStats = MutableStateFlow(PerformanceStats())
    val performanceStats: StateFlow<PerformanceStats> = _performanceStats.asStateFlow()

    // 引擎状态
    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning.asStateFlow()

    private val isInitialized = AtomicBoolean(false)
    private var requestIdCounter = 0L
    private var lastFrameTime = 0L

    // 性能统计数据类
    data class PerformanceStats(
        val avgInferenceTime: Double = 0.0,
        val fps: Double = 0.0,
        val totalInferences: Int = 0,
        val droppedFrames: Int = 0,
        val queueSize: Int = 0,
        val isNativeEngineAvailable: Boolean = false
    )

    /**
     * 初始化推理引擎
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.Default) {
        if (isInitialized.get()) {
            Log.i(TAG, "推理引擎已初始化")
            return@withContext true
        }

        try {
            Log.i(TAG, "初始化实时推理引擎...")

            // 检查native引擎是否可用
            val nativeAvailable = nativeEngine?.nativeIsNNAPIAvailable() == true
            Log.i(TAG, "Native引擎可用: $nativeAvailable")

            // 更新性能统计
            _performanceStats.value = _performanceStats.value.copy(
                isNativeEngineAvailable = nativeAvailable
            )

            // 启动推理工作线程
            startInferenceWorker()

            isInitialized.set(true)
            Log.i(TAG, "实时推理引擎初始化成功")
            return@withContext true

        } catch (e: Exception) {
            Log.e(TAG, "初始化推理引擎失败", e)
            return@withContext false
        }
    }

    /**
     * 启动推理引擎
     */
    fun start() {
        if (!isInitialized.get()) {
            Log.w(TAG, "推理引擎未初始化")
            return
        }

        _isRunning.value = true
        Log.i(TAG, "推理引擎已启动")
    }

    /**
     * 停止推理引擎
     */
    fun stop() {
        _isRunning.value = false
        Log.i(TAG, "推理引擎已停止")
    }

    /**
     * 提交推理请求（非阻塞，带帧率控制）
     */
    fun submitInference(bitmap: Bitmap): Boolean {
        if (!_isRunning.value) {
            return false
        }

        // 帧率控制：限制提交频率
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastFrameTime < FRAME_INTERVAL_MS) {
            // 跳过这一帧，维持目标帧率
            return false
        }
        lastFrameTime = currentTime

        val request = InferenceRequest(
            bitmap = bitmap,
            timestamp = currentTime,
            requestId = ++requestIdCounter
        )

        // 清空队列，只保留最新的请求
        while (inferenceQueue.tryReceive().isSuccess) {
            _performanceStats.value = _performanceStats.value.copy(
                droppedFrames = _performanceStats.value.droppedFrames + 1
            )
        }

        // 提交新请求
        val success = inferenceQueue.trySend(request).isSuccess

        if (!success) {
            _performanceStats.value = _performanceStats.value.copy(
                droppedFrames = _performanceStats.value.droppedFrames + 1
            )
            Log.d(TAG, "推理队列已满，丢弃帧")
        }

        return success
    }

    /**
     * 启动推理工作线程
     */
    private fun startInferenceWorker() {
        inferenceScope.launch {
            Log.i(TAG, "推理工作线程已启动")

            val inferenceTimes = mutableListOf<Long>()
            var lastFpsUpdate = System.currentTimeMillis()
            var frameCount = 0

            try {
                while (isActive) {
                    try {
                        // 从队列中获取推理请求（非阻塞）
                        val request = withTimeoutOrNull(100) {
                            inferenceQueue.receive()
                        } ?: continue

                        if (!_isRunning.value) {
                            continue
                        }

                        val startTime = System.currentTimeMillis()

                        // 执行推理
                        val result = performInference(request)

                        val inferenceTime = System.currentTimeMillis() - startTime
                        inferenceTimes.add(inferenceTime)

                        // 保持最近100次推理时间
                        if (inferenceTimes.size > 100) {
                            inferenceTimes.removeAt(0)
                        }

                        // 发布结果
                        _inferenceResults.value = result

                        frameCount++

                        // 每秒更新一次性能统计
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastFpsUpdate >= 1000) {
                            val fps = frameCount * 1000.0 / (currentTime - lastFpsUpdate)
                            val avgInferenceTime = inferenceTimes.average()

                            _performanceStats.value = _performanceStats.value.copy(
                                avgInferenceTime = avgInferenceTime,
                                fps = fps,
                                totalInferences = _performanceStats.value.totalInferences + frameCount,
                                queueSize = inferenceQueue.tryReceive().getOrNull()?.let { 1 } ?: 0
                            )

                            frameCount = 0
                            lastFpsUpdate = currentTime

                            Log.d(TAG, "性能统计: FPS=${String.format("%.1f", fps)}, " +
                                    "推理时间=${String.format("%.1f", avgInferenceTime)}ms")
                        }

                        Log.d(TAG, "推理完成: ${request.requestId}, 耗时: ${inferenceTime}ms")

                    } catch (e: Exception) {
                        Log.e(TAG, "推理过程中发生错误", e)
                    }
                }
            } finally {
                Log.i(TAG, "推理工作线程已停止")
            }
        }
    }

    /**
     * 执行实际推理 - 完全按照easyfsl pipeline
     */
    private suspend fun performInference(request: InferenceRequest): InferenceResult = withContext(Dispatchers.Default) {
        try {
            // 检查请求是否过期
            val currentTime = System.currentTimeMillis()
            if (currentTime - request.timestamp > INFERENCE_TIMEOUT_MS) {
                Log.d(TAG, "推理请求已过期: ${request.requestId}")
                return@withContext createEmptyResult(request.requestId, currentTime)
            }

            val startTime = System.currentTimeMillis()

            // 按照easyfsl的FewShotClassifier.forward流程进行推理
            val classificationResult = performFewShotClassification(request.bitmap)

            // 实现简单的目标跟踪（基于分类结果生成边界框）
            val trackingResults = generateTrackingResults(classificationResult, request.bitmap)

            val inferenceTime = System.currentTimeMillis() - startTime

            return@withContext InferenceResult(
                classificationResult = classificationResult,
                trackingResults = trackingResults,
                timestamp = currentTime,
                requestId = request.requestId,
                inferenceTime = inferenceTime
            )

        } catch (e: Exception) {
            Log.e(TAG, "推理执行失败", e)
            return@withContext createEmptyResult(request.requestId, System.currentTimeMillis())
        }
    }

    /**
     * 执行Few-Shot分类 - 严格按照easyfsl流程
     * 1. compute_features(query_images)
     * 2. l2_distance_to_prototypes(query_features)
     * 3. softmax_if_specified(scores)
     */
    private suspend fun performFewShotClassification(bitmap: Bitmap): ClassificationResult? {
        return try {
            // 检查是否有已学习的支持集
            if (!pytorchEngine.hasLearnedClasses()) {
                Log.d(TAG, "没有已学习的类别，跳过分类")
                return null
            }

            // 使用PyTorchInferenceEngine进行标准的few-shot分类
            val result = pytorchEngine.classify(bitmap)
            if (result.isSuccess) {
                result.getOrNull()
            } else {
                Log.w(TAG, "Few-shot分类失败: ${result.exceptionOrNull()?.message}")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Few-shot分类异常", e)
            null
        }
    }

    /**
     * 生成跟踪结果 - 基于分类结果创建边界框
     */
    private fun generateTrackingResults(
        classificationResult: ClassificationResult?,
        bitmap: Bitmap
    ): List<TrackingResult> {
        if (classificationResult == null || classificationResult.confidence < 0.3f) {
            return emptyList()
        }

        // 创建一个居中的边界框来表示检测到的对象
        val centerX = 0.5f
        val centerY = 0.5f
        val width = 0.3f + (classificationResult.confidence - 0.3f) * 0.4f // 置信度越高，框越大
        val height = width * 0.8f

        val x = (centerX - width / 2f).coerceIn(0f, 1f - width)
        val y = (centerY - height / 2f).coerceIn(0f, 1f - height)

        return listOf(
            TrackingResult(
                trackId = 1,
                className = classificationResult.className,
                confidence = classificationResult.confidence,
                x = x,
                y = y,
                width = width,
                height = height,
                timestamp = System.currentTimeMillis()
            )
        )
    }

    /**
     * 创建空结果
     */
    private fun createEmptyResult(requestId: Long, timestamp: Long): InferenceResult {
        return InferenceResult(
            classificationResult = null,
            trackingResults = emptyList(),
            timestamp = timestamp,
            requestId = requestId,
            inferenceTime = 0
        )
    }

    /**
     * 将Bitmap转换为FloatArray
     */
    private fun bitmapToFloatArray(bitmap: Bitmap): FloatArray {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        val floatArray = FloatArray(width * height * 3)
        var index = 0

        for (pixel in pixels) {
            // 提取RGB值并归一化到0-1范围
            floatArray[index++] = ((pixel shr 16) and 0xFF) / 255.0f // R
            floatArray[index++] = ((pixel shr 8) and 0xFF) / 255.0f  // G
            floatArray[index++] = (pixel and 0xFF) / 255.0f          // B
        }

        return floatArray
    }



    /**
     * 清理资源
     */
    fun cleanup() {
        stop()
        inferenceScope.cancel()
        Log.i(TAG, "推理引擎资源已清理")
    }
}
