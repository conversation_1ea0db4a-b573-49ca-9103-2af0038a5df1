/**
 * 图库图像数据模型
 *
 * 表示保存在图库中的图像及其分类信息
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.model

import java.io.File

/**
 * 图库图像数据类
 *
 * @param file 图像文件
 * @param classificationResult 分类结果
 * @param timestamp 创建时间戳
 * @param isLearned 是否为学习样本
 */
data class GalleryImage(
    val file: File,
    val classificationResult: ClassificationResult?,
    val timestamp: Long,
    val isLearned: Boolean = false
) {
    /**
     * 获取文件名
     */
    val fileName: String
        get() = file.name

    /**
     * 获取文件大小（KB）
     */
    val fileSizeKB: Long
        get() = file.length() / 1024

    /**
     * 检查文件是否存在
     */
    val exists: Boolean
        get() = file.exists()

    /**
     * 获取分类标签
     */
    val classLabel: String
        get() = classificationResult?.className ?: "未分类"

    /**
     * 获取置信度
     */
    val confidence: Float
        get() = classificationResult?.confidence ?: 0f

    /**
     * 获取置信度百分比字符串
     */
    val confidencePercent: String
        get() = "${(confidence * 100).toInt()}%"
}
