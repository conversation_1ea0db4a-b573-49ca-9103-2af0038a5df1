{"model": {"name": "prototypical_networks", "backbone": {"name": "resnet12", "feature_dim": 512, "dropout_rate": 0.1}, "use_softmax": true, "feature_normalization": 2.0, "distance_metric": "euclidean", "temperature": 1.0}, "training": {"num_epochs": 100, "learning_rate": 0.001, "weight_decay": 0.0001, "batch_size": 32, "n_way": 5, "n_shot": 5, "n_query": 15, "n_tasks": 1000, "optimizer": "adam", "momentum": 0.9, "betas": [0.9, 0.999], "scheduler": "step", "step_size": 30, "gamma": 0.1, "early_stopping": true, "patience": 10, "min_delta": 0.0001, "save_best": true, "save_last": true, "checkpoint_dir": "checkpoints", "log_interval": 10, "eval_interval": 1, "use_tensorboard": true, "device": "auto", "random_seed": 42}, "data": {"dataset_path": "data/sample_dataset", "image_size": 84, "augmentation": true, "augmentation_level": "medium", "supported_formats": [".jpg", ".jpeg", ".png", ".bmp"], "load_on_ram": false, "num_workers": 4}, "evaluation": {"test_n_way": 5, "test_n_shot": 5, "test_n_query": 15, "test_n_tasks": 1000, "confidence_threshold": 0.5, "save_predictions": true, "prediction_output_dir": "predictions"}, "inference": {"batch_size": 32, "return_confidence": true, "return_probabilities": false, "cache_support_set": true, "optimize_for_speed": true}}