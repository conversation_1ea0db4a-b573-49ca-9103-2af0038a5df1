package com.fsl.app;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\u001c\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0012\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u00a8\u0006\b"}, d2 = {"MainScreen", "", "navController", "Landroidx/navigation/NavHostController;", "viewModel", "Lcom/fsl/app/presentation/MainViewModel;", "getCurrentRoute", "", "app_debug"})
public final class MainActivityKt {
    
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    public static final void MainScreen(@org.jetbrains.annotations.NotNull
    androidx.navigation.NavHostController navController, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.MainViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    private static final java.lang.String getCurrentRoute(androidx.navigation.NavHostController navController) {
        return null;
    }
}