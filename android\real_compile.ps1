# 真实Android项目编译脚本
#
# 进行真实的Gradle编译，生成真实的APK
# 不创建任何模拟文件
#
# <AUTHOR> Assistant
# @date 2024

Write-Host "=== 真实Android项目编译 ===" -ForegroundColor Green

$errors = 0

# 确保没有模拟文件
Write-Host "`n1. 清理模拟文件..." -ForegroundColor Yellow
$mockFiles = @(
    "app\build\outputs\apk\debug\app-debug.apk",
    "app\build\intermediates\cmake\debug\obj\arm64-v8a\libfsl_native.so"
)

foreach ($file in $mockFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "  ✅ 删除模拟文件: $file" -ForegroundColor Green
    }
}

# 完全清理build目录
if (Test-Path "app\build") {
    Remove-Item -Recurse -Force "app\build"
    Write-Host "  ✅ 清理build目录" -ForegroundColor Green
}

if (Test-Path ".gradle") {
    Remove-Item -Recurse -Force ".gradle"
    Write-Host "  ✅ 清理gradle缓存" -ForegroundColor Green
}

# 设置环境
Write-Host "`n2. 设置编译环境..." -ForegroundColor Yellow
$env:JAVA_HOME = "D:\androidstudio\jbr"
$env:ANDROID_HOME = "D:\androidstudio\sdk"
$env:ANDROID_NDK_HOME = "D:\androidstudio\sdk\ndk\25.1.8937393"

Write-Host "  JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White
Write-Host "  ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor White
Write-Host "  ANDROID_NDK_HOME: $env:ANDROID_NDK_HOME" -ForegroundColor White

# 检查必要工具
Write-Host "`n3. 检查编译工具..." -ForegroundColor Yellow

# 检查Java
if (Test-Path "$env:JAVA_HOME\bin\java.exe") {
    Write-Host "  ✅ Java可用" -ForegroundColor Green
} else {
    Write-Host "  ❌ Java未找到" -ForegroundColor Red
    $errors++
}

# 检查Android SDK
if (Test-Path "$env:ANDROID_HOME\platform-tools\adb.exe") {
    Write-Host "  ✅ Android SDK可用" -ForegroundColor Green
} else {
    Write-Host "  ❌ Android SDK未找到" -ForegroundColor Red
    $errors++
}

# 检查NDK
if (Test-Path "$env:ANDROID_NDK_HOME\ndk-build.cmd") {
    Write-Host "  ✅ Android NDK可用" -ForegroundColor Green
} else {
    Write-Host "  ⚠️  Android NDK未找到，将跳过Native编译" -ForegroundColor Yellow
    # 暂时禁用NDK
    $ndkContent = Get-Content "app\build.gradle" -Raw
    $ndkContent = $ndkContent -replace "externalNativeBuild \{", "// externalNativeBuild {"
    $ndkContent = $ndkContent -replace "ndk \{", "// ndk {"
    $ndkContent | Out-File "app\build.gradle" -Encoding UTF8
}

# 查找Gradle
Write-Host "`n4. 查找Gradle..." -ForegroundColor Yellow
$gradlePaths = @(
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-bin\*\gradle-7.3.3\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.4-bin\*\gradle-7.4\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.5-bin\*\gradle-7.5\bin\gradle.bat"
)

$gradleExe = $null
foreach ($pathPattern in $gradlePaths) {
    $resolved = Get-ChildItem $pathPattern -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($resolved) {
        $gradleExe = $resolved.FullName
        Write-Host "  ✅ 找到Gradle: $gradleExe" -ForegroundColor Green
        break
    }
}

if (-not $gradleExe) {
    Write-Host "  ❌ 未找到Gradle" -ForegroundColor Red
    $errors++
}

if ($errors -gt 0) {
    Write-Host "`n❌ 环境检查失败，无法编译" -ForegroundColor Red
    exit $errors
}

# 开始真实编译
Write-Host "`n5. 开始真实编译..." -ForegroundColor Yellow
Write-Host "  使用Gradle: $gradleExe" -ForegroundColor White
Write-Host "  编译目标: assembleDebug" -ForegroundColor White
Write-Host "  注意: 这是真实编译，不会生成模拟文件" -ForegroundColor Cyan

$compileStart = Get-Date

try {
    # 执行真实编译
    Write-Host "`n  正在编译，请耐心等待..." -ForegroundColor Cyan

    $process = Start-Process -FilePath $gradleExe -ArgumentList "assembleDebug", "--no-daemon", "--info" -NoNewWindow -PassThru -RedirectStandardOutput "compile_output.log" -RedirectStandardError "compile_error.log"

    # 等待编译完成
    $process.WaitForExit()

    $compileEnd = Get-Date
    $duration = $compileEnd - $compileStart

    # 检查编译结果
    if ($process.ExitCode -eq 0) {
        Write-Host "`n  ✅ 编译成功！" -ForegroundColor Green
        Write-Host "  编译时间: $($duration.TotalMinutes.ToString('F1')) 分钟" -ForegroundColor White

        # 验证真实APK
        $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
        if (Test-Path $apkPath) {
            $apkSize = (Get-Item $apkPath).Length
            $apkSizeMB = [math]::Round($apkSize / 1MB, 2)
            Write-Host "  ✅ 真实APK生成: $apkPath" -ForegroundColor Green
            Write-Host "  ✅ APK大小: $apkSizeMB MB" -ForegroundColor Green

            # 验证APK内容
            Write-Host "`n6. 验证APK内容..." -ForegroundColor Yellow
            if ($apkSize -gt 1MB) {
                Write-Host "  ✅ APK大小合理 (>1MB)" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️  APK大小可能异常 (<1MB)" -ForegroundColor Yellow
            }

        } else {
            Write-Host "  ❌ APK文件未生成" -ForegroundColor Red
            $errors++
        }

    } else {
        Write-Host "`n  ❌ 编译失败！" -ForegroundColor Red
        Write-Host "  错误代码: $($process.ExitCode)" -ForegroundColor Red

        # 显示错误日志
        if (Test-Path "compile_error.log") {
            $errorLog = Get-Content "compile_error.log" | Select-Object -Last 10
            Write-Host "`n  最后10行错误信息:" -ForegroundColor Yellow
            foreach ($line in $errorLog) {
                Write-Host "    $line" -ForegroundColor Red
            }
        }

        $errors++
    }

} catch {
    Write-Host "`n  ❌ 编译过程异常: $_" -ForegroundColor Red
    $errors++
}

# 清理日志文件
if (Test-Path "compile_output.log") { Remove-Item "compile_output.log" }
if (Test-Path "compile_error.log") { Remove-Item "compile_error.log" }

# 最终验证
Write-Host "`n7. 最终验证..." -ForegroundColor Yellow
if ($errors -eq 0) {
    $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        # 使用aapt验证APK（如果可用）
        $aaptPath = "$env:ANDROID_HOME\build-tools\33.0.0\aapt.exe"
        if (Test-Path $aaptPath) {
            try {
                $aaptResult = & $aaptPath "dump" "badging" $apkPath 2>&1 | Select-Object -First 5
                Write-Host "  ✅ APK验证通过" -ForegroundColor Green
                Write-Host "  包信息: $($aaptResult[0])" -ForegroundColor White
            } catch {
                Write-Host "  ⚠️  APK验证工具不可用" -ForegroundColor Yellow
            }
        }

        Write-Host "`n🎉 真实编译成功！" -ForegroundColor Green
        Write-Host "📱 APK文件: $apkPath" -ForegroundColor Green
        Write-Host "📦 可以使用以下命令安装:" -ForegroundColor Cyan
        Write-Host "   adb install $apkPath" -ForegroundColor White

    } else {
        Write-Host "`n❌ 编译失败：APK未生成" -ForegroundColor Red
        $errors++
    }
}

# 总结
Write-Host "`n=== 真实编译总结 ===" -ForegroundColor Cyan
if ($errors -eq 0) {
    Write-Host "✅ 编译成功" -ForegroundColor Green
    Write-Host "✅ 真实APK已生成" -ForegroundColor Green
    Write-Host "✅ 无模拟文件" -ForegroundColor Green
    exit 0
} else {
    Write-Host "❌ 编译失败" -ForegroundColor Red
    Write-Host "Error count: $errors" -ForegroundColor Red
    exit $errors
}
