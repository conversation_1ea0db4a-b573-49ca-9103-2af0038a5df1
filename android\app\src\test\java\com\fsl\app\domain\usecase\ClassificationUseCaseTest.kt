/**
 * 分类用例测试
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.usecase

import android.graphics.Bitmap
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.repository.IImageProcessingRepository
import com.fsl.app.domain.repository.IInferenceRepository
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ClassificationUseCaseTest {

    @Mock
    private lateinit var inferenceRepository: IInferenceRepository

    @Mock
    private lateinit var imageProcessingRepository: IImageProcessingRepository

    @Mock
    private lateinit var mockBitmap: Bitmap

    private lateinit var classificationUseCase: ClassificationUseCase

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        classificationUseCase = ClassificationUseCase(
            inferenceRepository = inferenceRepository,
            imageProcessingRepository = imageProcessingRepository
        )
    }

    @Test
    fun `classify should return success when inference succeeds`() = runTest {
        // Given
        val expectedResult = ClassificationResult(
            className = "测试类别",
            confidence = 0.95f,
            allScores = mapOf("测试类别" to 0.95f),
            inferenceTime = 100L
        )
        
        whenever(inferenceRepository.isInitialized()).thenReturn(true)
        whenever(imageProcessingRepository.preprocessImage(any())).thenReturn(mockBitmap)
        whenever(inferenceRepository.classify(any())).thenReturn(Result.success(expectedResult))

        // When
        val result = classificationUseCase.classify(mockBitmap)

        // Then
        assertTrue(result.isSuccess)
        assertEquals(expectedResult, result.getOrNull())
    }

    @Test
    fun `classify should initialize repository when not initialized`() = runTest {
        // Given
        val expectedResult = ClassificationResult(
            className = "测试类别",
            confidence = 0.95f
        )
        
        whenever(inferenceRepository.isInitialized()).thenReturn(false)
        whenever(inferenceRepository.initialize()).thenReturn(Result.success(Unit))
        whenever(imageProcessingRepository.preprocessImage(any())).thenReturn(mockBitmap)
        whenever(inferenceRepository.classify(any())).thenReturn(Result.success(expectedResult))

        // When
        val result = classificationUseCase.classify(mockBitmap)

        // Then
        assertTrue(result.isSuccess)
    }

    @Test
    fun `classify should return failure when initialization fails`() = runTest {
        // Given
        val initError = Exception("初始化失败")
        
        whenever(inferenceRepository.isInitialized()).thenReturn(false)
        whenever(inferenceRepository.initialize()).thenReturn(Result.failure(initError))

        // When
        val result = classificationUseCase.classify(mockBitmap)

        // Then
        assertTrue(result.isFailure)
        assertEquals(initError, result.exceptionOrNull())
    }

    @Test
    fun `validateClassificationResult should return true for high confidence`() {
        // Given
        val result = ClassificationResult(
            className = "测试类别",
            confidence = 0.8f
        )

        // When
        val isValid = classificationUseCase.validateClassificationResult(result, 0.7f)

        // Then
        assertTrue(isValid)
    }

    @Test
    fun `getClassificationSuggestion should return appropriate message`() {
        // Given
        val highConfidenceResult = ClassificationResult("类别", 0.95f)
        val mediumConfidenceResult = ClassificationResult("类别", 0.75f)
        val lowConfidenceResult = ClassificationResult("类别", 0.3f)

        // When & Then
        assertEquals("分类结果非常可信", classificationUseCase.getClassificationSuggestion(highConfidenceResult))
        assertEquals("分类结果较为可信", classificationUseCase.getClassificationSuggestion(mediumConfidenceResult))
        assertEquals("分类结果不可信，请重新拍摄或添加更多训练样本", classificationUseCase.getClassificationSuggestion(lowConfidenceResult))
    }
}
