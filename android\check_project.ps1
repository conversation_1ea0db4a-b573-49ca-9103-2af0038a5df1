# Android Project Structure Check Script

Write-Host "Checking Android project structure..." -ForegroundColor Green

$errors = 0

# Check required files
$requiredFiles = @(
    "app/build.gradle",
    "app/src/main/AndroidManifest.xml",
    "app/src/main/java/com/fsl/app/MainActivity.kt",
    "app/src/main/java/com/fsl/app/FSLApplication.kt",
    "gradle/wrapper/gradle-wrapper.properties",
    "settings.gradle",
    "build.gradle"
)

Write-Host "`nChecking required files..." -ForegroundColor Yellow
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  OK: $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
        $errors++
    }
}

# 检查Kotlin源文件
Write-Host "`n📝 检查Kotlin源文件..." -ForegroundColor Yellow
$kotlinFiles = Get-ChildItem -Path "app/src/main/java" -Filter "*.kt" -Recurse

if ($kotlinFiles.Count -eq 0) {
    Write-Host "  ❌ 没有找到Kotlin源文件" -ForegroundColor Red
    $errors++
} else {
    Write-Host "  ✅ 找到 $($kotlinFiles.Count) 个Kotlin文件" -ForegroundColor Green

    foreach ($file in $kotlinFiles) {
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")

        # 检查文件内容
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue

        if ($content) {
            # 检查包声明
            if ($content -match "package\s+com\.fsl\.app") {
                Write-Host "    ✅ $relativePath (包声明正确)" -ForegroundColor Green
            } else {
                Write-Host "    ⚠️  $relativePath (包声明可能有问题)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "    ❌ $relativePath (无法读取文件)" -ForegroundColor Red
            $errors++
        }
    }
}

# 检查资源文件
Write-Host "`n🎨 检查资源文件..." -ForegroundColor Yellow
$resourceFiles = @(
    "app/src/main/res/values/strings.xml",
    "app/src/main/res/values/colors.xml",
    "app/src/main/res/values/themes.xml"
)

foreach ($file in $resourceFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (缺失)" -ForegroundColor Red
        $errors++
    }
}

# 检查AndroidManifest.xml内容
Write-Host "`n📋 检查AndroidManifest.xml内容..." -ForegroundColor Yellow
if (Test-Path "app/src/main/AndroidManifest.xml") {
    $manifest = Get-Content "app/src/main/AndroidManifest.xml" -Raw

    if ($manifest -match "com\.fsl\.app\.MainActivity") {
        Write-Host "  ✅ MainActivity在manifest中正确声明" -ForegroundColor Green
    } else {
        Write-Host "  ❌ MainActivity在manifest中声明有问题" -ForegroundColor Red
        $errors++
    }

    if ($manifest -match "com\.fsl\.app\.FSLApplication") {
        Write-Host "  ✅ Application类在manifest中正确声明" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Application类在manifest中声明有问题" -ForegroundColor Red
        $errors++
    }
}

# 检查build.gradle内容
Write-Host "`n🔧 检查build.gradle内容..." -ForegroundColor Yellow
if (Test-Path "app/build.gradle") {
    $buildGradle = Get-Content "app/build.gradle" -Raw

    if ($buildGradle -match "compileSdk") {
        Write-Host "  ✅ compileSdk配置存在" -ForegroundColor Green
    } else {
        Write-Host "  ❌ compileSdk配置缺失" -ForegroundColor Red
        $errors++
    }

    if ($buildGradle -match "compose") {
        Write-Host "  ✅ Compose配置存在" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Compose配置缺失" -ForegroundColor Red
        $errors++
    }

    if ($buildGradle -match "hilt") {
        Write-Host "  ✅ Hilt依赖存在" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Hilt依赖缺失" -ForegroundColor Red
        $errors++
    }
}

# 总结
Write-Host "`n📊 检查完成!" -ForegroundColor Cyan
Write-Host "总共发现 $errors 个问题" -ForegroundColor $(if ($errors -eq 0) { "Green" } else { "Red" })

if ($errors -eq 0) {
    Write-Host "🎉 恭喜！项目结构检查全部通过！" -ForegroundColor Green
    Write-Host "💡 项目应该可以正常编译" -ForegroundColor Green

    # 尝试运行语法检查
    Write-Host "`n🔍 尝试运行Kotlin语法检查..." -ForegroundColor Yellow

    # 检查是否有kotlinc
    try {
        $kotlincVersion = & kotlinc -version 2>&1
        Write-Host "  ✅ 找到Kotlin编译器: $kotlincVersion" -ForegroundColor Green

        # 可以在这里添加更详细的语法检查
        Write-Host "  💡 建议使用Android Studio进行完整的编译验证" -ForegroundColor Cyan

    } catch {
        Write-Host "  ⚠️  未找到Kotlin编译器，跳过语法检查" -ForegroundColor Yellow
        Write-Host "  💡 建议使用Android Studio进行编译验证" -ForegroundColor Cyan
    }

} else {
    Write-Host "⚠️  请修复上述问题后再尝试编译" -ForegroundColor Red
}

Write-Host "`n🚀 项目特性总结:" -ForegroundColor Cyan
Write-Host "  • 基于Jetpack Compose的现代UI" -ForegroundColor White
Write-Host "  • MVVM + Clean Architecture架构" -ForegroundColor White
Write-Host "  • Hilt依赖注入" -ForegroundColor White
Write-Host "  • 少样本学习推理引擎" -ForegroundColor White
Write-Host "  • 相机实时分类功能" -ForegroundColor White
Write-Host "  • 增量学习支持" -ForegroundColor White

exit $errors
