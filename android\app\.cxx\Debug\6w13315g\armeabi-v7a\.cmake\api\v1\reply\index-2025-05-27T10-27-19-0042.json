{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/androidsdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/androidsdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/androidsdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-75536022d0074a3873ff.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-4aafd623ac85a7da3ec8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6d7f5f70ed3ca420bec6.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-4aafd623ac85a7da3ec8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6d7f5f70ed3ca420bec6.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-75536022d0074a3873ff.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}