package com.fsl.app.ui.components;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001aZ\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0018\b\u0002\u0010\u0004\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00052\u0018\b\u0002\u0010\u0007\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\tH\u0007\u001a&\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\n\u001a\u00020\tH\u0003\u001al\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0016\u0010\u0004\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00052\u0016\u0010\u0007\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00052\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\t2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\b\u0010\u0014\u001a\u00020\u0015H\u0002\u001a\u0012\u0010\u0016\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u0002\u00a8\u0006\u0017"}, d2 = {"CameraPreview", "", "modifier", "Landroidx/compose/ui/Modifier;", "onImageCaptured", "Lkotlin/Function1;", "Landroidx/camera/core/ImageProxy;", "onManualCapture", "useMockCamera", "", "isFrontCamera", "MockCameraPreview", "errorMessage", "", "RealCameraPreview", "context", "Landroid/content/Context;", "lifecycleOwner", "Landroidx/lifecycle/LifecycleOwner;", "onError", "createMockBitmap", "Landroid/graphics/Bitmap;", "createMockImageProxy", "app_debug"})
public final class CameraPreviewKt {
    
    /**
     * 相机预览组件
     *
     * @param modifier 修饰符
     * @param onImageCaptured 图像捕获回调（实时模式）
     * @param onManualCapture 手动拍照回调
     * @param useMockCamera 是否使用模拟相机
     * @param isFrontCamera 是否使用前置相机
     */
    @androidx.compose.runtime.Composable
    public static final void CameraPreview(@org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function1<? super androidx.camera.core.ImageProxy, kotlin.Unit> onImageCaptured, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function1<? super androidx.camera.core.ImageProxy, kotlin.Unit> onManualCapture, boolean useMockCamera, boolean isFrontCamera) {
    }
    
    /**
     * 真实相机预览
     */
    @androidx.compose.runtime.Composable
    private static final void RealCameraPreview(androidx.compose.ui.Modifier modifier, kotlin.jvm.functions.Function1<? super androidx.camera.core.ImageProxy, kotlin.Unit> onImageCaptured, kotlin.jvm.functions.Function1<? super androidx.camera.core.ImageProxy, kotlin.Unit> onManualCapture, android.content.Context context, androidx.lifecycle.LifecycleOwner lifecycleOwner, boolean isFrontCamera, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError) {
    }
    
    /**
     * 模拟相机预览
     */
    @androidx.compose.runtime.Composable
    private static final void MockCameraPreview(androidx.compose.ui.Modifier modifier, java.lang.String errorMessage, boolean isFrontCamera) {
    }
    
    /**
     * 创建模拟的ImageProxy用于测试
     * 生成真实的测试图像数据
     */
    private static final androidx.camera.core.ImageProxy createMockImageProxy(android.content.Context context) {
        return null;
    }
    
    /**
     * 创建模拟图像用于测试
     * 生成具有特定类别特征的测试图像
     */
    private static final android.graphics.Bitmap createMockBitmap() {
        return null;
    }
}