// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        compose_version = '1.3.3'
        kotlin_version = '1.8.10'
        hilt_version = '2.44'
        room_version = '2.4.3'
        camerax_version = '1.2.0'
    }
    repositories {
        // 腾讯云镜像，优先使用
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        maven { url 'https://mirrors.cloud.tencent.com/repository/maven/google/' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.3.1"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
    }
}

plugins {
    id 'com.android.application' version '7.3.1' apply false
    id 'com.android.library' version '7.3.1' apply false
    id 'org.jetbrains.kotlin.android' version '1.8.10' apply false
}

// 仓库配置移动到settings.gradle

task clean(type: Delete) {
    delete rootProject.buildDir
}
