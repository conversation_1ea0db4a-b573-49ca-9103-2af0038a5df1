/**
 * 已学习类别数据访问对象
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.database

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface LearnedClassDao {
    
    /**
     * 获取所有已学习的类别
     */
    @Query("SELECT * FROM learned_classes ORDER BY updatedAt DESC")
    fun getAllClasses(): Flow<List<LearnedClassEntity>>
    
    /**
     * 根据名称获取类别
     */
    @Query("SELECT * FROM learned_classes WHERE name = :className")
    suspend fun getClassByName(className: String): LearnedClassEntity?
    
    /**
     * 插入或更新类别
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertClass(classEntity: LearnedClassEntity)
    
    /**
     * 更新类别
     */
    @Update
    suspend fun updateClass(classEntity: LearnedClassEntity)
    
    /**
     * 删除类别
     */
    @Delete
    suspend fun deleteClass(classEntity: LearnedClassEntity)
    
    /**
     * 根据名称删除类别
     */
    @Query("DELETE FROM learned_classes WHERE name = :className")
    suspend fun deleteClassByName(className: String)
    
    /**
     * 获取类别数量
     */
    @Query("SELECT COUNT(*) FROM learned_classes")
    suspend fun getClassCount(): Int
    
    /**
     * 更新样本数量
     */
    @Query("UPDATE learned_classes SET sampleCount = :sampleCount, updatedAt = :updatedAt WHERE name = :className")
    suspend fun updateSampleCount(className: String, sampleCount: Int, updatedAt: Long)
}

@Dao
interface TrainingSampleDao {
    
    /**
     * 获取指定类别的所有样本
     */
    @Query("SELECT * FROM training_samples WHERE classId = :classId ORDER BY createdAt DESC")
    fun getSamplesByClassId(classId: String): Flow<List<TrainingSampleEntity>>
    
    /**
     * 插入训练样本
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSample(sample: TrainingSampleEntity)
    
    /**
     * 批量插入训练样本
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSamples(samples: List<TrainingSampleEntity>)
    
    /**
     * 删除训练样本
     */
    @Delete
    suspend fun deleteSample(sample: TrainingSampleEntity)
    
    /**
     * 删除指定类别的所有样本
     */
    @Query("DELETE FROM training_samples WHERE classId = :classId")
    suspend fun deleteSamplesByClassId(classId: String)
    
    /**
     * 获取指定类别的样本数量
     */
    @Query("SELECT COUNT(*) FROM training_samples WHERE classId = :classId")
    suspend fun getSampleCountByClassId(classId: String): Int
    
    /**
     * 获取所有样本数量
     */
    @Query("SELECT COUNT(*) FROM training_samples")
    suspend fun getTotalSampleCount(): Int
}
