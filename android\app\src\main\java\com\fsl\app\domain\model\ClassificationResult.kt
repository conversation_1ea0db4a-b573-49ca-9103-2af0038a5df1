/**
 * 分类结果数据模型
 * 
 * 表示图像分类的结果信息
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 分类结果
 * 
 * @property className 预测的类别名称
 * @property confidence 置信度分数 (0.0 - 1.0)
 * @property allScores 所有类别的分数映射
 * @property inferenceTime 推理耗时（毫秒）
 * @property timestamp 分类时间戳
 */
@Parcelize
data class ClassificationResult(
    val className: String,
    val confidence: Float,
    val allScores: Map<String, Float> = emptyMap(),
    val inferenceTime: Long = 0L,
    val timestamp: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * 获取置信度百分比字符串
     */
    fun getConfidencePercentage(): String {
        return "${(confidence * 100).toInt()}%"
    }
    
    /**
     * 判断是否为高置信度结果
     */
    fun isHighConfidence(threshold: Float = 0.7f): Boolean {
        return confidence >= threshold
    }
    
    /**
     * 获取前N个最高分数的类别
     */
    fun getTopNClasses(n: Int = 3): List<Pair<String, Float>> {
        return allScores.toList()
            .sortedByDescending { it.second }
            .take(n)
    }
    
    /**
     * 获取分类质量等级
     */
    fun getQualityLevel(): QualityLevel {
        return when {
            confidence >= 0.9f -> QualityLevel.EXCELLENT
            confidence >= 0.7f -> QualityLevel.GOOD
            confidence >= 0.5f -> QualityLevel.FAIR
            else -> QualityLevel.POOR
        }
    }
}

/**
 * 分类质量等级
 */
enum class QualityLevel {
    EXCELLENT,  // 优秀 (>= 90%)
    GOOD,       // 良好 (>= 70%)
    FAIR,       // 一般 (>= 50%)
    POOR        // 较差 (< 50%)
}

/**
 * 批量分类结果
 * 
 * @property results 所有分类结果列表
 * @property totalInferenceTime 总推理时间
 * @property averageConfidence 平均置信度
 */
@Parcelize
data class BatchClassificationResult(
    val results: List<ClassificationResult>,
    val totalInferenceTime: Long,
    val averageConfidence: Float
) : Parcelable {
    
    /**
     * 获取成功分类的数量
     */
    fun getSuccessCount(threshold: Float = 0.5f): Int {
        return results.count { it.confidence >= threshold }
    }
    
    /**
     * 获取成功率
     */
    fun getSuccessRate(threshold: Float = 0.5f): Float {
        if (results.isEmpty()) return 0f
        return getSuccessCount(threshold).toFloat() / results.size
    }
    
    /**
     * 获取平均推理时间
     */
    fun getAverageInferenceTime(): Float {
        if (results.isEmpty()) return 0f
        return totalInferenceTime.toFloat() / results.size
    }
}

/**
 * 分类统计信息
 * 
 * @property totalClassifications 总分类次数
 * @property successfulClassifications 成功分类次数
 * @property averageConfidence 平均置信度
 * @property averageInferenceTime 平均推理时间
 * @property classDistribution 类别分布统计
 */
data class ClassificationStats(
    val totalClassifications: Int,
    val successfulClassifications: Int,
    val averageConfidence: Float,
    val averageInferenceTime: Float,
    val classDistribution: Map<String, Int>
) {
    
    /**
     * 获取成功率
     */
    fun getSuccessRate(): Float {
        if (totalClassifications == 0) return 0f
        return successfulClassifications.toFloat() / totalClassifications
    }
    
    /**
     * 获取最常见的类别
     */
    fun getMostCommonClass(): String? {
        return classDistribution.maxByOrNull { it.value }?.key
    }
    
    /**
     * 获取类别多样性（不同类别的数量）
     */
    fun getClassDiversity(): Int {
        return classDistribution.size
    }
}
