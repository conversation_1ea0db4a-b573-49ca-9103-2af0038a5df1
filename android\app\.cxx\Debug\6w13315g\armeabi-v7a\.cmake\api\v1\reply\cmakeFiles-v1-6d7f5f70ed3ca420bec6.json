{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "F:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/abis.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isGenerated": true, "path": "F:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "F:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "D:/androidsdk/ndk/27.0.11718014/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}], "kind": "cmakeFiles", "paths": {"build": "F:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a", "source": "F:/geek/fsl/android/app/src/main/cpp"}, "version": {"major": 1, "minor": 0}}