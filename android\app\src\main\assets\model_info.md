# PyTorch Mobile模型信息

## 特征提取模型
- 文件: mobilenet_v3_small.ptl (待生成)
- 架构: MobileNetV3-Small
- 输入: (1, 3, 224, 224)
- 输出: (1, 576)
- 用途: 图像特征提取

## FSL原型网络模型  
- 文件: fsl_prototypical_network.ptl (待生成)
- 架构: MobileNetV3-Small + 原型层
- 输入: (1, 3, 224, 224)
- 输出: (1, 10) # 10个类别的分数
- 用途: Few-Shot Learning分类

## 使用说明
1. 将模型文件放在 android/app/src/main/assets/ 目录下
2. Android应用会自动加载这些模型进行推理
3. 特征提取模型用于提取图像特征
4. FSL模型用于基于原型的分类

## 当前状态
- 模型文件需要通过Python脚本生成
- 运行 `python android/scripts/create_real_pytorch_model.py` 生成模型
- 需要安装PyTorch和torchvision依赖
