package com.fsl.app.di;

import java.lang.System;

@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\'J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\'J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\'J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\'\u00a8\u0006\u0013"}, d2 = {"Lcom/fsl/app/di/RepositoryModule;", "", "()V", "bindGalleryRepository", "Lcom/fsl/app/domain/repository/GalleryRepository;", "galleryRepositoryImpl", "Lcom/fsl/app/data/repository/GalleryRepositoryImpl;", "bindImageProcessingRepository", "Lcom/fsl/app/domain/repository/IImageProcessingRepository;", "imageProcessingRepository", "Lcom/fsl/app/data/repository/ImageProcessingRepository;", "bindInferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "pyTorchInferenceEngine", "Lcom/fsl/app/data/inference/PyTorchInferenceEngine;", "bindModelRepository", "Lcom/fsl/app/domain/repository/IModelRepository;", "modelRepository", "Lcom/fsl/app/data/repository/ModelRepository;", "app_debug"})
@dagger.Module
public abstract class RepositoryModule {
    
    public RepositoryModule() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @dagger.Binds
    public abstract com.fsl.app.domain.repository.IInferenceRepository bindInferenceRepository(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.inference.PyTorchInferenceEngine pyTorchInferenceEngine);
    
    @org.jetbrains.annotations.NotNull
    @dagger.Binds
    public abstract com.fsl.app.domain.repository.IImageProcessingRepository bindImageProcessingRepository(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.ImageProcessingRepository imageProcessingRepository);
    
    @org.jetbrains.annotations.NotNull
    @dagger.Binds
    public abstract com.fsl.app.domain.repository.IModelRepository bindModelRepository(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.ModelRepository modelRepository);
    
    @org.jetbrains.annotations.NotNull
    @dagger.Binds
    public abstract com.fsl.app.domain.repository.GalleryRepository bindGalleryRepository(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepositoryImpl);
}