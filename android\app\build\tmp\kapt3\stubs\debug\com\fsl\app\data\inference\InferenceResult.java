package com.fsl.app.data.inference;

import java.lang.System;

/**
 * 推理结果
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\b\u00a2\u0006\u0002\u0010\u000bJ\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0017\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0018\u001a\u00020\bH\u00c6\u0003JC\u0010\u0019\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006!"}, d2 = {"Lcom/fsl/app/data/inference/InferenceResult;", "", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "trackingResults", "", "Lcom/fsl/app/domain/model/TrackingResult;", "timestamp", "", "requestId", "inferenceTime", "(Lcom/fsl/app/domain/model/ClassificationResult;Ljava/util/List;JJJ)V", "getClassificationResult", "()Lcom/fsl/app/domain/model/ClassificationResult;", "getInferenceTime", "()J", "getRequestId", "getTimestamp", "getTrackingResults", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class InferenceResult {
    @org.jetbrains.annotations.Nullable
    private final com.fsl.app.domain.model.ClassificationResult classificationResult = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.fsl.app.domain.model.TrackingResult> trackingResults = null;
    private final long timestamp = 0L;
    private final long requestId = 0L;
    private final long inferenceTime = 0L;
    
    /**
     * 推理结果
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.data.inference.InferenceResult copy(@org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult classificationResult, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.TrackingResult> trackingResults, long timestamp, long requestId, long inferenceTime) {
        return null;
    }
    
    /**
     * 推理结果
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 推理结果
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 推理结果
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public InferenceResult(@org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult classificationResult, @org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.domain.model.TrackingResult> trackingResults, long timestamp, long requestId, long inferenceTime) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.fsl.app.domain.model.ClassificationResult component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.fsl.app.domain.model.ClassificationResult getClassificationResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.TrackingResult> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.TrackingResult> getTrackingResults() {
        return null;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    public final long component4() {
        return 0L;
    }
    
    public final long getRequestId() {
        return 0L;
    }
    
    public final long component5() {
        return 0L;
    }
    
    public final long getInferenceTime() {
        return 0L;
    }
}