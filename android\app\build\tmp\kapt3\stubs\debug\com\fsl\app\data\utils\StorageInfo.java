package com.fsl.app.data.utils;

import java.lang.System;

/**
 * 存储信息数据类
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0003J\u0006\u0010\u0018\u001a\u00020\u0016J\u0006\u0010\u0019\u001a\u00020\u0016J\u0006\u0010\u001a\u001a\u00020\u0016J\u0006\u0010\u001b\u001a\u00020\u0016J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0016H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\t\u00a8\u0006\u001f"}, d2 = {"Lcom/fsl/app/data/utils/StorageInfo;", "", "imagesSize", "", "modelsSize", "exportsSize", "totalSize", "(JJJJ)V", "getExportsSize", "()J", "getImagesSize", "getModelsSize", "getTotalSize", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "formatSize", "", "bytes", "getFormattedExportsSize", "getFormattedImagesSize", "getFormattedModelsSize", "getFormattedTotalSize", "hashCode", "", "toString", "app_debug"})
public final class StorageInfo {
    private final long imagesSize = 0L;
    private final long modelsSize = 0L;
    private final long exportsSize = 0L;
    private final long totalSize = 0L;
    
    /**
     * 存储信息数据类
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.data.utils.StorageInfo copy(long imagesSize, long modelsSize, long exportsSize, long totalSize) {
        return null;
    }
    
    /**
     * 存储信息数据类
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 存储信息数据类
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 存储信息数据类
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public StorageInfo(long imagesSize, long modelsSize, long exportsSize, long totalSize) {
        super();
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final long getImagesSize() {
        return 0L;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long getModelsSize() {
        return 0L;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final long getExportsSize() {
        return 0L;
    }
    
    public final long component4() {
        return 0L;
    }
    
    public final long getTotalSize() {
        return 0L;
    }
    
    /**
     * 格式化大小显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatSize(long bytes) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFormattedTotalSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFormattedImagesSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFormattedModelsSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFormattedExportsSize() {
        return null;
    }
}