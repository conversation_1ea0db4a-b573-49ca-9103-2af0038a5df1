package com.fsl.app.domain.repository;

import java.lang.System;

/**
 * 图像处理仓库接口
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H&J\u0010\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u0004H&J\u0010\u0010\b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004H&J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u000bH&J \u0010\f\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH&\u00a8\u0006\u0010"}, d2 = {"Lcom/fsl/app/domain/repository/IImageProcessingRepository;", "", "augmentImage", "", "Landroid/graphics/Bitmap;", "bitmap", "normalizeImage", "", "preprocessImage", "preprocessImageProxy", "imageProxy", "Landroidx/camera/core/ImageProxy;", "resizeImage", "width", "", "height", "app_debug"})
public abstract interface IImageProcessingRepository {
    
    /**
     * 预处理图像
     */
    @org.jetbrains.annotations.NotNull
    public abstract android.graphics.Bitmap preprocessImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap);
    
    /**
     * 预处理ImageProxy
     */
    @org.jetbrains.annotations.NotNull
    public abstract android.graphics.Bitmap preprocessImageProxy(@org.jetbrains.annotations.NotNull
    androidx.camera.core.ImageProxy imageProxy);
    
    /**
     * 图像增强
     */
    @org.jetbrains.annotations.NotNull
    public abstract java.util.List<android.graphics.Bitmap> augmentImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap);
    
    /**
     * 调整图像大小
     */
    @org.jetbrains.annotations.NotNull
    public abstract android.graphics.Bitmap resizeImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, int width, int height);
    
    /**
     * 图像归一化
     */
    @org.jetbrains.annotations.NotNull
    public abstract float[] normalizeImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap);
}