[{"level_": 0, "message_": "android.ndkVersion from module build.gradle is [27.0.11718014]", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1458957769}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1177004253}, {"level_": 0, "message_": "ndk.dir in local.properties is D:\\androidsdk\\ndk\\27.0.11718014", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2087703237}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1390271010}, {"level_": 0, "message_": "sdkFolder is D:\\androidsdk", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -867165447}, {"level_": 0, "message_": "Checking whether deleting ndk.dir and setting android.ndkVersion to [27.0.11718014] would result in the same NDK", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1290329758}, {"level_": 0, "message_": "android.ndkVersion from module build.gradle is [27.0.11718014]", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1082684292}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1553277730}, {"level_": 0, "message_": "ndk.dir in local.properties is not set", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -848816537}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1013997533}, {"level_": 0, "message_": "sdkFolder is D:\\androidsdk", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1243438924}, {"level_": 0, "message_": "Deleting ndk.dir and setting android.ndkVersion to [27.0.11718014] would result in the same NDK.", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -418682734}, {"level_": 2, "message_": "NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please delete ndk.dir from local.properties and set android.ndkVersion to [27.0.11718014] in all native modules in the project. https://developer.android.com/r/studio-ui/ndk-dir", "file_": "", "tag_": "", "diagnosticCode_": 5106, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1915454618}]