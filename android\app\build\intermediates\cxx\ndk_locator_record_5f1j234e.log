[{"level_": 0, "message_": "android.ndkVersion from module build.gradle is [27.0.11718014]", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2120696445}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -515265577}, {"level_": 0, "message_": "ndk.dir in local.properties is D:\\androidsdk\\ndk\\27.0.11718014", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1545525383}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2052009686}, {"level_": 0, "message_": "sdkFolder is D:\\androidsdk", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -205426771}, {"level_": 0, "message_": "Checking whether deleting ndk.dir and setting android.ndkVersion to [27.0.11718014] would result in the same NDK", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1952068434}, {"level_": 0, "message_": "android.ndkVersion from module build.gradle is [27.0.11718014]", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1744422968}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -891539054}, {"level_": 0, "message_": "ndk.dir in local.properties is not set", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -187077861}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1675736209}, {"level_": 0, "message_": "sdkFolder is D:\\androidsdk", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -581700248}, {"level_": 0, "message_": "Deleting ndk.dir and setting android.ndkVersion to [27.0.11718014] would result in the same NDK.", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 243055942}, {"level_": 2, "message_": "NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please delete ndk.dir from local.properties and set android.ndkVersion to [27.0.11718014] in all native modules in the project. https://developer.android.com/r/studio-ui/ndk-dir", "file_": "", "tag_": "", "diagnosticCode_": 5106, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1253715942}]