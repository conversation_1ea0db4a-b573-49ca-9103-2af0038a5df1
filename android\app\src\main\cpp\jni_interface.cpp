/**
 * JNI接口实现
 * 
 * Java和C++之间的桥接接口
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#include <jni.h>
#include <string>
#include <memory>
#include <android/log.h>
#include <android/bitmap.h>
#include "include/fsl_inference.h"

#define LOG_TAG "FSL_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 全局推理引擎实例
static std::unique_ptr<fsl::FSLInferenceEngine> g_engine = nullptr;

extern "C" {

/**
 * 初始化推理引擎
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeInitialize(JNIEnv *env, jobject thiz) {
    LOGI("Initializing native inference engine...");
    
    try {
        if (!g_engine) {
            g_engine = std::make_unique<fsl::FSLInferenceEngine>();
        }
        
        bool success = g_engine->initialize();
        LOGI("Native engine initialization: %s", success ? "SUCCESS" : "FAILED");
        return success;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeInitialize: %s", e.what());
        return false;
    }
}

/**
 * 分类图像
 */
JNIEXPORT jobject JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeClassify(JNIEnv *env, jobject thiz, 
                                                                     jfloatArray imageData, 
                                                                     jint width, jint height) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return nullptr;
    }
    
    try {
        // 获取图像数据
        jfloat* imageArray = env->GetFloatArrayElements(imageData, nullptr);
        if (!imageArray) {
            LOGE("Failed to get image array");
            return nullptr;
        }
        
        // 执行分类
        fsl::ClassificationResult result = g_engine->classify(imageArray, width, height);
        
        // 释放数组
        env->ReleaseFloatArrayElements(imageData, imageArray, JNI_ABORT);
        
        // 创建Java对象
        jclass resultClass = env->FindClass("com/fsl/app/domain/model/ClassificationResult");
        if (!resultClass) {
            LOGE("Failed to find ClassificationResult class");
            return nullptr;
        }
        
        jmethodID constructor = env->GetMethodID(resultClass, "<init>", "(Ljava/lang/String;FLjava/util/Map;J)V");
        if (!constructor) {
            LOGE("Failed to find ClassificationResult constructor");
            return nullptr;
        }
        
        // 创建类别名称字符串
        jstring className = env->NewStringUTF(result.className.c_str());
        
        // 创建分数映射
        jclass hashMapClass = env->FindClass("java/util/HashMap");
        jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
        jmethodID putMethod = env->GetMethodID(hashMapClass, "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;");
        
        jobject scoresMap = env->NewObject(hashMapClass, hashMapConstructor);
        
        // 添加所有分数到映射
        for (const auto& pair : result.allScores) {
            jstring key = env->NewStringUTF(pair.first.c_str());
            
            jclass floatClass = env->FindClass("java/lang/Float");
            jmethodID floatConstructor = env->GetMethodID(floatClass, "<init>", "(F)V");
            jobject value = env->NewObject(floatClass, floatConstructor, pair.second);
            
            env->CallObjectMethod(scoresMap, putMethod, key, value);
            
            env->DeleteLocalRef(key);
            env->DeleteLocalRef(value);
        }
        
        // 创建结果对象
        jobject resultObject = env->NewObject(resultClass, constructor, 
                                            className, result.confidence, scoresMap, result.inferenceTime);
        
        // 清理本地引用
        env->DeleteLocalRef(className);
        env->DeleteLocalRef(scoresMap);
        env->DeleteLocalRef(resultClass);
        env->DeleteLocalRef(hashMapClass);
        
        return resultObject;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeClassify: %s", e.what());
        return nullptr;
    }
}

/**
 * 提取特征
 */
JNIEXPORT jfloatArray JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeExtractFeatures(JNIEnv *env, jobject thiz,
                                                                            jfloatArray imageData,
                                                                            jint width, jint height) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return nullptr;
    }
    
    try {
        // 获取图像数据
        jfloat* imageArray = env->GetFloatArrayElements(imageData, nullptr);
        if (!imageArray) {
            LOGE("Failed to get image array");
            return nullptr;
        }
        
        // 提取特征
        fsl::FeatureVector features = g_engine->extractFeatures(imageArray, width, height);
        
        // 释放数组
        env->ReleaseFloatArrayElements(imageData, imageArray, JNI_ABORT);
        
        if (features.empty()) {
            LOGE("Failed to extract features");
            return nullptr;
        }
        
        // 创建Java浮点数组
        jfloatArray result = env->NewFloatArray(features.size());
        env->SetFloatArrayRegion(result, 0, features.size(), features.data());
        
        return result;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeExtractFeatures: %s", e.what());
        return nullptr;
    }
}

/**
 * 添加类别
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeAddClass(JNIEnv *env, jobject thiz,
                                                                     jstring className,
                                                                     jobjectArray featuresArray) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return false;
    }
    
    try {
        // 获取类别名称
        const char* classNameStr = env->GetStringUTFChars(className, nullptr);
        std::string classNameCpp(classNameStr);
        env->ReleaseStringUTFChars(className, classNameStr);
        
        // 获取特征矩阵
        jsize numFeatures = env->GetArrayLength(featuresArray);
        fsl::FeatureMatrix features;
        
        for (jsize i = 0; i < numFeatures; ++i) {
            jfloatArray featureArray = (jfloatArray)env->GetObjectArrayElement(featuresArray, i);
            jsize featureSize = env->GetArrayLength(featureArray);
            
            jfloat* featureData = env->GetFloatArrayElements(featureArray, nullptr);
            fsl::FeatureVector feature(featureData, featureData + featureSize);
            features.push_back(feature);
            
            env->ReleaseFloatArrayElements(featureArray, featureData, JNI_ABORT);
            env->DeleteLocalRef(featureArray);
        }
        
        // 添加类别
        bool success = g_engine->addClass(classNameCpp, features);
        LOGI("Added class %s: %s", classNameCpp.c_str(), success ? "SUCCESS" : "FAILED");
        
        return success;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeAddClass: %s", e.what());
        return false;
    }
}

/**
 * 更新类别
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeUpdateClass(JNIEnv *env, jobject thiz,
                                                                        jstring className,
                                                                        jobjectArray featuresArray) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return false;
    }
    
    try {
        // 获取类别名称
        const char* classNameStr = env->GetStringUTFChars(className, nullptr);
        std::string classNameCpp(classNameStr);
        env->ReleaseStringUTFChars(className, classNameStr);
        
        // 获取特征矩阵
        jsize numFeatures = env->GetArrayLength(featuresArray);
        fsl::FeatureMatrix features;
        
        for (jsize i = 0; i < numFeatures; ++i) {
            jfloatArray featureArray = (jfloatArray)env->GetObjectArrayElement(featuresArray, i);
            jsize featureSize = env->GetArrayLength(featureArray);
            
            jfloat* featureData = env->GetFloatArrayElements(featureArray, nullptr);
            fsl::FeatureVector feature(featureData, featureData + featureSize);
            features.push_back(feature);
            
            env->ReleaseFloatArrayElements(featureArray, featureData, JNI_ABORT);
            env->DeleteLocalRef(featureArray);
        }
        
        // 更新类别
        bool success = g_engine->updateClass(classNameCpp, features);
        LOGI("Updated class %s: %s", classNameCpp.c_str(), success ? "SUCCESS" : "FAILED");
        
        return success;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeUpdateClass: %s", e.what());
        return false;
    }
}

/**
 * 删除类别
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeRemoveClass(JNIEnv *env, jobject thiz,
                                                                        jstring className) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return false;
    }
    
    try {
        // 获取类别名称
        const char* classNameStr = env->GetStringUTFChars(className, nullptr);
        std::string classNameCpp(classNameStr);
        env->ReleaseStringUTFChars(className, classNameStr);
        
        // 删除类别
        bool success = g_engine->removeClass(classNameCpp);
        LOGI("Removed class %s: %s", classNameCpp.c_str(), success ? "SUCCESS" : "FAILED");
        
        return success;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeRemoveClass: %s", e.what());
        return false;
    }
}

/**
 * 获取类别名称列表
 */
JNIEXPORT jobjectArray JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeGetClassNames(JNIEnv *env, jobject thiz) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return nullptr;
    }
    
    try {
        std::vector<std::string> classNames = g_engine->getClassNames();
        
        // 创建Java字符串数组
        jclass stringClass = env->FindClass("java/lang/String");
        jobjectArray result = env->NewObjectArray(classNames.size(), stringClass, nullptr);
        
        for (size_t i = 0; i < classNames.size(); ++i) {
            jstring className = env->NewStringUTF(classNames[i].c_str());
            env->SetObjectArrayElement(result, i, className);
            env->DeleteLocalRef(className);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeGetClassNames: %s", e.what());
        return nullptr;
    }
}

/**
 * 保存模型
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeSaveModel(JNIEnv *env, jobject thiz,
                                                                      jstring filePath) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return false;
    }
    
    try {
        const char* filePathStr = env->GetStringUTFChars(filePath, nullptr);
        std::string filePathCpp(filePathStr);
        env->ReleaseStringUTFChars(filePath, filePathStr);
        
        bool success = g_engine->saveModel(filePathCpp);
        LOGI("Save model to %s: %s", filePathCpp.c_str(), success ? "SUCCESS" : "FAILED");
        
        return success;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeSaveModel: %s", e.what());
        return false;
    }
}

/**
 * 加载模型
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeLoadModel(JNIEnv *env, jobject thiz,
                                                                      jstring filePath) {
    if (!g_engine) {
        LOGE("Engine not initialized");
        return false;
    }
    
    try {
        const char* filePathStr = env->GetStringUTFChars(filePath, nullptr);
        std::string filePathCpp(filePathStr);
        env->ReleaseStringUTFChars(filePath, filePathStr);
        
        bool success = g_engine->loadModel(filePathCpp);
        LOGI("Load model from %s: %s", filePathCpp.c_str(), success ? "SUCCESS" : "FAILED");
        
        return success;
        
    } catch (const std::exception& e) {
        LOGE("Exception in nativeLoadModel: %s", e.what());
        return false;
    }
}

/**
 * 检查是否已初始化
 */
JNIEXPORT jboolean JNICALL
Java_com_fsl_app_data_inference_NativeInferenceEngine_nativeIsInitialized(JNIEnv *env, jobject thiz) {
    return g_engine && g_engine->isInitialized();
}

} // extern "C"
