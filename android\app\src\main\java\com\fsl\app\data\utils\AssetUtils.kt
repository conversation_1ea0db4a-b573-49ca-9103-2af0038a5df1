/**
 * 资源文件工具类
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.utils

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AssetUtils @Inject constructor(
    @ApplicationContext private val context: Context
) {

    /**
     * 从assets复制文件到内部存储
     */
    fun copyAssetToFile(assetFileName: String): String {
        val file = File(context.filesDir, assetFileName)

        if (!file.exists()) {
            try {
                context.assets.open(assetFileName).use { inputStream ->
                    FileOutputStream(file).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
            } catch (e: Exception) {
                // 如果assets中没有文件，创建一个空文件
                file.createNewFile()
            }
        }

        return file.absolutePath
    }

    /**
     * 检查assets中是否存在文件
     */
    fun assetExists(fileName: String): Boolean {
        return try {
            context.assets.open(fileName).use { true }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 加载Asset文件为字符串
     */
    fun loadAssetAsString(fileName: String): String {
        return try {
            context.assets.open(fileName).use { inputStream ->
                inputStream.bufferedReader().use { reader ->
                    reader.readText()
                }
            }
        } catch (e: java.io.IOException) {
            throw java.io.IOException("无法加载Asset文件: $fileName", e)
        }
    }
}
