{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a", "soFolder": "F:\\geek\\fsl\\android\\app\\build\\intermediates\\cxx\\Debug\\6w13315g\\obj\\arm64-v8a", "soRepublishFolder": "F:\\geek\\fsl\\android\\app\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DANDROID_PLATFORM=android-29", "-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014"], "cFlagsList": [], "cppFlagsList": ["-std=c++17", "-fexceptions", "-frtti", "-DUSE_NNAPI"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "F:\\geek\\fsl\\android\\app\\.cxx", "intermediatesBaseFolder": "F:\\geek\\fsl\\android\\app\\build\\intermediates", "intermediatesFolder": "F:\\geek\\fsl\\android\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "F:\\geek\\fsl\\android\\app", "moduleBuildFile": "F:\\geek\\fsl\\android\\app\\build.gradle", "makeFile": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\androidsdk\\ndk\\27.0.11718014", "ndkVersion": "27.0.11718014", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\androidsdk\\ndk\\27.0.11718014\\build\\cmake\\android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "D:\\androidsdk\\cmake\\3.22.1\\bin\\cmake.exe", "minimumCmakeVersion": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "F:\\geek\\fsl\\android", "sdkFolder": "D:\\androidsdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "D:\\androidsdk\\cmake\\3.22.1\\bin\\ninja.exe"}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "6w13315g4f5k6h1f1v6i3c596d4eb27y1j58172054y5m4l405g68641q5e3m", "configurationArguments": ["-HF:\\geek\\fsl\\android\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014", "-DCMAKE_TOOLCHAIN_FILE=D:\\androidsdk\\ndk\\27.0.11718014\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\androidsdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\geek\\fsl\\android\\app\\build\\intermediates\\cxx\\Debug\\6w13315g\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\geek\\fsl\\android\\app\\build\\intermediates\\cxx\\Debug\\6w13315g\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BF:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DANDROID_PLATFORM=android-29", "-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014"], "stlLibraryFile": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "F:\\geek\\fsl\\android\\app\\build\\intermediates\\cxx\\Debug\\6w13315g"}