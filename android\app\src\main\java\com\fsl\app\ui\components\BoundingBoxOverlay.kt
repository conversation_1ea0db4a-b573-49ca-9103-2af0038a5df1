/**
 * 边界框覆盖组件
 *
 * 用于在相机预览上绘制物体检测的边界框
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.fsl.app.domain.model.ClassificationResult
import com.fsl.app.domain.model.TrackingResult

/**
 * 边界框覆盖组件
 */
@Composable
fun BoundingBoxOverlay(
    modifier: Modifier = Modifier,
    trackingResults: List<TrackingResult> = emptyList(),
    isRealTimeMode: Boolean = false,
    currentClassification: ClassificationResult? = null
) {
    Box(modifier = modifier) {
        // 绘制边界框
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            if (isRealTimeMode && trackingResults.isNotEmpty()) {
                // 实时模式：绘制多个跟踪框
                trackingResults.forEach { tracking ->
                    drawTrackingBox(
                        tracking = tracking,
                        canvasSize = size
                    )
                }
            } else if (currentClassification != null) {
                // 手动模式：绘制单个中心框
                drawCenterDetectionBox(
                    classification = currentClassification,
                    canvasSize = size
                )
            }
        }

        // 实时模式的跟踪结果显示
        if (isRealTimeMode && trackingResults.isNotEmpty()) {
            LazyColumn(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .widthIn(max = 200.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(trackingResults.take(3)) { tracking -> // 只显示前3个结果
                    TrackingCard(
                        tracking = tracking,
                        isCompact = true
                    )
                }
            }
        }

        // 手动模式的分类结果显示
        if (!isRealTimeMode && currentClassification != null) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(16.dp)
            ) {
                ClassificationCard(
                    classification = currentClassification,
                    isCompact = false
                )
            }
        }
    }
}

/**
 * 绘制跟踪框
 */
private fun DrawScope.drawTrackingBox(
    tracking: TrackingResult,
    canvasSize: Size
) {
    // 转换归一化坐标到实际像素坐标
    val left = tracking.x * canvasSize.width
    val top = tracking.y * canvasSize.height
    val right = (tracking.x + tracking.width) * canvasSize.width
    val bottom = (tracking.y + tracking.height) * canvasSize.height

    // 根据置信度选择颜色
    val confidence = tracking.confidence
    val color = when {
        confidence > 0.8f -> Color.Green
        confidence > 0.6f -> Color.Yellow
        confidence > 0.4f -> Color(0xFFFFA500) // Orange
        else -> Color.Red
    }

    // 绘制边界框
    drawRect(
        color = color,
        topLeft = Offset(left, top),
        size = Size(right - left, bottom - top),
        style = Stroke(width = 4.dp.toPx())
    )

    // 绘制跟踪ID标识
    val idRadius = 12.dp.toPx()
    drawCircle(
        color = color,
        radius = idRadius,
        center = Offset(left + idRadius + 4.dp.toPx(), top + idRadius + 4.dp.toPx())
    )

    // 绘制跟踪ID文字
    drawContext.canvas.nativeCanvas.apply {
        val paint = android.graphics.Paint().apply {
            this.color = android.graphics.Color.WHITE
            textSize = 10.sp.toPx()
            isAntiAlias = true
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            textAlign = android.graphics.Paint.Align.CENTER
        }

        drawText(
            tracking.trackId.toString(),
            left + idRadius + 4.dp.toPx(),
            top + idRadius + 8.dp.toPx(),
            paint
        )
    }

    // 绘制标签背景
    val labelHeight = 30.dp.toPx()
    val labelWidth = 120.dp.toPx()
    drawRect(
        color = color,
        topLeft = Offset(left, top - labelHeight),
        size = Size(labelWidth, labelHeight)
    )

    // 绘制标签文字
    drawContext.canvas.nativeCanvas.apply {
        val paint = android.graphics.Paint().apply {
            this.color = android.graphics.Color.WHITE
            textSize = 12.sp.toPx()
            isAntiAlias = true
            typeface = android.graphics.Typeface.DEFAULT_BOLD
        }

        val text = "${tracking.className} ${(confidence * 100).toInt()}%"
        drawText(
            text,
            left + 8.dp.toPx(),
            top - 8.dp.toPx(),
            paint
        )
    }
}

/**
 * 绘制中心检测框（手动模式）
 */
private fun DrawScope.drawCenterDetectionBox(
    classification: ClassificationResult,
    canvasSize: Size
) {
    val centerX = canvasSize.width / 2
    val centerY = canvasSize.height / 2
    val boxSize = minOf(canvasSize.width, canvasSize.height) * 0.6f

    val left = centerX - boxSize / 2
    val top = centerY - boxSize / 2
    val right = centerX + boxSize / 2
    val bottom = centerY + boxSize / 2

    // 根据置信度选择颜色
    val confidence = classification.confidence
    val color = when {
        confidence > 0.8f -> Color.Green
        confidence > 0.6f -> Color.Yellow
        confidence > 0.4f -> Color(0xFFFFA500) // Orange
        else -> Color.Red
    }

    // 绘制边界框
    drawRect(
        color = color,
        topLeft = Offset(left, top),
        size = Size(boxSize, boxSize),
        style = Stroke(width = 6.dp.toPx())
    )

    // 绘制角落标记
    val cornerSize = 20.dp.toPx()
    val strokeWidth = 6.dp.toPx()

    // 左上角
    drawLine(color, Offset(left, top), Offset(left + cornerSize, top), strokeWidth)
    drawLine(color, Offset(left, top), Offset(left, top + cornerSize), strokeWidth)

    // 右上角
    drawLine(color, Offset(right - cornerSize, top), Offset(right, top), strokeWidth)
    drawLine(color, Offset(right, top), Offset(right, top + cornerSize), strokeWidth)

    // 左下角
    drawLine(color, Offset(left, bottom - cornerSize), Offset(left, bottom), strokeWidth)
    drawLine(color, Offset(left, bottom), Offset(left + cornerSize, bottom), strokeWidth)

    // 右下角
    drawLine(color, Offset(right, bottom - cornerSize), Offset(right, bottom), strokeWidth)
    drawLine(color, Offset(right - cornerSize, bottom), Offset(right, bottom), strokeWidth)
}

/**
 * 分类结果卡片
 */
@Composable
private fun ClassificationCard(
    classification: ClassificationResult,
    isCompact: Boolean = false
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(
                if (isCompact) 8.dp else 16.dp
            )
        ) {
            Text(
                text = classification.className,
                style = if (isCompact) {
                    MaterialTheme.typography.bodyMedium
                } else {
                    MaterialTheme.typography.titleMedium
                },
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Text(
                text = "${(classification.confidence * 100).toInt()}%",
                style = if (isCompact) {
                    MaterialTheme.typography.bodySmall
                } else {
                    MaterialTheme.typography.bodyMedium
                },
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (!isCompact && classification.inferenceTime > 0) {
                Text(
                    text = "${classification.inferenceTime}ms",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 跟踪结果卡片
 */
@Composable
private fun TrackingCard(
    tracking: TrackingResult,
    isCompact: Boolean = false
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(
                if (isCompact) 8.dp else 16.dp
            )
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 跟踪ID标识
                Card(
                    modifier = Modifier.size(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = when {
                            tracking.confidence > 0.8f -> Color.Green
                            tracking.confidence > 0.6f -> Color.Yellow
                            tracking.confidence > 0.4f -> Color(0xFFFFA500)
                            else -> Color.Red
                        }
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = tracking.trackId.toString(),
                            style = MaterialTheme.typography.labelSmall,
                            color = Color.White,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = tracking.className,
                    style = if (isCompact) {
                        MaterialTheme.typography.bodyMedium
                    } else {
                        MaterialTheme.typography.titleMedium
                    },
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            Text(
                text = "${(tracking.confidence * 100).toInt()}%",
                style = if (isCompact) {
                    MaterialTheme.typography.bodySmall
                } else {
                    MaterialTheme.typography.bodyMedium
                },
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (!isCompact) {
                Text(
                    text = "位置: ${String.format("%.2f", tracking.getCenterX())}, ${String.format("%.2f", tracking.getCenterY())}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}


