// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AssetUtils_Factory implements Factory<AssetUtils> {
  private final Provider<Context> contextProvider;

  public AssetUtils_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AssetUtils get() {
    return newInstance(contextProvider.get());
  }

  public static AssetUtils_Factory create(Provider<Context> contextProvider) {
    return new AssetUtils_Factory(contextProvider);
  }

  public static AssetUtils newInstance(Context context) {
    return new AssetUtils(context);
  }
}
