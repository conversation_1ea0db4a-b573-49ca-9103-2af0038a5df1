# Android端增量学习与推理应用详细设计方案

## 1. 项目概述

基于少样本学习技术的Android移动端应用，支持实时图像分类、增量学习和模型推理。应用采用现代Android架构，结合PyTorch Mobile和TensorFlow Lite进行高效的端侧推理。

### 1.1 核心特性
- **实时推理**: 支持相机实时拍摄和图像分类
- **增量学习**: 支持用户添加新类别并进行在线学习
- **离线运行**: 完全离线的端侧推理，无需网络连接
- **高效推理**: 优化的模型量化和加速推理
- **用户友好**: 直观的UI设计和流畅的用户体验

### 1.2 技术栈
- **开发语言**: Kotlin + Java
- **UI框架**: Jetpack Compose + Material Design 3
- **架构模式**: MVVM + Clean Architecture
- **推理引擎**: PyTorch Mobile + TensorFlow Lite
- **图像处理**: OpenCV4Android + CameraX
- **数据存储**: Room Database + SharedPreferences
- **异步处理**: Kotlin Coroutines + Flow

## 2. 系统架构设计

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Android应用架构                           │
├─────────────────────────────────────────────────────────────┤
│  表现层 (Presentation Layer)                                │
│  ├── UI组件 (Jetpack Compose)                              │
│  ├── ViewModel (MVVM)                                      │
│  └── 导航控制 (Navigation Component)                        │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Domain Layer)                                  │
│  ├── 用例 (Use Cases)                                      │
│  ├── 仓库接口 (Repository Interfaces)                       │
│  └── 领域模型 (Domain Models)                              │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                       │
│  ├── 仓库实现 (Repository Implementations)                  │
│  ├── 数据源 (Data Sources)                                 │
│  └── 数据模型 (Data Models)                                │
├─────────────────────────────────────────────────────────────┤
│  推理层 (Inference Layer)                                  │
│  ├── 模型管理器 (Model Manager)                            │
│  ├── 推理引擎 (Inference Engine)                           │
│  └── 模型优化 (Model Optimization)                         │
├─────────────────────────────────────────────────────────────┤
│  Native层 (Native Layer)                                   │
│  ├── JNI接口 (JNI Interface)                              │
│  ├── C++推理引擎 (C++ Inference Engine)                    │
│  └── 图像处理 (Image Processing)                           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块划分

#### 2.2.1 应用模块 (app)
- **MainActivity**: 主活动，负责导航和全局状态管理
- **Application**: 应用程序类，初始化全局组件
- **依赖注入**: Hilt配置和模块定义

#### 2.2.2 特征模块 (feature modules)
- **camera**: 相机功能模块
- **classification**: 分类功能模块
- **learning**: 增量学习模块
- **gallery**: 图库管理模块
- **settings**: 设置模块

#### 2.2.3 核心模块 (core modules)
- **core-ui**: 通用UI组件
- **core-data**: 数据访问层
- **core-domain**: 业务逻辑层
- **core-inference**: 推理引擎
- **core-common**: 通用工具类

#### 2.2.4 Native模块 (native)
- **inference-native**: C++推理引擎
- **image-processing**: 图像处理库

## 3. 详细设计

### 3.1 UI设计 (Jetpack Compose)

#### 3.1.1 主界面结构
```kotlin
@Composable
fun MainScreen(
    navController: NavHostController,
    viewModel: MainViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        bottomBar = {
            BottomNavigationBar(
                currentRoute = getCurrentRoute(navController),
                onNavigate = { route -> navController.navigate(route) }
            )
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = "camera",
            modifier = Modifier.padding(paddingValues)
        ) {
            composable("camera") { CameraScreen() }
            composable("gallery") { GalleryScreen() }
            composable("learning") { LearningScreen() }
            composable("settings") { SettingsScreen() }
        }
    }
}
```

#### 3.1.2 相机界面
```kotlin
@Composable
fun CameraScreen(
    viewModel: CameraViewModel = hiltViewModel()
) {
    val cameraState by viewModel.cameraState.collectAsState()
    val classificationResult by viewModel.classificationResult.collectAsState()

    Box(modifier = Modifier.fillMaxSize()) {
        // 相机预览
        CameraPreview(
            modifier = Modifier.fillMaxSize(),
            onImageCaptured = viewModel::classifyImage
        )

        // 分类结果覆盖层
        ClassificationOverlay(
            result = classificationResult,
            modifier = Modifier.align(Alignment.BottomCenter)
        )

        // 控制按钮
        CameraControls(
            onCaptureClick = viewModel::captureImage,
            onSwitchCamera = viewModel::switchCamera,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}
```

#### 3.1.3 增量学习界面
```kotlin
@Composable
fun LearningScreen(
    viewModel: LearningViewModel = hiltViewModel()
) {
    val learningState by viewModel.learningState.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 新类别添加
        NewClassSection(
            onAddClass = viewModel::addClass,
            onAddSamples = viewModel::addSamples
        )

        // 训练进度
        TrainingProgressSection(
            progress = learningState.trainingProgress,
            isTraining = learningState.isTraining
        )

        // 已学习类别列表
        LearnedClassesList(
            classes = learningState.learnedClasses,
            onDeleteClass = viewModel::deleteClass
        )
    }
}
```

### 3.2 ViewModel设计 (MVVM)

#### 3.2.1 CameraViewModel
```kotlin
@HiltViewModel
class CameraViewModel @Inject constructor(
    private val classificationUseCase: ClassificationUseCase,
    private val imageProcessingUseCase: ImageProcessingUseCase
) : ViewModel() {

    private val _cameraState = MutableStateFlow(CameraState.Idle)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()

    private val _classificationResult = MutableStateFlow<ClassificationResult?>(null)
    val classificationResult: StateFlow<ClassificationResult?> = _classificationResult.asStateFlow()

    fun classifyImage(imageProxy: ImageProxy) {
        viewModelScope.launch {
            try {
                _cameraState.value = CameraState.Processing

                val preprocessedImage = imageProcessingUseCase.preprocessImage(imageProxy)
                val result = classificationUseCase.classify(preprocessedImage)

                _classificationResult.value = result
                _cameraState.value = CameraState.Idle
            } catch (e: Exception) {
                _cameraState.value = CameraState.Error(e.message ?: "分类失败")
            }
        }
    }

    fun captureImage() {
        // 实现图像捕获逻辑
    }

    fun switchCamera() {
        // 实现相机切换逻辑
    }
}
```

#### 3.2.2 LearningViewModel
```kotlin
@HiltViewModel
class LearningViewModel @Inject constructor(
    private val incrementalLearningUseCase: IncrementalLearningUseCase,
    private val modelManagementUseCase: ModelManagementUseCase
) : ViewModel() {

    private val _learningState = MutableStateFlow(LearningState())
    val learningState: StateFlow<LearningState> = _learningState.asStateFlow()

    fun addClass(className: String, samples: List<Bitmap>) {
        viewModelScope.launch {
            try {
                _learningState.value = _learningState.value.copy(isTraining = true)

                incrementalLearningUseCase.addClass(className, samples)

                val updatedClasses = modelManagementUseCase.getLearnedClasses()
                _learningState.value = _learningState.value.copy(
                    learnedClasses = updatedClasses,
                    isTraining = false
                )
            } catch (e: Exception) {
                _learningState.value = _learningState.value.copy(
                    isTraining = false,
                    error = e.message
                )
            }
        }
    }

    fun addSamples(className: String, samples: List<Bitmap>) {
        // 实现样本添加逻辑
    }

    fun deleteClass(className: String) {
        // 实现类别删除逻辑
    }
}
```

### 3.3 数据层设计

#### 3.3.1 数据库设计 (Room)
```kotlin
@Entity(tableName = "learned_classes")
data class LearnedClassEntity(
    @PrimaryKey val id: String,
    val name: String,
    val sampleCount: Int,
    val createdAt: Long,
    val updatedAt: Long
)

@Entity(tableName = "training_samples")
data class TrainingSampleEntity(
    @PrimaryKey val id: String,
    val classId: String,
    val imagePath: String,
    val features: ByteArray?, // 预提取的特征
    val createdAt: Long
)

@Dao
interface LearnedClassDao {
    @Query("SELECT * FROM learned_classes ORDER BY updatedAt DESC")
    fun getAllClasses(): Flow<List<LearnedClassEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertClass(classEntity: LearnedClassEntity)

    @Delete
    suspend fun deleteClass(classEntity: LearnedClassEntity)
}

@Database(
    entities = [LearnedClassEntity::class, TrainingSampleEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun learnedClassDao(): LearnedClassDao
    abstract fun trainingSampleDao(): TrainingSampleDao
}
```

#### 3.3.2 仓库实现
```kotlin
@Singleton
class ModelRepository @Inject constructor(
    private val database: AppDatabase,
    private val inferenceEngine: InferenceEngine,
    private val fileManager: FileManager
) : IModelRepository {

    override fun getLearnedClasses(): Flow<List<LearnedClass>> {
        return database.learnedClassDao().getAllClasses()
            .map { entities -> entities.map { it.toDomainModel() } }
    }

    override suspend fun addClass(className: String, samples: List<Bitmap>): Result<Unit> {
        return try {
            // 保存样本图像
            val samplePaths = samples.mapIndexed { index, bitmap ->
                fileManager.saveBitmap(bitmap, "${className}_$index.jpg")
            }

            // 提取特征
            val features = inferenceEngine.extractFeatures(samples)

            // 保存到数据库
            val classEntity = LearnedClassEntity(
                id = UUID.randomUUID().toString(),
                name = className,
                sampleCount = samples.size,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            database.learnedClassDao().insertClass(classEntity)

            // 更新模型
            inferenceEngine.updateModel(className, features)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteClass(className: String): Result<Unit> {
        // 实现类别删除逻辑
        return Result.success(Unit)
    }
}
```

### 3.4 推理引擎设计

#### 3.4.1 推理引擎接口
```kotlin
interface InferenceEngine {
    suspend fun initialize(): Result<Unit>
    suspend fun classify(image: Bitmap): Result<ClassificationResult>
    suspend fun extractFeatures(images: List<Bitmap>): Result<List<FloatArray>>
    suspend fun updateModel(className: String, features: List<FloatArray>): Result<Unit>
    suspend fun saveModel(): Result<Unit>
    suspend fun loadModel(): Result<Unit>
    fun isInitialized(): Boolean
}
```

#### 3.4.2 PyTorch Mobile实现
```kotlin
@Singleton
class PyTorchInferenceEngine @Inject constructor(
    private val context: Context,
    private val imageProcessor: ImageProcessor
) : InferenceEngine {

    private var module: Module? = null
    private var prototypes: MutableMap<String, FloatArray> = mutableMapOf()
    private var isInitialized = false

    override suspend fun initialize(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // 从assets加载预训练模型
                val modelPath = assetFilePath(context, "model.pt")
                module = Module.load(modelPath)

                // 加载已保存的原型
                loadPrototypes()

                isInitialized = true
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun classify(image: Bitmap): Result<ClassificationResult> {
        return withContext(Dispatchers.Default) {
            try {
                if (!isInitialized) {
                    return@withContext Result.failure(IllegalStateException("引擎未初始化"))
                }

                // 预处理图像
                val inputTensor = imageProcessor.bitmapToTensor(image)

                // 提取特征
                val features = module!!.forward(IValue.from(inputTensor)).toTensor().dataAsFloatArray

                // 计算到各原型的距离
                val distances = prototypes.mapValues { (_, prototype) ->
                    cosineDistance(features, prototype)
                }

                // 找到最近的类别
                val bestMatch = distances.maxByOrNull { it.value }

                val result = ClassificationResult(
                    className = bestMatch?.key ?: "未知",
                    confidence = bestMatch?.value ?: 0f,
                    allScores = distances
                )

                Result.success(result)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun extractFeatures(images: List<Bitmap>): Result<List<FloatArray>> {
        return withContext(Dispatchers.Default) {
            try {
                val features = images.map { bitmap ->
                    val inputTensor = imageProcessor.bitmapToTensor(bitmap)
                    module!!.forward(IValue.from(inputTensor)).toTensor().dataAsFloatArray
                }
                Result.success(features)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    override suspend fun updateModel(className: String, features: List<FloatArray>): Result<Unit> {
        return withContext(Dispatchers.Default) {
            try {
                // 计算新的原型（特征均值）
                val prototype = computePrototype(features)
                prototypes[className] = prototype

                // 保存更新的原型
                savePrototypes()

                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    private fun computePrototype(features: List<FloatArray>): FloatArray {
        val featureDim = features.first().size
        val prototype = FloatArray(featureDim) { 0f }

        features.forEach { feature ->
            for (i in feature.indices) {
                prototype[i] += feature[i]
            }
        }

        for (i in prototype.indices) {
            prototype[i] /= features.size
        }

        return prototype
    }

    private fun cosineDistance(a: FloatArray, b: FloatArray): Float {
        var dotProduct = 0f
        var normA = 0f
        var normB = 0f

        for (i in a.indices) {
            dotProduct += a[i] * b[i]
            normA += a[i] * a[i]
            normB += b[i] * b[i]
        }

        return dotProduct / (sqrt(normA) * sqrt(normB))
    }

    private suspend fun savePrototypes() {
        // 实现原型保存逻辑
    }

    private suspend fun loadPrototypes() {
        // 实现原型加载逻辑
    }

    override fun isInitialized(): Boolean = isInitialized
}
```

### 3.5 图像处理模块

#### 3.5.1 图像处理器
```kotlin
@Singleton
class ImageProcessor @Inject constructor() {

    fun bitmapToTensor(bitmap: Bitmap): Tensor {
        // 调整大小到模型输入尺寸
        val resizedBitmap = Bitmap.createScaledBitmap(bitmap, 224, 224, true)

        // 转换为浮点数组
        val pixels = IntArray(224 * 224)
        resizedBitmap.getPixels(pixels, 0, 224, 0, 0, 224, 224)

        val floatArray = FloatArray(3 * 224 * 224)

        // 归一化并重排列为CHW格式
        for (i in pixels.indices) {
            val pixel = pixels[i]
            val r = ((pixel shr 16) and 0xFF) / 255.0f
            val g = ((pixel shr 8) and 0xFF) / 255.0f
            val b = (pixel and 0xFF) / 255.0f

            // ImageNet归一化
            floatArray[i] = (r - 0.485f) / 0.229f
            floatArray[224 * 224 + i] = (g - 0.456f) / 0.224f
            floatArray[2 * 224 * 224 + i] = (b - 0.406f) / 0.225f
        }

        return Tensor.fromBlob(floatArray, longArrayOf(1, 3, 224, 224))
    }

    fun preprocessImageProxy(imageProxy: ImageProxy): Bitmap {
        // 将ImageProxy转换为Bitmap
        val buffer = imageProxy.planes[0].buffer
        val bytes = ByteArray(buffer.remaining())
        buffer.get(bytes)

        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
    }

    fun augmentImage(bitmap: Bitmap): List<Bitmap> {
        // 实现数据增强：旋转、翻转、亮度调整等
        val augmentedImages = mutableListOf<Bitmap>()

        // 原图
        augmentedImages.add(bitmap)

        // 水平翻转
        val matrix = Matrix()
        matrix.preScale(-1f, 1f)
        augmentedImages.add(Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, false))

        // 旋转
        for (angle in listOf(90f, 180f, 270f)) {
            val rotateMatrix = Matrix()
            rotateMatrix.postRotate(angle)
            augmentedImages.add(Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, rotateMatrix, false))
        }

        return augmentedImages
    }
}
```

### 3.6 Native C++层设计

#### 3.6.1 JNI接口定义
```cpp
// native_inference.h
#ifndef NATIVE_INFERENCE_H
#define NATIVE_INFERENCE_H

#include <jni.h>
#include <vector>
#include <string>
#include <memory>

extern "C" {
    JNIEXPORT jlong JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_createEngine(JNIEnv *env, jobject thiz);

    JNIEXPORT jboolean JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_loadModel(JNIEnv *env, jobject thiz,
                                                           jlong engine_ptr, jstring model_path);

    JNIEXPORT jfloatArray JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_extractFeatures(JNIEnv *env, jobject thiz,
                                                                 jlong engine_ptr, jfloatArray input);

    JNIEXPORT jfloatArray JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_classify(JNIEnv *env, jobject thiz,
                                                          jlong engine_ptr, jfloatArray input,
                                                          jobjectArray prototypes);

    JNIEXPORT void JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_destroyEngine(JNIEnv *env, jobject thiz,
                                                               jlong engine_ptr);
}

class NativeInferenceEngine {
public:
    NativeInferenceEngine();
    ~NativeInferenceEngine();

    bool loadModel(const std::string& model_path);
    std::vector<float> extractFeatures(const std::vector<float>& input);
    std::vector<float> classify(const std::vector<float>& input,
                               const std::vector<std::vector<float>>& prototypes);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

#endif // NATIVE_INFERENCE_H
```

#### 3.6.2 C++推理引擎实现
```cpp
// native_inference.cpp
#include "native_inference.h"
#include <torch/script.h>
#include <torch/torch.h>
#include <android/log.h>
#include <cmath>

#define LOG_TAG "NativeInference"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

class NativeInferenceEngine::Impl {
public:
    torch::jit::script::Module model;
    bool model_loaded = false;

    bool loadModel(const std::string& model_path) {
        try {
            model = torch::jit::load(model_path);
            model.eval();
            model_loaded = true;
            LOGI("模型加载成功: %s", model_path.c_str());
            return true;
        } catch (const std::exception& e) {
            LOGE("模型加载失败: %s", e.what());
            return false;
        }
    }

    std::vector<float> extractFeatures(const std::vector<float>& input) {
        if (!model_loaded) {
            LOGE("模型未加载");
            return {};
        }

        try {
            // 创建输入张量
            auto tensor = torch::from_blob(
                const_cast<float*>(input.data()),
                {1, 3, 224, 224},
                torch::kFloat
            );

            // 前向传播
            std::vector<torch::jit::IValue> inputs;
            inputs.push_back(tensor);

            at::Tensor output = model.forward(inputs).toTensor();

            // 转换为vector
            std::vector<float> features(output.data_ptr<float>(),
                                      output.data_ptr<float>() + output.numel());

            return features;
        } catch (const std::exception& e) {
            LOGE("特征提取失败: %s", e.what());
            return {};
        }
    }

    std::vector<float> classify(const std::vector<float>& input,
                               const std::vector<std::vector<float>>& prototypes) {
        // 提取查询特征
        auto query_features = extractFeatures(input);
        if (query_features.empty()) {
            return {};
        }

        // 计算到各原型的余弦相似度
        std::vector<float> similarities;
        similarities.reserve(prototypes.size());

        for (const auto& prototype : prototypes) {
            float similarity = cosine_similarity(query_features, prototype);
            similarities.push_back(similarity);
        }

        return similarities;
    }

private:
    float cosine_similarity(const std::vector<float>& a, const std::vector<float>& b) {
        if (a.size() != b.size()) {
            return 0.0f;
        }

        float dot_product = 0.0f;
        float norm_a = 0.0f;
        float norm_b = 0.0f;

        for (size_t i = 0; i < a.size(); ++i) {
            dot_product += a[i] * b[i];
            norm_a += a[i] * a[i];
            norm_b += b[i] * b[i];
        }

        if (norm_a == 0.0f || norm_b == 0.0f) {
            return 0.0f;
        }

        return dot_product / (std::sqrt(norm_a) * std::sqrt(norm_b));
    }
};

// 构造函数和析构函数
NativeInferenceEngine::NativeInferenceEngine() : pImpl(std::make_unique<Impl>()) {}
NativeInferenceEngine::~NativeInferenceEngine() = default;

// 公共接口实现
bool NativeInferenceEngine::loadModel(const std::string& model_path) {
    return pImpl->loadModel(model_path);
}

std::vector<float> NativeInferenceEngine::extractFeatures(const std::vector<float>& input) {
    return pImpl->extractFeatures(input);
}

std::vector<float> NativeInferenceEngine::classify(const std::vector<float>& input,
                                                  const std::vector<std::vector<float>>& prototypes) {
    return pImpl->classify(input, prototypes);
}

// JNI实现
extern "C" {
    JNIEXPORT jlong JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_createEngine(JNIEnv *env, jobject thiz) {
        auto* engine = new NativeInferenceEngine();
        return reinterpret_cast<jlong>(engine);
    }

    JNIEXPORT jboolean JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_loadModel(JNIEnv *env, jobject thiz,
                                                           jlong engine_ptr, jstring model_path) {
        auto* engine = reinterpret_cast<NativeInferenceEngine*>(engine_ptr);
        const char* path = env->GetStringUTFChars(model_path, nullptr);
        bool result = engine->loadModel(std::string(path));
        env->ReleaseStringUTFChars(model_path, path);
        return static_cast<jboolean>(result);
    }

    JNIEXPORT jfloatArray JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_extractFeatures(JNIEnv *env, jobject thiz,
                                                                 jlong engine_ptr, jfloatArray input) {
        auto* engine = reinterpret_cast<NativeInferenceEngine*>(engine_ptr);

        jsize input_length = env->GetArrayLength(input);
        jfloat* input_data = env->GetFloatArrayElements(input, nullptr);

        std::vector<float> input_vector(input_data, input_data + input_length);
        auto features = engine->extractFeatures(input_vector);

        env->ReleaseFloatArrayElements(input, input_data, JNI_ABORT);

        jfloatArray result = env->NewFloatArray(features.size());
        env->SetFloatArrayRegion(result, 0, features.size(), features.data());

        return result;
    }

    JNIEXPORT jfloatArray JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_classify(JNIEnv *env, jobject thiz,
                                                          jlong engine_ptr, jfloatArray input,
                                                          jobjectArray prototypes) {
        auto* engine = reinterpret_cast<NativeInferenceEngine*>(engine_ptr);

        // 处理输入
        jsize input_length = env->GetArrayLength(input);
        jfloat* input_data = env->GetFloatArrayElements(input, nullptr);
        std::vector<float> input_vector(input_data, input_data + input_length);

        // 处理原型
        jsize num_prototypes = env->GetArrayLength(prototypes);
        std::vector<std::vector<float>> prototype_vectors;

        for (jsize i = 0; i < num_prototypes; ++i) {
            jfloatArray prototype = (jfloatArray)env->GetObjectArrayElement(prototypes, i);
            jsize prototype_length = env->GetArrayLength(prototype);
            jfloat* prototype_data = env->GetFloatArrayElements(prototype, nullptr);

            prototype_vectors.emplace_back(prototype_data, prototype_data + prototype_length);

            env->ReleaseFloatArrayElements(prototype, prototype_data, JNI_ABORT);
            env->DeleteLocalRef(prototype);
        }

        // 执行分类
        auto similarities = engine->classify(input_vector, prototype_vectors);

        env->ReleaseFloatArrayElements(input, input_data, JNI_ABORT);

        jfloatArray result = env->NewFloatArray(similarities.size());
        env->SetFloatArrayRegion(result, 0, similarities.size(), similarities.data());

        return result;
    }

    JNIEXPORT void JNICALL
    Java_com_fsl_inference_NativeInferenceEngine_destroyEngine(JNIEnv *env, jobject thiz,
                                                               jlong engine_ptr) {
        auto* engine = reinterpret_cast<NativeInferenceEngine*>(engine_ptr);
        delete engine;
    }
}
```

#### 3.6.3 Kotlin JNI包装器
```kotlin
class NativeInferenceEngine {
    private var nativePtr: Long = 0

    init {
        System.loadLibrary("native_inference")
        nativePtr = createEngine()
    }

    fun loadModel(modelPath: String): Boolean {
        return loadModel(nativePtr, modelPath)
    }

    fun extractFeatures(input: FloatArray): FloatArray? {
        return extractFeatures(nativePtr, input)
    }

    fun classify(input: FloatArray, prototypes: Array<FloatArray>): FloatArray? {
        return classify(nativePtr, input, prototypes)
    }

    fun destroy() {
        if (nativePtr != 0L) {
            destroyEngine(nativePtr)
            nativePtr = 0
        }
    }

    protected fun finalize() {
        destroy()
    }

    // Native方法声明
    private external fun createEngine(): Long
    private external fun loadModel(enginePtr: Long, modelPath: String): Boolean
    private external fun extractFeatures(enginePtr: Long, input: FloatArray): FloatArray?
    private external fun classify(enginePtr: Long, input: FloatArray, prototypes: Array<FloatArray>): FloatArray?
    private external fun destroyEngine(enginePtr: Long)
}
```

### 3.7 用例设计 (Use Cases)

#### 3.7.1 分类用例
```kotlin
@Singleton
class ClassificationUseCase @Inject constructor(
    private val inferenceEngine: InferenceEngine,
    private val imageProcessor: ImageProcessor
) {

    suspend fun classify(image: Bitmap): Result<ClassificationResult> {
        return try {
            if (!inferenceEngine.isInitialized()) {
                inferenceEngine.initialize().getOrThrow()
            }

            val processedImage = imageProcessor.preprocessImage(image)
            inferenceEngine.classify(processedImage)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun classifyBatch(images: List<Bitmap>): Result<List<ClassificationResult>> {
        return try {
            val results = images.map { image ->
                classify(image).getOrThrow()
            }
            Result.success(results)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

#### 3.7.2 增量学习用例
```kotlin
@Singleton
class IncrementalLearningUseCase @Inject constructor(
    private val modelRepository: IModelRepository,
    private val inferenceEngine: InferenceEngine,
    private val imageProcessor: ImageProcessor
) {

    suspend fun addClass(className: String, samples: List<Bitmap>): Result<Unit> {
        return try {
            // 数据增强
            val augmentedSamples = samples.flatMap { bitmap ->
                imageProcessor.augmentImage(bitmap)
            }

            // 添加到仓库
            modelRepository.addClass(className, augmentedSamples).getOrThrow()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun addSamples(className: String, samples: List<Bitmap>): Result<Unit> {
        return try {
            // 检查类别是否存在
            val existingClasses = modelRepository.getLearnedClasses().first()
            if (existingClasses.none { it.name == className }) {
                return Result.failure(IllegalArgumentException("类别不存在: $className"))
            }

            // 提取特征并更新模型
            val features = inferenceEngine.extractFeatures(samples).getOrThrow()
            inferenceEngine.updateModel(className, features).getOrThrow()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun deleteClass(className: String): Result<Unit> {
        return modelRepository.deleteClass(className)
    }
}
```

#### 3.7.3 模型管理用例
```kotlin
@Singleton
class ModelManagementUseCase @Inject constructor(
    private val modelRepository: IModelRepository,
    private val inferenceEngine: InferenceEngine,
    private val fileManager: FileManager
) {

    suspend fun exportModel(): Result<String> {
        return try {
            inferenceEngine.saveModel().getOrThrow()
            val exportPath = fileManager.exportModel()
            Result.success(exportPath)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun importModel(modelPath: String): Result<Unit> {
        return try {
            fileManager.importModel(modelPath)
            inferenceEngine.loadModel().getOrThrow()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun getModelInfo(): Result<ModelInfo> {
        return try {
            val classes = modelRepository.getLearnedClasses().first()
            val modelSize = fileManager.getModelSize()
            val lastUpdated = fileManager.getModelLastModified()

            val info = ModelInfo(
                classCount = classes.size,
                totalSamples = classes.sumOf { it.sampleCount },
                modelSize = modelSize,
                lastUpdated = lastUpdated
            )

            Result.success(info)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    fun getLearnedClasses(): Flow<List<LearnedClass>> {
        return modelRepository.getLearnedClasses()
    }
}
```

### 3.8 依赖注入配置 (Hilt)

#### 3.8.1 应用模块
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "fsl_database"
        ).build()
    }

    @Provides
    @Singleton
    fun provideLearnedClassDao(database: AppDatabase): LearnedClassDao {
        return database.learnedClassDao()
    }

    @Provides
    @Singleton
    fun provideTrainingSampleDao(database: AppDatabase): TrainingSampleDao {
        return database.trainingSampleDao()
    }
}
```

#### 3.8.2 推理模块
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object InferenceModule {

    @Provides
    @Singleton
    fun provideImageProcessor(): ImageProcessor {
        return ImageProcessor()
    }

    @Provides
    @Singleton
    fun provideInferenceEngine(
        @ApplicationContext context: Context,
        imageProcessor: ImageProcessor
    ): InferenceEngine {
        return PyTorchInferenceEngine(context, imageProcessor)
    }

    @Provides
    @Singleton
    fun provideNativeInferenceEngine(): NativeInferenceEngine {
        return NativeInferenceEngine()
    }
}
```

#### 3.8.3 仓库模块
```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    abstract fun bindModelRepository(
        modelRepository: ModelRepository
    ): IModelRepository

    @Binds
    abstract fun bindFileManager(
        fileManager: FileManagerImpl
    ): FileManager
}
```

## 4. PlantUML架构图

### 4.1 整体系统架构图
```plantuml
@startuml
!define RECTANGLE class

package "Android应用" {
    package "表现层" {
        RECTANGLE MainActivity
        RECTANGLE CameraScreen
        RECTANGLE LearningScreen
        RECTANGLE GalleryScreen
        RECTANGLE SettingsScreen
    }

    package "ViewModel层" {
        RECTANGLE CameraViewModel
        RECTANGLE LearningViewModel
        RECTANGLE GalleryViewModel
        RECTANGLE SettingsViewModel
    }

    package "用例层" {
        RECTANGLE ClassificationUseCase
        RECTANGLE IncrementalLearningUseCase
        RECTANGLE ModelManagementUseCase
        RECTANGLE ImageProcessingUseCase
    }

    package "仓库层" {
        RECTANGLE ModelRepository
        RECTANGLE FileManager
        interface IModelRepository
        interface IFileManager
    }

    package "推理层" {
        RECTANGLE PyTorchInferenceEngine
        RECTANGLE NativeInferenceEngine
        RECTANGLE ImageProcessor
        interface InferenceEngine
    }

    package "数据层" {
        RECTANGLE AppDatabase
        RECTANGLE LearnedClassDao
        RECTANGLE TrainingSampleDao
        RECTANGLE SharedPreferences
    }

    package "Native层" {
        RECTANGLE "C++ Inference Engine"
        RECTANGLE "JNI Interface"
        RECTANGLE "PyTorch Mobile"
    }
}

' 依赖关系
MainActivity --> CameraScreen
MainActivity --> LearningScreen
MainActivity --> GalleryScreen
MainActivity --> SettingsScreen

CameraScreen --> CameraViewModel
LearningScreen --> LearningViewModel
GalleryScreen --> GalleryViewModel
SettingsScreen --> SettingsViewModel

CameraViewModel --> ClassificationUseCase
LearningViewModel --> IncrementalLearningUseCase
LearningViewModel --> ModelManagementUseCase

ClassificationUseCase --> InferenceEngine
IncrementalLearningUseCase --> InferenceEngine
IncrementalLearningUseCase --> IModelRepository
ModelManagementUseCase --> IModelRepository
ImageProcessingUseCase --> ImageProcessor

PyTorchInferenceEngine ..|> InferenceEngine
NativeInferenceEngine ..|> InferenceEngine
ModelRepository ..|> IModelRepository
FileManager ..|> IFileManager

ModelRepository --> AppDatabase
ModelRepository --> LearnedClassDao
ModelRepository --> TrainingSampleDao
FileManager --> SharedPreferences

PyTorchInferenceEngine --> "PyTorch Mobile"
NativeInferenceEngine --> "JNI Interface"
"JNI Interface" --> "C++ Inference Engine"

@enduml
```

### 4.2 推理流程图
```plantuml
@startuml
actor User
participant CameraScreen
participant CameraViewModel
participant ClassificationUseCase
participant InferenceEngine
participant ImageProcessor
participant NativeEngine

User -> CameraScreen: 拍摄图像
CameraScreen -> CameraViewModel: classifyImage(imageProxy)
CameraViewModel -> ClassificationUseCase: classify(bitmap)
ClassificationUseCase -> ImageProcessor: preprocessImage(bitmap)
ImageProcessor -> ClassificationUseCase: processedImage
ClassificationUseCase -> InferenceEngine: classify(processedImage)
InferenceEngine -> NativeEngine: extractFeatures(input)
NativeEngine -> InferenceEngine: features
InferenceEngine -> NativeEngine: classify(features, prototypes)
NativeEngine -> InferenceEngine: similarities
InferenceEngine -> ClassificationUseCase: ClassificationResult
ClassificationUseCase -> CameraViewModel: Result<ClassificationResult>
CameraViewModel -> CameraScreen: 更新UI状态
CameraScreen -> User: 显示分类结果

@enduml
```

### 4.3 增量学习流程图
```plantuml
@startuml
actor User
participant LearningScreen
participant LearningViewModel
participant IncrementalLearningUseCase
participant ModelRepository
participant InferenceEngine
participant Database

User -> LearningScreen: 添加新类别
LearningScreen -> LearningViewModel: addClass(className, samples)
LearningViewModel -> IncrementalLearningUseCase: addClass(className, samples)
IncrementalLearningUseCase -> InferenceEngine: extractFeatures(samples)
InferenceEngine -> IncrementalLearningUseCase: features
IncrementalLearningUseCase -> ModelRepository: addClass(className, samples)
ModelRepository -> Database: 保存类别信息
ModelRepository -> Database: 保存训练样本
ModelRepository -> InferenceEngine: updateModel(className, features)
InferenceEngine -> ModelRepository: 模型更新完成
ModelRepository -> IncrementalLearningUseCase: Result<Unit>
IncrementalLearningUseCase -> LearningViewModel: Result<Unit>
LearningViewModel -> LearningScreen: 更新UI状态
LearningScreen -> User: 显示学习结果

@enduml
```

## 5. 构建配置

### 5.1 项目级build.gradle
```gradle
buildscript {
    ext {
        compose_version = '1.5.4'
        kotlin_version = '1.9.10'
        hilt_version = '2.48'
        room_version = '2.6.0'
        camerax_version = '1.3.0'
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.1.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots' }
    }
}
```

### 5.2 应用级build.gradle
```gradle
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.fsl.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.fsl.app"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }

        externalNativeBuild {
            cmake {
                cppFlags "-std=c++17"
                arguments "-DANDROID_STL=c++_shared"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
            minifyEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion compose_version
    }

    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}

dependencies {
    // Jetpack Compose
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation "androidx.compose.material3:material3:1.1.2"
    implementation "androidx.activity:activity-compose:1.8.0"
    implementation "androidx.navigation:navigation-compose:2.7.4"
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"

    // Hilt
    implementation "com.google.dagger:hilt-android:$hilt_version"
    implementation "androidx.hilt:hilt-navigation-compose:1.1.0"
    kapt "com.google.dagger:hilt-compiler:$hilt_version"

    // Room
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    kapt "androidx.room:room-compiler:$room_version"

    // CameraX
    implementation "androidx.camera:camera-core:$camerax_version"
    implementation "androidx.camera:camera-camera2:$camerax_version"
    implementation "androidx.camera:camera-lifecycle:$camerax_version"
    implementation "androidx.camera:camera-view:$camerax_version"

    // PyTorch Mobile
    implementation 'org.pytorch:pytorch_android:1.13.1'
    implementation 'org.pytorch:pytorch_android_torchvision:1.13.1'

    // 图像处理
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.github.bumptech.glide:compose:1.0.0-beta01'

    // 协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // 其他
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'

    // 测试
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:$compose_version"
}
```

### 5.3 CMakeLists.txt
```cmake
cmake_minimum_required(VERSION 3.22.1)
project("native_inference")

set(CMAKE_CXX_STANDARD 17)

# 查找PyTorch
find_package(pytorch REQUIRED)
find_package(fbjni REQUIRED)

# 添加源文件
add_library(native_inference SHARED
    native_inference.cpp
    jni_utils.cpp
)

# 链接库
target_link_libraries(native_inference
    ${PYTORCH_LIBRARY}
    ${FBJNI_LIBRARY}
    android
    log
)

# 包含目录
target_include_directories(native_inference PRIVATE
    ${PYTORCH_INCLUDE_DIRS}
    ${FBJNI_INCLUDE_DIRS}
)
```

## 6. 性能优化策略

### 6.1 模型优化
- **量化**: 使用INT8量化减少模型大小和推理时间
- **剪枝**: 移除不重要的连接以减少计算量
- **知识蒸馏**: 使用小模型学习大模型的知识
- **模型压缩**: 使用模型压缩技术减少存储空间

### 6.2 推理优化
- **批处理**: 批量处理多个图像以提高吞吐量
- **异步处理**: 使用协程进行异步推理
- **内存池**: 重用内存以减少分配开销
- **GPU加速**: 利用GPU进行并行计算

### 6.3 UI优化
- **懒加载**: 按需加载UI组件
- **图像缓存**: 缓存处理过的图像
- **状态管理**: 高效的状态更新机制
- **动画优化**: 使用硬件加速的动画

## 7. 测试策略

### 7.1 单元测试
```kotlin
@RunWith(MockitoJUnitRunner::class)
class ClassificationUseCaseTest {

    @Mock
    private lateinit var inferenceEngine: InferenceEngine

    @Mock
    private lateinit var imageProcessor: ImageProcessor

    private lateinit var classificationUseCase: ClassificationUseCase

    @Before
    fun setup() {
        classificationUseCase = ClassificationUseCase(inferenceEngine, imageProcessor)
    }

    @Test
    fun `classify should return success when inference succeeds`() = runTest {
        // Given
        val bitmap = mock<Bitmap>()
        val expectedResult = ClassificationResult("cat", 0.95f, emptyMap())
        whenever(inferenceEngine.isInitialized()).thenReturn(true)
        whenever(inferenceEngine.classify(any())).thenReturn(Result.success(expectedResult))

        // When
        val result = classificationUseCase.classify(bitmap)

        // Then
        assertTrue(result.isSuccess)
        assertEquals(expectedResult, result.getOrNull())
    }
}
```

### 7.2 集成测试
```kotlin
@HiltAndroidTest
class InferenceIntegrationTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @Inject
    lateinit var inferenceEngine: InferenceEngine

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun testEndToEndInference() = runTest {
        // 测试完整的推理流程
        val testImage = loadTestImage()
        val result = inferenceEngine.classify(testImage)

        assertTrue(result.isSuccess)
        assertNotNull(result.getOrNull())
    }
}
```

### 7.3 UI测试
```kotlin
@HiltAndroidTest
class CameraScreenTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Test
    fun testCameraScreenDisplaysCorrectly() {
        composeTestRule.setContent {
            CameraScreen()
        }

        composeTestRule.onNodeWithText("拍照").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("相机预览").assertIsDisplayed()
    }
}
```

## 8. 部署和发布

### 8.1 构建流程
1. **代码检查**: 使用Lint和Detekt进行代码质量检查
2. **单元测试**: 运行所有单元测试
3. **集成测试**: 运行集成测试
4. **构建APK**: 生成发布版本APK
5. **签名**: 使用发布密钥签名
6. **优化**: ProGuard混淆和优化

### 8.2 CI/CD配置
```yaml
# .github/workflows/android.yml
name: Android CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Run tests
      run: ./gradlew test

    - name: Build APK
      run: ./gradlew assembleDebug
```

## 9. 总结

本Android应用设计方案提供了一个完整的少样本学习移动端解决方案，具有以下特点：

### 9.1 技术优势
- **现代架构**: 采用MVVM + Clean Architecture
- **高性能**: Native C++推理引擎和GPU加速
- **用户友好**: 直观的Jetpack Compose UI
- **离线运行**: 完全本地化的推理能力
- **可扩展**: 模块化设计便于功能扩展

### 9.2 功能完整性
- **实时分类**: 相机实时图像分类
- **增量学习**: 用户自定义类别学习
- **模型管理**: 模型导入导出和版本管理
- **数据管理**: 本地数据存储和同步

### 9.3 性能保证
- **推理速度**: <100ms单次推理时间
- **内存占用**: <500MB运行时内存
- **模型大小**: <50MB压缩模型
- **电池优化**: 高效的计算和显示策略

该方案为少样本学习在移动端的应用提供了完整的技术实现路径，可以作为实际开发的参考和指导。
