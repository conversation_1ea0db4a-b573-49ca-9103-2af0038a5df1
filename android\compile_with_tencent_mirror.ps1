# Android项目编译脚本 - 使用腾讯镜像加速
#
# 使用腾讯云镜像加速Gradle和依赖下载
#
# <AUTHOR> Assistant
# @date 2024

Write-Host "=== Android项目编译 - 腾讯镜像加速 ===" -ForegroundColor Green

$errors = 0

# 设置环境变量
Write-Host "`n1. 设置编译环境..." -ForegroundColor Yellow
$env:JAVA_HOME = "D:\androidstudio\jbr"
$env:ANDROID_HOME = "D:\androidstudio\sdk"

Write-Host "  JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White
Write-Host "  ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor White

# 检查Java
if (Test-Path "$env:JAVA_HOME\bin\java.exe") {
    $javaVersion = & "$env:JAVA_HOME\bin\java.exe" -version 2>&1 | Select-Object -First 1
    Write-Host "  ✅ Java: $javaVersion" -ForegroundColor Green
} else {
    Write-Host "  ❌ Java未找到" -ForegroundColor Red
    $errors++
}

# 查找可用的Gradle
Write-Host "`n2. 查找Gradle..." -ForegroundColor Yellow
$gradlePaths = @(
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-bin\3bvlxrqvjoi4y7kacvcseppqd\gradle-7.3.3\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.4-bin\*\gradle-7.4\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.5-bin\*\gradle-7.5\bin\gradle.bat"
)

$gradleExe = $null
foreach ($path in $gradlePaths) {
    $resolved = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($resolved) {
        $gradleExe = $resolved.FullName
        Write-Host "  ✅ 找到Gradle: $gradleExe" -ForegroundColor Green
        break
    }
}

if (-not $gradleExe) {
    Write-Host "  ❌ 未找到Gradle" -ForegroundColor Red
    $errors++
}

if ($errors -gt 0) {
    Write-Host "`n❌ 环境检查失败" -ForegroundColor Red
    exit $errors
}

# 清理之前的构建
Write-Host "`n3. 清理之前的构建..." -ForegroundColor Yellow
if (Test-Path "app\build") {
    Remove-Item -Recurse -Force "app\build"
    Write-Host "  ✅ 清理完成" -ForegroundColor Green
} else {
    Write-Host "  ✅ 无需清理" -ForegroundColor Green
}

# 创建gradle.properties配置腾讯镜像
Write-Host "`n4. 配置腾讯镜像..." -ForegroundColor Yellow
$gradlePropsContent = @"
# 腾讯云镜像配置
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
android.useAndroidX=true
android.enableJetifier=true

# 镜像配置
systemProp.https.proxyHost=
systemProp.https.proxyPort=
systemProp.http.proxyHost=
systemProp.http.proxyPort=
"@

$gradlePropsContent | Out-File -FilePath "gradle.properties" -Encoding UTF8
Write-Host "  ✅ gradle.properties已配置" -ForegroundColor Green

# 开始编译
Write-Host "`n5. 开始编译..." -ForegroundColor Yellow
Write-Host "  使用Gradle: $gradleExe" -ForegroundColor White
Write-Host "  编译目标: assembleDebug" -ForegroundColor White

try {
    # 执行编译
    $compileStart = Get-Date
    Write-Host "`n  正在编译，请稍候..." -ForegroundColor Cyan

    $result = & $gradleExe assembleDebug --no-daemon --stacktrace 2>&1

    $compileEnd = Get-Date
    $duration = $compileEnd - $compileStart

    # 检查编译结果
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n  ✅ 编译成功！" -ForegroundColor Green
        Write-Host "  编译时间: $($duration.TotalMinutes.ToString('F1')) 分钟" -ForegroundColor White

        # 检查真实的APK文件
        $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
        if (Test-Path $apkPath) {
            $apkSize = (Get-Item $apkPath).Length
            $apkSizeMB = [math]::Round($apkSize / 1MB, 2)
            Write-Host "  ✅ 真实APK文件: $apkPath" -ForegroundColor Green
            Write-Host "  ✅ APK大小: $apkSizeMB MB" -ForegroundColor Green
        } else {
            Write-Host "  ❌ APK文件未生成" -ForegroundColor Red
            $errors++
        }

    } else {
        Write-Host "`n  ❌ 编译失败！" -ForegroundColor Red
        Write-Host "  错误代码: $LASTEXITCODE" -ForegroundColor Red

        # 显示错误信息
        Write-Host "`n编译输出:" -ForegroundColor Yellow
        $result | Select-Object -Last 20 | ForEach-Object {
            Write-Host "  $_" -ForegroundColor White
        }

        $errors++
    }

} catch {
    Write-Host "`n  ❌ 编译过程出错: $_" -ForegroundColor Red
    $errors++
}

# 分析编译结果
Write-Host "`n6. 分析编译结果..." -ForegroundColor Yellow

if ($errors -eq 0) {
    # 检查生成的文件
    $buildFiles = @(
        "app\build\outputs\apk\debug\app-debug.apk",
        "app\build\intermediates\dex\debug\classes.dex",
        "app\build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\R.jar"
    )

    foreach ($file in $buildFiles) {
        if (Test-Path $file) {
            $size = (Get-Item $file).Length
            $sizeStr = if ($size -gt 1MB) { "{0:N1} MB" -f ($size / 1MB) } else { "{0:N0} KB" -f ($size / 1KB) }
            Write-Host "  ✅ $file ($sizeStr)" -ForegroundColor Green
        }
    }

    # 模拟adb安装
    Write-Host "`n7. 模拟adb安装..." -ForegroundColor Yellow
    Write-Host "  adb install app\build\outputs\apk\debug\app-debug.apk" -ForegroundColor White
    Write-Host "  ✅ 安装命令准备就绪" -ForegroundColor Green
}

# 总结
Write-Host "`n=== 编译总结 ===" -ForegroundColor Cyan
Write-Host "错误数量: $errors" -ForegroundColor $(if ($errors -eq 0) { "Green" } else { "Red" })

if ($errors -eq 0) {
    Write-Host "`n🎉 编译成功！" -ForegroundColor Green
    Write-Host "✅ APK已生成" -ForegroundColor Green
    Write-Host "✅ 可以安装到设备" -ForegroundColor Green

    Write-Host "`n📱 下一步:" -ForegroundColor Cyan
    Write-Host "1. 连接Android设备" -ForegroundColor White
    Write-Host "2. 运行: adb install app\build\outputs\apk\debug\app-debug.apk" -ForegroundColor White
    Write-Host "3. 在设备上启动FSL应用" -ForegroundColor White

    Write-Host "`n🚀 应用特性:" -ForegroundColor Cyan
    Write-Host "- PyTorch Mobile少样本学习" -ForegroundColor White
    Write-Host "- 实时相机分类" -ForegroundColor White
    Write-Host "- 增量学习系统" -ForegroundColor White
    Write-Host "- Jetpack Compose UI" -ForegroundColor White

    exit 0
} else {
    Write-Host "`n❌ 编译失败！" -ForegroundColor Red
    Write-Host "请检查上述错误并重新编译" -ForegroundColor Red
    exit $errors
}
