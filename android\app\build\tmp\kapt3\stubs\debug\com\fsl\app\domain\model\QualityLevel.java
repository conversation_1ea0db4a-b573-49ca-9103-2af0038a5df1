package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 分类质量等级
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/fsl/app/domain/model/QualityLevel;", "", "(Ljava/lang/String;I)V", "EXCELLENT", "GOOD", "FAIR", "POOR", "app_debug"})
public enum QualityLevel {
    /*public static final*/ EXCELLENT /* = new EXCELLENT() */,
    /*public static final*/ GOOD /* = new GOOD() */,
    /*public static final*/ FAIR /* = new FAIR() */,
    /*public static final*/ POOR /* = new POOR() */;
    
    QualityLevel() {
    }
}