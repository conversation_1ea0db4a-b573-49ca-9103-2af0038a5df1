-- Merging decision tree log ---
manifest
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
MERGED from [com.github.bumptech.glide:compose:1.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\1895f50a63ee9775757fba3de45e1f15\transformed\jetified-compose-1.0.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\e97315ddf4994c979448edeacfe9401f\transformed\jetified-room-ktx-2.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe4df33e34c916b774a0ecdee1cee2a\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce0f9ee26d61916c425735ff7a199643\transformed\jetified-hilt-navigation-compose-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.hilt:hilt-navigation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf54968651835d97a918588a7aa99ee3\transformed\jetified-hilt-navigation-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:hilt-android:2.44] C:\Users\<USER>\.gradle\caches\transforms-3\fe6157341ecbee8894b8693cb09aacb8\transformed\jetified-hilt-android-2.44\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f0a6ff10e7855dd2623816e2b527841\transformed\jetified-camera-view-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-lifecycle:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f1b62978e6dc06ea43bdd27fde08c4d\transformed\jetified-camera-lifecycle-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.navigation:navigation-compose:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\aaf7942427ac3312fd121f8de8861855\transformed\jetified-navigation-compose-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\4a8eb6b29afb15c9b201f38b39a75504\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\82c63c69ac718150815caccd1171ae66\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\154a1ba9c71ec06842c95a5522f1809d\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:ktx:1.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5b162c3d52a49b96b49908930082701\transformed\jetified-ktx-1.0.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:recyclerview-integration:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\6cb9e575cdc773540012e56cf1018e34\transformed\jetified-recyclerview-integration-5.0.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\38e083de9a839aecd2d2e673a8d1b0ac\transformed\jetified-glide-5.0.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d56bac1b3e49ce81dbd1ba2ff5e66773\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-permissions:0.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\040701af0044f0891c55a878afa3c4ac\transformed\jetified-accompanist-permissions-0.28.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\868247c4097930f93a25a5a5f5696b79\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\96ad0c0260ee33406e5eaf4e816dae20\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d68e6891a5713b487435c4ae9780cf9b\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9c02057905602c84cbaa82585b94b480\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bc0e893b2992fa2b82c9652a35d1673f\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\132334de4204ab264754869e02c9564b\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69f86441205b02d2146454f267271b39\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\3f31fe43fe20039d16e45002db1eca6a\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\28fb2d28e0dfcb7bff38fa38e5730813\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cfaf21802ef49c3d8616911787455245\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\28fcac792051139554ff75475d601c6b\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fe1c5147badcee5aaf342cda98ae9a27\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\26f7df66742ec477b4d4754b32d582a7\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\79cfd0f14b6eee31d14120cad7cab3cd\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1b25e512ef125a41b417899a352d86cc\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\8643cb5c3ac03a95b8b820be4ad51539\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\28297456bfed98835a42443329442fd0\transformed\jetified-activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\ae66932b41b730606d78db45ba08f0b5\transformed\jetified-activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\72ccbacfecbbba1cbe69ffeb616187eb\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dd93810d26f4d73e9113e6c0795b4cc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\ee0401cd0da30379ab02518288b15f53\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\8c8453ce42bd8e9d5c0d99a12434622c\transformed\navigation-common-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\de83928e630c8496d1464b8cef36c656\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7500985541443f2362d7f624cba2f4f6\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6beb4578d5252f84c4fdb05a6cad66d9\transformed\jetified-lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bc4b5a3f4aae56aff80fc377ca1ca13\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ae8178220089305ce36aae88e52db865\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0830d31589b30f030480b0bf680b1dec\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\77882a5e66ca3cabf2786432352538d4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\612fd5b5064e8d8de31b256d9d37ffdf\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6f0531ba1c37aa655fc6903d88ebaf2\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b4a05db14ecc13e208e38772e3cc3ac\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ce7a25d9c85aaf80d651fca6342aa7f\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1d4b3edfe3f310c6b3fa66d35b78d0c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a837e594cb5c3dc8ec7a5d462fce8ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9df42c28b2e45a60ad78d02936af751\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550f9ed9b1ae2c8c3b4ccd7610a35e97\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c8510873ceb6f9aba289569c01a5e84\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce1d8b5e04e594e4016fa6ddfc1a3415\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e27ec58fc9884cfad32f25da8cb0eea\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\96a1a198d275a540629da571ff99df18\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f21834245850172ef3c32c234f873a52\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4215165c495631f737825b9b8de5aef7\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4f706ba209b8f5123aced03ce6e705a2\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f428fe841c55ebf90c69396ecc6b6fbb\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:17:1-31:12
MERGED from [org.pytorch:pytorch_android_torchvision_lite:1.12.2] C:\Users\<USER>\.gradle\caches\transforms-3\c93455862fbae2c7e84816a5a41cc243\transformed\jetified-pytorch_android_torchvision_lite-1.12.2\AndroidManifest.xml:2:1-10:12
MERGED from [org.pytorch:pytorch_android_lite:1.12.2] C:\Users\<USER>\.gradle\caches\transforms-3\862145a07a0b770510f159c557e0d9af\transformed\jetified-pytorch_android_lite-1.12.2\AndroidManifest.xml:2:1-10:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2328a0fd7450c6852dafa9b6faa7dd98\transformed\jetified-napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\566a4a02c010163deb0b153ba2acfdef\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccd41ed56a8766e85c66a093c65c61f9\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d921635a61b67be96ea213705f340055\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f54acaf7e850f94b17dd754a744048b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\37b4158231187d93ccd9fb910bf6ac54\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\60ec81e9a0b18311d0f31c2551583c20\transformed\jetified-gifdecoder-5.0.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\952bdd6d3cfb7e09da0093bd7f889064\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7f84172ae06243f557715788286fd33\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0485b5b3baab08eef852bbcd2e6da1a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\59c10c2f4c07aa2560184f08d403d719\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.44] C:\Users\<USER>\.gradle\caches\transforms-3\715b0fbbf35499b72f12bc6772261fe7\transformed\jetified-dagger-lint-aar-2.44\AndroidManifest.xml:16:1-19:12
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
	package
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:1-39:12
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:5:5-65
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:5:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:6:5-80
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:7:22-78
uses-feature#android.hardware.camera
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:9:5-11:35
	android:required
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:11:9-32
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:10:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:14:9-33
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:13:9-57
application
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:16:5-37:19
MERGED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0485b5b3baab08eef852bbcd2e6da1a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0485b5b3baab08eef852bbcd2e6da1a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:24:9-35
	android:label
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:22:9-41
	android:fullBackupContent
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:20:9-54
	android:roundIcon
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:23:9-56
	tools:targetApi
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:26:9-29
	android:icon
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:21:9-45
	android:allowBackup
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:25:9-41
	android:dataExtractionRules
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:19:9-65
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:17:9-39
activity#com.fsl.app.MainActivity
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:27:9-36:20
	android:label
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:30:13-45
	android:exported
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:31:13-45
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:32:13-35:29
action#android.intent.action.MAIN
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:33:17-69
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:33:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:34:17-77
	android:name
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml:34:27-74
uses-sdk
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
MERGED from [com.github.bumptech.glide:compose:1.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\1895f50a63ee9775757fba3de45e1f15\transformed\jetified-compose-1.0.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:compose:1.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\1895f50a63ee9775757fba3de45e1f15\transformed\jetified-compose-1.0.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\e97315ddf4994c979448edeacfe9401f\transformed\jetified-room-ktx-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\e97315ddf4994c979448edeacfe9401f\transformed\jetified-room-ktx-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe4df33e34c916b774a0ecdee1cee2a\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe4df33e34c916b774a0ecdee1cee2a\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce0f9ee26d61916c425735ff7a199643\transformed\jetified-hilt-navigation-compose-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-navigation-compose:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce0f9ee26d61916c425735ff7a199643\transformed\jetified-hilt-navigation-compose-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-navigation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf54968651835d97a918588a7aa99ee3\transformed\jetified-hilt-navigation-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-navigation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf54968651835d97a918588a7aa99ee3\transformed\jetified-hilt-navigation-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:hilt-android:2.44] C:\Users\<USER>\.gradle\caches\transforms-3\fe6157341ecbee8894b8693cb09aacb8\transformed\jetified-hilt-android-2.44\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.44] C:\Users\<USER>\.gradle\caches\transforms-3\fe6157341ecbee8894b8693cb09aacb8\transformed\jetified-hilt-android-2.44\AndroidManifest.xml:18:3-42
MERGED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f0a6ff10e7855dd2623816e2b527841\transformed\jetified-camera-view-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f0a6ff10e7855dd2623816e2b527841\transformed\jetified-camera-view-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f1b62978e6dc06ea43bdd27fde08c4d\transformed\jetified-camera-lifecycle-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f1b62978e6dc06ea43bdd27fde08c4d\transformed\jetified-camera-lifecycle-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-compose:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\aaf7942427ac3312fd121f8de8861855\transformed\jetified-navigation-compose-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-compose:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\aaf7942427ac3312fd121f8de8861855\transformed\jetified-navigation-compose-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\4a8eb6b29afb15c9b201f38b39a75504\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\4a8eb6b29afb15c9b201f38b39a75504\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\82c63c69ac718150815caccd1171ae66\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\82c63c69ac718150815caccd1171ae66\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\154a1ba9c71ec06842c95a5522f1809d\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\154a1ba9c71ec06842c95a5522f1809d\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:ktx:1.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5b162c3d52a49b96b49908930082701\transformed\jetified-ktx-1.0.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:ktx:1.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5b162c3d52a49b96b49908930082701\transformed\jetified-ktx-1.0.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:recyclerview-integration:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\6cb9e575cdc773540012e56cf1018e34\transformed\jetified-recyclerview-integration-5.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:recyclerview-integration:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\6cb9e575cdc773540012e56cf1018e34\transformed\jetified-recyclerview-integration-5.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\38e083de9a839aecd2d2e673a8d1b0ac\transformed\jetified-glide-5.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\38e083de9a839aecd2d2e673a8d1b0ac\transformed\jetified-glide-5.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d56bac1b3e49ce81dbd1ba2ff5e66773\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d56bac1b3e49ce81dbd1ba2ff5e66773\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\040701af0044f0891c55a878afa3c4ac\transformed\jetified-accompanist-permissions-0.28.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.accompanist:accompanist-permissions:0.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\040701af0044f0891c55a878afa3c4ac\transformed\jetified-accompanist-permissions-0.28.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\868247c4097930f93a25a5a5f5696b79\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\868247c4097930f93a25a5a5f5696b79\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\96ad0c0260ee33406e5eaf4e816dae20\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\96ad0c0260ee33406e5eaf4e816dae20\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d68e6891a5713b487435c4ae9780cf9b\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d68e6891a5713b487435c4ae9780cf9b\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9c02057905602c84cbaa82585b94b480\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9c02057905602c84cbaa82585b94b480\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bc0e893b2992fa2b82c9652a35d1673f\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bc0e893b2992fa2b82c9652a35d1673f\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\132334de4204ab264754869e02c9564b\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\132334de4204ab264754869e02c9564b\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69f86441205b02d2146454f267271b39\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69f86441205b02d2146454f267271b39\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\3f31fe43fe20039d16e45002db1eca6a\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\3f31fe43fe20039d16e45002db1eca6a\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\28fb2d28e0dfcb7bff38fa38e5730813\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\28fb2d28e0dfcb7bff38fa38e5730813\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cfaf21802ef49c3d8616911787455245\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cfaf21802ef49c3d8616911787455245\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\28fcac792051139554ff75475d601c6b\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\28fcac792051139554ff75475d601c6b\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fe1c5147badcee5aaf342cda98ae9a27\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fe1c5147badcee5aaf342cda98ae9a27\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\26f7df66742ec477b4d4754b32d582a7\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\26f7df66742ec477b4d4754b32d582a7\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\79cfd0f14b6eee31d14120cad7cab3cd\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\79cfd0f14b6eee31d14120cad7cab3cd\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1b25e512ef125a41b417899a352d86cc\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1b25e512ef125a41b417899a352d86cc\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\8643cb5c3ac03a95b8b820be4ad51539\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\8643cb5c3ac03a95b8b820be4ad51539\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\28297456bfed98835a42443329442fd0\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\28297456bfed98835a42443329442fd0\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\ae66932b41b730606d78db45ba08f0b5\transformed\jetified-activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\ae66932b41b730606d78db45ba08f0b5\transformed\jetified-activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\72ccbacfecbbba1cbe69ffeb616187eb\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\72ccbacfecbbba1cbe69ffeb616187eb\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dd93810d26f4d73e9113e6c0795b4cc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5dd93810d26f4d73e9113e6c0795b4cc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\ee0401cd0da30379ab02518288b15f53\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\ee0401cd0da30379ab02518288b15f53\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\8c8453ce42bd8e9d5c0d99a12434622c\transformed\navigation-common-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\8c8453ce42bd8e9d5c0d99a12434622c\transformed\navigation-common-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\de83928e630c8496d1464b8cef36c656\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\de83928e630c8496d1464b8cef36c656\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7500985541443f2362d7f624cba2f4f6\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7500985541443f2362d7f624cba2f4f6\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6beb4578d5252f84c4fdb05a6cad66d9\transformed\jetified-lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6beb4578d5252f84c4fdb05a6cad66d9\transformed\jetified-lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bc4b5a3f4aae56aff80fc377ca1ca13\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bc4b5a3f4aae56aff80fc377ca1ca13\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ae8178220089305ce36aae88e52db865\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ae8178220089305ce36aae88e52db865\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0830d31589b30f030480b0bf680b1dec\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\0830d31589b30f030480b0bf680b1dec\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\77882a5e66ca3cabf2786432352538d4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\77882a5e66ca3cabf2786432352538d4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\612fd5b5064e8d8de31b256d9d37ffdf\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\612fd5b5064e8d8de31b256d9d37ffdf\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6f0531ba1c37aa655fc6903d88ebaf2\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6f0531ba1c37aa655fc6903d88ebaf2\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b4a05db14ecc13e208e38772e3cc3ac\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b4a05db14ecc13e208e38772e3cc3ac\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ce7a25d9c85aaf80d651fca6342aa7f\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ce7a25d9c85aaf80d651fca6342aa7f\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1d4b3edfe3f310c6b3fa66d35b78d0c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1d4b3edfe3f310c6b3fa66d35b78d0c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a837e594cb5c3dc8ec7a5d462fce8ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a837e594cb5c3dc8ec7a5d462fce8ef\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9df42c28b2e45a60ad78d02936af751\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9df42c28b2e45a60ad78d02936af751\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550f9ed9b1ae2c8c3b4ccd7610a35e97\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550f9ed9b1ae2c8c3b4ccd7610a35e97\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c8510873ceb6f9aba289569c01a5e84\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c8510873ceb6f9aba289569c01a5e84\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce1d8b5e04e594e4016fa6ddfc1a3415\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce1d8b5e04e594e4016fa6ddfc1a3415\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e27ec58fc9884cfad32f25da8cb0eea\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\5e27ec58fc9884cfad32f25da8cb0eea\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\96a1a198d275a540629da571ff99df18\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\96a1a198d275a540629da571ff99df18\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f21834245850172ef3c32c234f873a52\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f21834245850172ef3c32c234f873a52\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4215165c495631f737825b9b8de5aef7\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4215165c495631f737825b9b8de5aef7\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4f706ba209b8f5123aced03ce6e705a2\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4f706ba209b8f5123aced03ce6e705a2\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f428fe841c55ebf90c69396ecc6b6fbb\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f428fe841c55ebf90c69396ecc6b6fbb\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [org.pytorch:pytorch_android_torchvision_lite:1.12.2] C:\Users\<USER>\.gradle\caches\transforms-3\c93455862fbae2c7e84816a5a41cc243\transformed\jetified-pytorch_android_torchvision_lite-1.12.2\AndroidManifest.xml:6:5-8:41
MERGED from [org.pytorch:pytorch_android_torchvision_lite:1.12.2] C:\Users\<USER>\.gradle\caches\transforms-3\c93455862fbae2c7e84816a5a41cc243\transformed\jetified-pytorch_android_torchvision_lite-1.12.2\AndroidManifest.xml:6:5-8:41
MERGED from [org.pytorch:pytorch_android_lite:1.12.2] C:\Users\<USER>\.gradle\caches\transforms-3\862145a07a0b770510f159c557e0d9af\transformed\jetified-pytorch_android_lite-1.12.2\AndroidManifest.xml:6:5-8:41
MERGED from [org.pytorch:pytorch_android_lite:1.12.2] C:\Users\<USER>\.gradle\caches\transforms-3\862145a07a0b770510f159c557e0d9af\transformed\jetified-pytorch_android_lite-1.12.2\AndroidManifest.xml:6:5-8:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2328a0fd7450c6852dafa9b6faa7dd98\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\2328a0fd7450c6852dafa9b6faa7dd98\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\566a4a02c010163deb0b153ba2acfdef\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\566a4a02c010163deb0b153ba2acfdef\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccd41ed56a8766e85c66a093c65c61f9\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccd41ed56a8766e85c66a093c65c61f9\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d921635a61b67be96ea213705f340055\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d921635a61b67be96ea213705f340055\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f54acaf7e850f94b17dd754a744048b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f54acaf7e850f94b17dd754a744048b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\37b4158231187d93ccd9fb910bf6ac54\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\37b4158231187d93ccd9fb910bf6ac54\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\60ec81e9a0b18311d0f31c2551583c20\transformed\jetified-gifdecoder-5.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:5.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\60ec81e9a0b18311d0f31c2551583c20\transformed\jetified-gifdecoder-5.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\952bdd6d3cfb7e09da0093bd7f889064\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\952bdd6d3cfb7e09da0093bd7f889064\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7f84172ae06243f557715788286fd33\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7f84172ae06243f557715788286fd33\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0485b5b3baab08eef852bbcd2e6da1a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0485b5b3baab08eef852bbcd2e6da1a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\59c10c2f4c07aa2560184f08d403d719\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\59c10c2f4c07aa2560184f08d403d719\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.44] C:\Users\<USER>\.gradle\caches\transforms-3\715b0fbbf35499b72f12bc6772261fe7\transformed\jetified-dagger-lint-aar-2.44\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.44] C:\Users\<USER>\.gradle\caches\transforms-3\715b0fbbf35499b72f12bc6772261fe7\transformed\jetified-dagger-lint-aar-2.44\AndroidManifest.xml:18:3-42
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		ADDED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
		INJECTED from F:\geek\fsl\android\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f466f79c0adc0e574180527f14f47812\transformed\jetified-camera-core-1.2.0\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:31:17-103
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\27f4573bcf6555a93319f1243cc87aa6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.fsl.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.fsl.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:26:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
