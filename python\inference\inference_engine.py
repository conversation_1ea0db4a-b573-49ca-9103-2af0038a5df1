"""
推理引擎
用于少样本学习模型的实时推理

@author: AI Assistant
@date: 2024
"""

import time
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
import logging

from ..core.base_classifier import FewShotClassifier
from ..core.utils import compute_accuracy, compute_confidence_scores, get_device


class InferenceEngine:
    """
    少样本学习推理引擎
    
    提供高效的实时推理功能，支持单样本和批量推理
    """
    
    def __init__(
        self,
        model: FewShotClassifier,
        device: Optional[torch.device] = None,
        logger: Optional[logging.Logger] = None,
    ):
        """
        初始化推理引擎
        
        Args:
            model: 少样本分类模型
            device: 计算设备
            logger: 日志记录器
        """
        self.model = model
        self.device = device or get_device(prefer_gpu=True)
        self.logger = logger or self._setup_logger()
        
        # 将模型移动到设备并设置为评估模式
        self.model.to(self.device)
        self.model.eval()
        
        # 推理统计
        self.inference_count = 0
        self.total_inference_time = 0.0
        self.support_set_cache = None
        
        self.logger.info(f"推理引擎初始化完成，设备: {self.device}")

    def set_support_set(
        self,
        support_images: torch.Tensor,
        support_labels: torch.Tensor,
        class_names: Optional[List[str]] = None,
    ) -> None:
        """
        设置支持集
        
        Args:
            support_images: 支持集图像，形状为 (n_support, **image_shape)
            support_labels: 支持集标签，形状为 (n_support,)
            class_names: 类别名称列表
        """
        # 移动到设备
        support_images = support_images.to(self.device)
        support_labels = support_labels.to(self.device)
        
        # 处理支持集
        with torch.no_grad():
            self.model.process_support_set(support_images, support_labels)
        
        # 缓存支持集信息
        self.support_set_cache = {
            "images": support_images,
            "labels": support_labels,
            "class_names": class_names or [f"class_{i}" for i in range(len(torch.unique(support_labels)))],
            "n_way": len(torch.unique(support_labels)),
            "n_shot": len(support_labels) // len(torch.unique(support_labels)),
        }
        
        self.logger.info(
            f"支持集已设置: {self.support_set_cache['n_way']}-way "
            f"{self.support_set_cache['n_shot']}-shot"
        )

    def predict_single(
        self,
        query_image: torch.Tensor,
        return_confidence: bool = True,
        return_probabilities: bool = False,
    ) -> Dict[str, Union[int, float, torch.Tensor]]:
        """
        预测单个查询样本
        
        Args:
            query_image: 查询图像，形状为 (channels, height, width)
            return_confidence: 是否返回置信度
            return_probabilities: 是否返回所有类别的概率
            
        Returns:
            预测结果字典
        """
        if self.support_set_cache is None:
            raise RuntimeError("请先设置支持集")
        
        start_time = time.time()
        
        # 确保输入是4D张量 (batch_size=1, channels, height, width)
        if query_image.dim() == 3:
            query_image = query_image.unsqueeze(0)
        
        query_image = query_image.to(self.device)
        
        with torch.no_grad():
            # 前向传播
            predictions = self.model(query_image)
            
            # 获取预测标签
            predicted_label = predictions.argmax(dim=1).item()
            
            result = {
                "predicted_label": predicted_label,
                "predicted_class": self.support_set_cache["class_names"][predicted_label],
            }
            
            # 计算置信度
            if return_confidence:
                confidence = compute_confidence_scores(predictions).item()
                result["confidence"] = confidence
            
            # 返回所有类别概率
            if return_probabilities:
                probabilities = torch.softmax(predictions, dim=1).squeeze(0)
                result["probabilities"] = probabilities.cpu()
                result["class_probabilities"] = {
                    class_name: prob.item()
                    for class_name, prob in zip(
                        self.support_set_cache["class_names"], probabilities
                    )
                }
        
        # 更新统计信息
        inference_time = time.time() - start_time
        self.inference_count += 1
        self.total_inference_time += inference_time
        
        result["inference_time"] = inference_time
        
        return result

    def predict_batch(
        self,
        query_images: torch.Tensor,
        return_confidence: bool = True,
        return_probabilities: bool = False,
    ) -> List[Dict[str, Union[int, float, torch.Tensor]]]:
        """
        批量预测查询样本
        
        Args:
            query_images: 查询图像，形状为 (batch_size, channels, height, width)
            return_confidence: 是否返回置信度
            return_probabilities: 是否返回所有类别的概率
            
        Returns:
            预测结果列表
        """
        if self.support_set_cache is None:
            raise RuntimeError("请先设置支持集")
        
        start_time = time.time()
        
        query_images = query_images.to(self.device)
        batch_size = query_images.size(0)
        
        with torch.no_grad():
            # 批量前向传播
            predictions = self.model(query_images)
            
            # 获取预测标签
            predicted_labels = predictions.argmax(dim=1)
            
            results = []
            for i in range(batch_size):
                result = {
                    "predicted_label": predicted_labels[i].item(),
                    "predicted_class": self.support_set_cache["class_names"][predicted_labels[i].item()],
                }
                
                # 计算置信度
                if return_confidence:
                    confidence = compute_confidence_scores(predictions[i:i+1]).item()
                    result["confidence"] = confidence
                
                # 返回所有类别概率
                if return_probabilities:
                    probabilities = torch.softmax(predictions[i], dim=0)
                    result["probabilities"] = probabilities.cpu()
                    result["class_probabilities"] = {
                        class_name: prob.item()
                        for class_name, prob in zip(
                            self.support_set_cache["class_names"], probabilities
                        )
                    }
                
                results.append(result)
        
        # 更新统计信息
        inference_time = time.time() - start_time
        self.inference_count += batch_size
        self.total_inference_time += inference_time
        
        # 为每个结果添加推理时间
        avg_inference_time = inference_time / batch_size
        for result in results:
            result["inference_time"] = avg_inference_time
        
        return results

    def evaluate_on_task(
        self,
        support_images: torch.Tensor,
        support_labels: torch.Tensor,
        query_images: torch.Tensor,
        query_labels: torch.Tensor,
        class_names: Optional[List[str]] = None,
    ) -> Dict[str, float]:
        """
        在单个任务上评估模型
        
        Args:
            support_images: 支持集图像
            support_labels: 支持集标签
            query_images: 查询集图像
            query_labels: 查询集标签
            class_names: 类别名称列表
            
        Returns:
            评估结果字典
        """
        # 设置支持集
        self.set_support_set(support_images, support_labels, class_names)
        
        # 批量预测
        start_time = time.time()
        predictions = self.predict_batch(query_images, return_confidence=True)
        inference_time = time.time() - start_time
        
        # 提取预测标签和置信度
        predicted_labels = torch.tensor([p["predicted_label"] for p in predictions])
        confidences = torch.tensor([p["confidence"] for p in predictions])
        
        # 计算准确率
        accuracy = compute_accuracy(predicted_labels, query_labels.cpu())
        
        # 计算其他指标
        mean_confidence = confidences.mean().item()
        correct_mask = (predicted_labels == query_labels.cpu())
        correct_confidence = confidences[correct_mask].mean().item() if correct_mask.sum() > 0 else 0.0
        incorrect_confidence = confidences[~correct_mask].mean().item() if (~correct_mask).sum() > 0 else 0.0
        
        return {
            "accuracy": accuracy,
            "mean_confidence": mean_confidence,
            "correct_confidence": correct_confidence,
            "incorrect_confidence": incorrect_confidence,
            "inference_time": inference_time,
            "samples_per_second": len(query_labels) / inference_time,
        }

    def get_statistics(self) -> Dict[str, Union[int, float]]:
        """
        获取推理统计信息
        
        Returns:
            统计信息字典
        """
        avg_inference_time = (
            self.total_inference_time / self.inference_count
            if self.inference_count > 0 else 0.0
        )
        
        return {
            "total_inferences": self.inference_count,
            "total_inference_time": self.total_inference_time,
            "average_inference_time": avg_inference_time,
            "inferences_per_second": 1.0 / avg_inference_time if avg_inference_time > 0 else 0.0,
            "support_set_info": self.support_set_cache,
        }

    def reset_statistics(self) -> None:
        """重置推理统计信息"""
        self.inference_count = 0
        self.total_inference_time = 0.0

    def clear_support_set(self) -> None:
        """清除支持集缓存"""
        self.support_set_cache = None
        self.model.reset_support_set()

    def save_model(self, path: Union[str, Path]) -> None:
        """
        保存模型
        
        Args:
            path: 保存路径
        """
        torch.save({
            "model_state_dict": self.model.state_dict(),
            "model_class": self.model.__class__.__name__,
            "device": str(self.device),
        }, path)
        
        self.logger.info(f"模型已保存到: {path}")

    def load_model(self, path: Union[str, Path]) -> None:
        """
        加载模型
        
        Args:
            path: 模型路径
        """
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint["model_state_dict"])
        self.model.eval()
        
        self.logger.info(f"模型已从 {path} 加载")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("InferenceEngine")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger

    def __repr__(self) -> str:
        """返回推理引擎的字符串表示"""
        return (
            f"InferenceEngine(\n"
            f"  model={self.model.__class__.__name__},\n"
            f"  device={self.device},\n"
            f"  total_inferences={self.inference_count},\n"
            f"  support_set_cached={self.support_set_cache is not None}\n"
            f")"
        )
