@echo off
echo ========================================
echo FSL Android应用自动化测试脚本
echo ========================================

echo.
echo [1] 检查设备连接...
adb devices

echo.
echo [2] 检查应用进程状态...
adb shell ps | findstr com.fsl.app

echo.
echo [3] 检查应用内存使用...
adb shell dumpsys meminfo com.fsl.app | findstr "TOTAL\|Native Heap\|Graphics"

echo.
echo [4] 检查Native库加载状态...
adb shell "ls -la /data/app/com.fsl.app*/lib/arm64-v8a/"

echo.
echo [5] 模拟用户交互 - 点击屏幕中央...
adb shell input tap 540 1000
timeout /t 2 /nobreak > nul

echo.
echo [6] 检查相机权限状态...
adb shell dumpsys package com.fsl.app | findstr "android.permission.CAMERA"

echo.
echo [7] 启动实时日志监控 (按Ctrl+C停止)...
echo 监控FSL相关日志输出...
adb logcat -v time | findstr "FSL\|Native\|SOTA\|Inference\|com.fsl.app"
