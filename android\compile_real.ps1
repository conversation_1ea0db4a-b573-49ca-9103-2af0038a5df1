# Real Android Project Compilation Script
# Performs actual Gradle compilation to generate real APK
# No mock files will be created

Write-Host "=== Real Android Project Compilation ===" -ForegroundColor Green

$errors = 0

# Clean any mock files
Write-Host "`n1. Cleaning mock files..." -ForegroundColor Yellow
$mockFiles = @(
    "app\build\outputs\apk\debug\app-debug.apk",
    "app\build\intermediates\cmake\debug\obj\arm64-v8a\libfsl_native.so"
)

foreach ($file in $mockFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "  Removed mock file: $file" -ForegroundColor Green
    }
}

# Clean build directory completely
if (Test-Path "app\build") {
    Remove-Item -Recurse -Force "app\build"
    Write-Host "  Cleaned build directory" -ForegroundColor Green
}

if (Test-Path ".gradle") {
    Remove-Item -Recurse -Force ".gradle"
    Write-Host "  Cleaned gradle cache" -ForegroundColor Green
}

# Set environment
Write-Host "`n2. Setting build environment..." -ForegroundColor Yellow
$env:JAVA_HOME = "D:\androidstudio\jbr"
$env:ANDROID_HOME = "D:\androidstudio\sdk"

Write-Host "  JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White
Write-Host "  ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor White

# Check tools
Write-Host "`n3. Checking build tools..." -ForegroundColor Yellow

if (Test-Path "$env:JAVA_HOME\bin\java.exe") {
    Write-Host "  OK: Java available" -ForegroundColor Green
} else {
    Write-Host "  ERROR: Java not found" -ForegroundColor Red
    $errors++
}

# Find Gradle
Write-Host "`n4. Finding Gradle..." -ForegroundColor Yellow
$gradlePaths = @(
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-bin\*\gradle-7.3.3\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.4-bin\*\gradle-7.4\bin\gradle.bat",
    "C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.0-bin\*\gradle-8.0\bin\gradle.bat"
)

$gradleExe = $null
foreach ($pathPattern in $gradlePaths) {
    $resolved = Get-ChildItem $pathPattern -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($resolved) {
        $gradleExe = $resolved.FullName
        Write-Host "  Found Gradle: $gradleExe" -ForegroundColor Green
        break
    }
}

if (-not $gradleExe) {
    Write-Host "  ERROR: Gradle not found" -ForegroundColor Red
    $errors++
}

if ($errors -gt 0) {
    Write-Host "`nEnvironment check failed" -ForegroundColor Red
    exit $errors
}

# Start real compilation
Write-Host "`n5. Starting real compilation..." -ForegroundColor Yellow
Write-Host "  Using Gradle: $gradleExe" -ForegroundColor White
Write-Host "  Target: assembleDebug" -ForegroundColor White
Write-Host "  NOTE: This is real compilation, no mock files" -ForegroundColor Cyan

$compileStart = Get-Date

try {
    Write-Host "`n  Compiling, please wait..." -ForegroundColor Cyan

    $result = & $gradleExe "assembleDebug" "--no-daemon" "--stacktrace" 2>&1

    $compileEnd = Get-Date
    $duration = $compileEnd - $compileStart

    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n  SUCCESS: Compilation completed!" -ForegroundColor Green
        Write-Host "  Build time: $($duration.TotalMinutes.ToString('F1')) minutes" -ForegroundColor White

        # Verify real APK
        $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
        if (Test-Path $apkPath) {
            $apkSize = (Get-Item $apkPath).Length
            $apkSizeMB = [math]::Round($apkSize / 1MB, 2)
            Write-Host "  REAL APK generated: $apkPath" -ForegroundColor Green
            Write-Host "  APK size: $apkSizeMB MB" -ForegroundColor Green
        } else {
            Write-Host "  ERROR: APK not generated" -ForegroundColor Red
            $errors++
        }

    } else {
        Write-Host "`n  ERROR: Compilation failed!" -ForegroundColor Red
        Write-Host "  Exit code: $LASTEXITCODE" -ForegroundColor Red

        # Show last few lines of output
        $result | Select-Object -Last 10 | ForEach-Object {
            Write-Host "  $_" -ForegroundColor Red
        }

        $errors++
    }

} catch {
    Write-Host "`n  ERROR: Compilation exception: $_" -ForegroundColor Red
    $errors++
}

# Final verification
Write-Host "`n6. Final verification..." -ForegroundColor Yellow
if ($errors -eq 0) {
    $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        Write-Host "`nSUCCESS: Real compilation completed!" -ForegroundColor Green
        Write-Host "APK file: $apkPath" -ForegroundColor Green
        Write-Host "Install command: adb install $apkPath" -ForegroundColor Cyan
    } else {
        Write-Host "`nERROR: APK not found" -ForegroundColor Red
        $errors++
    }
}

# Summary
Write-Host "`n=== Real Compilation Summary ===" -ForegroundColor Cyan
if ($errors -eq 0) {
    Write-Host "SUCCESS: Real APK generated" -ForegroundColor Green
    Write-Host "No mock files created" -ForegroundColor Green
    exit 0
} else {
    Write-Host "FAILED: Compilation errors" -ForegroundColor Red
    Write-Host "Error count: $errors" -ForegroundColor Red
    exit $errors
}
