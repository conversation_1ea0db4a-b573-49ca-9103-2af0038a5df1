package com.fsl.app.ui.settings;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a*\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u00032\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u0018\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u0004H\u0003\u001a.\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u000f\u001a\u00020\u00042\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u0012\u0010\u0011\u001a\u00020\u00012\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0007\u001a#\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u00042\u0011\u0010\u0015\u001a\r\u0012\u0004\u0012\u00020\u00010\u0007\u00a2\u0006\u0002\b\u0016H\u0003\u00a8\u0006\u0017"}, d2 = {"ModelInfoDialog", "", "modelInfo", "", "", "", "onDismiss", "Lkotlin/Function0;", "ModelStatItem", "label", "value", "SettingsItem", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "title", "subtitle", "onClick", "SettingsScreen", "viewModel", "Lcom/fsl/app/presentation/settings/SettingsViewModel;", "SettingsSection", "content", "Landroidx/compose/runtime/Composable;", "app_debug"})
public final class SettingsScreenKt {
    
    /**
     * 设置界面
     */
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    public static final void SettingsScreen(@org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.settings.SettingsViewModel viewModel) {
    }
    
    /**
     * 设置分组
     */
    @androidx.compose.runtime.Composable
    private static final void SettingsSection(java.lang.String title, kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 设置项
     */
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    private static final void SettingsItem(androidx.compose.ui.graphics.vector.ImageVector icon, java.lang.String title, java.lang.String subtitle, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * 模型统计项
     */
    @androidx.compose.runtime.Composable
    private static final void ModelStatItem(java.lang.String label, java.lang.String value) {
    }
    
    /**
     * 模型信息对话框
     */
    @androidx.compose.runtime.Composable
    private static final void ModelInfoDialog(java.util.Map<java.lang.String, ? extends java.lang.Object> modelInfo, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}