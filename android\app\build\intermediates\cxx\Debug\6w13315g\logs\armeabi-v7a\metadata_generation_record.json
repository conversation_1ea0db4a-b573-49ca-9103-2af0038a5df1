[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -900338829}, {"level_": 0, "message_": "rebuilding JSON F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\armeabi-v7a\\android_gradle_build.json due to:", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1386944362}, {"level_": 0, "message_": "- a dependent build file changed", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -89336825}, {"level_": 0, "message_": "  - F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 779702840}, {"level_": 0, "message_": "keeping json folder 'F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\armeabi-v7a' but regenerating project", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1346201666}, {"level_": 0, "message_": "executing cmake @echo off\n\"D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\geek\\\\fsl\\\\android\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\androidsdk\\\\ndk\\\\27.0.11718014\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\geek\\\\fsl\\\\android\\\\app\\\\.cxx\\\\Debug\\\\6w13315g\\\\armeabi-v7a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_PLATFORM=android-29\" ^\n  \"-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\"\n", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1765115951}, {"level_": 0, "message_": "@echo off\n\"D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\geek\\\\fsl\\\\android\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\androidsdk\\\\ndk\\\\27.0.11718014\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\geek\\\\fsl\\\\android\\\\app\\\\.cxx\\\\Debug\\\\6w13315g\\\\armeabi-v7a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_PLATFORM=android-29\" ^\n  \"-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\"\n", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1243776562}, {"level_": 0, "message_": "Exiting generation of F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\armeabi-v7a\\compile_commands.json.bin normally", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1545226060}, {"level_": 0, "message_": "done executing cmake", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1225211433}, {"level_": 0, "message_": "write command file F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\armeabi-v7a\\metadata_generation_command.txt", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1669539230}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 712861651}]