[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -709790003}, {"level_": 0, "message_": "JSON 'F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 496428859}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 903410477}]