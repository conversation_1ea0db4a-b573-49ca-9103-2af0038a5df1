// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.learning;

import com.fsl.app.domain.repository.IInferenceRepository;
import com.fsl.app.domain.repository.IModelRepository;
import com.fsl.app.domain.usecase.ClassificationUseCase;
import com.fsl.app.domain.usecase.IncrementalLearningUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class LearningViewModel_Factory implements Factory<LearningViewModel> {
  private final Provider<ClassificationUseCase> classificationUseCaseProvider;

  private final Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  private final Provider<IModelRepository> modelRepositoryProvider;

  public LearningViewModel_Factory(Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IModelRepository> modelRepositoryProvider) {
    this.classificationUseCaseProvider = classificationUseCaseProvider;
    this.incrementalLearningUseCaseProvider = incrementalLearningUseCaseProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
    this.modelRepositoryProvider = modelRepositoryProvider;
  }

  @Override
  public LearningViewModel get() {
    return newInstance(classificationUseCaseProvider.get(), incrementalLearningUseCaseProvider.get(), inferenceRepositoryProvider.get(), modelRepositoryProvider.get());
  }

  public static LearningViewModel_Factory create(
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IModelRepository> modelRepositoryProvider) {
    return new LearningViewModel_Factory(classificationUseCaseProvider, incrementalLearningUseCaseProvider, inferenceRepositoryProvider, modelRepositoryProvider);
  }

  public static LearningViewModel newInstance(ClassificationUseCase classificationUseCase,
      IncrementalLearningUseCase incrementalLearningUseCase,
      IInferenceRepository inferenceRepository, IModelRepository modelRepository) {
    return new LearningViewModel(classificationUseCase, incrementalLearningUseCase, inferenceRepository, modelRepository);
  }
}
