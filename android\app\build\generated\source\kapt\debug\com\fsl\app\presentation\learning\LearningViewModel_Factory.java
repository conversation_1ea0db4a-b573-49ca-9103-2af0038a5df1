// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.learning;

import com.fsl.app.domain.repository.IInferenceRepository;
import com.fsl.app.domain.usecase.ClassificationUseCase;
import com.fsl.app.domain.usecase.IncrementalLearningUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class LearningViewModel_Factory implements Factory<LearningViewModel> {
  private final Provider<ClassificationUseCase> classificationUseCaseProvider;

  private final Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  public LearningViewModel_Factory(Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider) {
    this.classificationUseCaseProvider = classificationUseCaseProvider;
    this.incrementalLearningUseCaseProvider = incrementalLearningUseCaseProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
  }

  @Override
  public LearningViewModel get() {
    return newInstance(classificationUseCaseProvider.get(), incrementalLearningUseCaseProvider.get(), inferenceRepositoryProvider.get());
  }

  public static LearningViewModel_Factory create(
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<IncrementalLearningUseCase> incrementalLearningUseCaseProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider) {
    return new LearningViewModel_Factory(classificationUseCaseProvider, incrementalLearningUseCaseProvider, inferenceRepositoryProvider);
  }

  public static LearningViewModel newInstance(ClassificationUseCase classificationUseCase,
      IncrementalLearningUseCase incrementalLearningUseCase,
      IInferenceRepository inferenceRepository) {
    return new LearningViewModel(classificationUseCase, incrementalLearningUseCase, inferenceRepository);
  }
}
