// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.di;

import com.fsl.app.data.inference.NativeInferenceEngine;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import org.jetbrains.annotations.Nullable;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AppModule_ProvideNativeInferenceEngineFactory implements Factory<NativeInferenceEngine> {
  @Override
  @Nullable
  public NativeInferenceEngine get() {
    return provideNativeInferenceEngine();
  }

  public static AppModule_ProvideNativeInferenceEngineFactory create() {
    return InstanceHolder.INSTANCE;
  }

  @Nullable
  public static NativeInferenceEngine provideNativeInferenceEngine() {
    return AppModule.INSTANCE.provideNativeInferenceEngine();
  }

  private static final class InstanceHolder {
    private static final AppModule_ProvideNativeInferenceEngineFactory INSTANCE = new AppModule_ProvideNativeInferenceEngineFactory();
  }
}
