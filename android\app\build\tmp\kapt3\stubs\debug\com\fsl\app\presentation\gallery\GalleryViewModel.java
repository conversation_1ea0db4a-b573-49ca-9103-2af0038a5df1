package com.fsl.app.presentation.gallery;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J$\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u00112\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011H\u0002J\u0006\u0010\u0014\u001a\u00020\u0015J\u000e\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u0012J\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\u0011J\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011J\u0006\u0010\u001b\u001a\u00020\u0015J\u0016\u0010\u001c\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00122\u0006\u0010\u001d\u001a\u00020\u0019J\u0010\u0010\u001e\u001a\u00020\u00152\b\u0010\u001d\u001a\u0004\u0018\u00010\u0019R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/fsl/app/presentation/gallery/GalleryViewModel;", "Landroidx/lifecycle/ViewModel;", "galleryRepository", "Lcom/fsl/app/domain/repository/GalleryRepository;", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "(Lcom/fsl/app/domain/repository/GalleryRepository;Lcom/fsl/app/domain/repository/IInferenceRepository;)V", "_galleryState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/gallery/GalleryState;", "galleryState", "Lkotlinx/coroutines/flow/StateFlow;", "getGalleryState", "()Lkotlinx/coroutines/flow/StateFlow;", "calculateStats", "Lcom/fsl/app/presentation/gallery/GalleryStats;", "allImages", "", "Lcom/fsl/app/domain/model/GalleryImage;", "learnedImages", "clearError", "", "deleteImage", "image", "getLearnedClasses", "", "getSelectedClassImages", "loadGallery", "markAsLearned", "className", "selectClass", "app_debug"})
public final class GalleryViewModel extends androidx.lifecycle.ViewModel {
    private final com.fsl.app.domain.repository.GalleryRepository galleryRepository = null;
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.gallery.GalleryState> _galleryState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.gallery.GalleryState> galleryState = null;
    
    @javax.inject.Inject
    public GalleryViewModel(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.GalleryRepository galleryRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.gallery.GalleryState> getGalleryState() {
        return null;
    }
    
    /**
     * 加载图库
     */
    public final void loadGallery() {
    }
    
    /**
     * 选择分类
     */
    public final void selectClass(@org.jetbrains.annotations.Nullable
    java.lang.String className) {
    }
    
    /**
     * 删除图像
     */
    public final void deleteImage(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.GalleryImage image) {
    }
    
    /**
     * 标记为学习样本
     */
    public final void markAsLearned(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.GalleryImage image, @org.jetbrains.annotations.NotNull
    java.lang.String className) {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 获取当前选择的分类图像
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.fsl.app.domain.model.GalleryImage> getSelectedClassImages() {
        return null;
    }
    
    /**
     * 计算统计信息
     */
    private final com.fsl.app.presentation.gallery.GalleryStats calculateStats(java.util.List<com.fsl.app.domain.model.GalleryImage> allImages, java.util.List<com.fsl.app.domain.model.GalleryImage> learnedImages) {
        return null;
    }
    
    /**
     * 获取学习的类别列表
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getLearnedClasses() {
        return null;
    }
}