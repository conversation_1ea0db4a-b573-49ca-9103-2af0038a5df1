/**
 * 简化的Eigen库头文件
 * 
 * 为了编译需要，提供基本的数学运算功能
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef EIGEN_CORE_H
#define EIGEN_CORE_H

#include <vector>
#include <cmath>
#include <algorithm>

namespace Eigen {

// 简化的向量类
template<typename T>
class VectorX {
public:
    std::vector<T> data;
    
    VectorX() = default;
    VectorX(int size) : data(size) {}
    VectorX(const std::vector<T>& vec) : data(vec) {}
    
    int size() const { return data.size(); }
    void resize(int size) { data.resize(size); }
    
    T& operator[](int i) { return data[i]; }
    const T& operator[](int i) const { return data[i]; }
    
    T& operator()(int i) { return data[i]; }
    const T& operator()(int i) const { return data[i]; }
    
    // 向量运算
    VectorX operator+(const VectorX& other) const {
        VectorX result(size());
        for (int i = 0; i < size(); ++i) {
            result[i] = data[i] + other[i];
        }
        return result;
    }
    
    VectorX operator-(const VectorX& other) const {
        VectorX result(size());
        for (int i = 0; i < size(); ++i) {
            result[i] = data[i] - other[i];
        }
        return result;
    }
    
    VectorX operator*(T scalar) const {
        VectorX result(size());
        for (int i = 0; i < size(); ++i) {
            result[i] = data[i] * scalar;
        }
        return result;
    }
    
    T dot(const VectorX& other) const {
        T result = T(0);
        for (int i = 0; i < size(); ++i) {
            result += data[i] * other[i];
        }
        return result;
    }
    
    T norm() const {
        return std::sqrt(dot(*this));
    }
    
    VectorX normalized() const {
        T n = norm();
        if (n == T(0)) return *this;
        return (*this) * (T(1) / n);
    }
};

// 简化的矩阵类
template<typename T>
class MatrixX {
public:
    std::vector<std::vector<T>> data;
    int rows_, cols_;
    
    MatrixX() : rows_(0), cols_(0) {}
    MatrixX(int rows, int cols) : rows_(rows), cols_(cols), data(rows, std::vector<T>(cols)) {}
    
    int rows() const { return rows_; }
    int cols() const { return cols_; }
    
    std::vector<T>& operator[](int i) { return data[i]; }
    const std::vector<T>& operator[](int i) const { return data[i]; }
    
    T& operator()(int i, int j) { return data[i][j]; }
    const T& operator()(int i, int j) const { return data[i][j]; }
    
    void resize(int rows, int cols) {
        rows_ = rows;
        cols_ = cols;
        data.resize(rows);
        for (auto& row : data) {
            row.resize(cols);
        }
    }
    
    // 矩阵向量乘法
    VectorX<T> operator*(const VectorX<T>& vec) const {
        VectorX<T> result(rows_);
        for (int i = 0; i < rows_; ++i) {
            T sum = T(0);
            for (int j = 0; j < cols_; ++j) {
                sum += data[i][j] * vec[j];
            }
            result[i] = sum;
        }
        return result;
    }
};

// 类型别名
using VectorXf = VectorX<float>;
using VectorXd = VectorX<double>;
using MatrixXf = MatrixX<float>;
using MatrixXd = MatrixX<double>;

// 数学函数
namespace internal {
    template<typename T>
    T abs(T x) { return std::abs(x); }
    
    template<typename T>
    T sqrt(T x) { return std::sqrt(x); }
    
    template<typename T>
    T exp(T x) { return std::exp(x); }
    
    template<typename T>
    T log(T x) { return std::log(x); }
}

} // namespace Eigen

#endif // EIGEN_CORE_H
