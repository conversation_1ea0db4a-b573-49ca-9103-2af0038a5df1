// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.di;

import com.fsl.app.data.utils.ImageProcessor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AppModule_ProvideImageProcessorFactory implements Factory<ImageProcessor> {
  @Override
  public ImageProcessor get() {
    return provideImageProcessor();
  }

  public static AppModule_ProvideImageProcessorFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ImageProcessor provideImageProcessor() {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideImageProcessor());
  }

  private static final class InstanceHolder {
    private static final AppModule_ProvideImageProcessorFactory INSTANCE = new AppModule_ProvideImageProcessorFactory();
  }
}
