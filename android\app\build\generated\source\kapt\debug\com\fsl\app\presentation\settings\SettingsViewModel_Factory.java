// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.settings;

import com.fsl.app.domain.usecase.ModelManagementUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<ModelManagementUseCase> modelManagementUseCaseProvider;

  public SettingsViewModel_Factory(
      Provider<ModelManagementUseCase> modelManagementUseCaseProvider) {
    this.modelManagementUseCaseProvider = modelManagementUseCaseProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(modelManagementUseCaseProvider.get());
  }

  public static SettingsViewModel_Factory create(
      Provider<ModelManagementUseCase> modelManagementUseCaseProvider) {
    return new SettingsViewModel_Factory(modelManagementUseCaseProvider);
  }

  public static SettingsViewModel newInstance(ModelManagementUseCase modelManagementUseCase) {
    return new SettingsViewModel(modelManagementUseCase);
  }
}
