/ Header Record For PersistentHashMapValueStorageW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\ClassificationUseCase.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\ImageProcessingUseCase.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\IncrementalLearningUseCase.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\IncrementalLearningUseCase.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\IncrementalLearningUseCase.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\IncrementalLearningUseCase.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\IncrementalLearningUseCase.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\IncrementalLearningUseCase.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\usecase\ModelManagementUseCase.ktJ I$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\MainViewModel.ktJ I$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\MainViewModel.kt> =$PROJECT_DIR$\app\src\main\java\com\fsl\app\FSLApplication.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\MainActivity.ktH G$PROJECT_DIR$\app\src\main\java\com\fsl\app\ui\gallery\GalleryScreen.ktQ P$PROJECT_DIR$\app\src\main\java\com\fsl\app\ui\components\BottomNavigationBar.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.kt\ [$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\repository\IImageProcessingRepository.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\repository\IInferenceRepository.ktR Q$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\repository\IModelRepository.ktY X$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\repository\ImageProcessingRepository.ktO N$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\repository\ModelRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\settings\SettingsViewModel.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktQ P$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\ClassificationResult.ktQ P$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\ClassificationResult.ktQ P$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\ClassificationResult.ktQ P$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\ClassificationResult.ktI H$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\LearnedClass.ktI H$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\database\AppDatabase.ktI H$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\database\AppDatabase.ktM L$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\database\LearnedClassDao.ktM L$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\database\LearnedClassDao.ktP O$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\database\LearnedClassEntity.ktP O$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\database\LearnedClassEntity.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\di\AppModule.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\di\AppModule.ktE D$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\utils\AssetUtils.ktF E$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\utils\FileManager.ktF E$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\utils\FileManager.ktF E$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\utils\FileManager.ktI H$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\utils\ImageProcessor.ktI H$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\utils\ImageProcessor.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\repository\GalleryRepositoryImpl.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\repository\GalleryRepositoryImpl.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\repository\GalleryRepositoryImpl.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktI H$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\GalleryImage.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\di\AppModule.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\di\AppModule.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\repository\GalleryRepository.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\repository\IInferenceRepository.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\MainActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\MainActivity.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\gallery\GalleryViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\gallery\GalleryViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\gallery\GalleryViewModel.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\MainActivity.ktP O$PROJECT_DIR$\app\src\main\java\com\fsl\app\ui\components\BoundingBoxOverlay.ktP O$PROJECT_DIR$\app\src\main\java\com\fsl\app\ui\components\BoundingBoxOverlay.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\di\AppModule.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\di\AppModule.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktK J$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\TrackingResult.ktK J$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\TrackingResult.ktK J$PROJECT_DIR$\app\src\main\java\com\fsl\app\domain\model\TrackingResult.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktT S$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\NativeInferenceEngine.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\MainActivity.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktW V$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\LearningViewModel.ktJ I$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\MainViewModel.ktJ I$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\MainViewModel.kt< ;$PROJECT_DIR$\app\src\main\java\com\fsl\app\MainActivity.ktJ I$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\MainViewModel.ktJ I$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\MainViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.ktU T$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\PyTorchInferenceEngine.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.ktV U$PROJECT_DIR$\app\src\main\java\com\fsl\app\data\inference\RealTimeInferenceEngine.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.kt_ ^$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\learning\SampleCollectionViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.ktS R$PROJECT_DIR$\app\src\main\java\com\fsl\app\presentation\camera\CameraViewModel.kt