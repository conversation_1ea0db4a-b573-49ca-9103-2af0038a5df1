/**
 * 图像处理器实现
 * 
 * 图像预处理和后处理的C++实现
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "include/image_processor.h"
#include <algorithm>
#include <cmath>
#include <android/log.h>

#define LOG_TAG "ImageProcessor"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

namespace fsl {

ImageProcessor::ImageProcessor() = default;
ImageProcessor::~ImageProcessor() = default;

std::vector<float> ImageProcessor::preprocessImage(const float* imageData, int width, int height, int channels) {
    LOGI("Preprocessing image: %dx%dx%d", width, height, channels);
    
    try {
        // 确保输入是224x224x3
        if (width != 224 || height != 224 || channels != 3) {
            LOGE("Invalid input dimensions: %dx%dx%d (expected 224x224x3)", width, height, channels);
            return std::vector<float>();
        }
        
        int totalPixels = width * height * channels;
        std::vector<float> processed(totalPixels);
        
        // ImageNet标准化参数
        const float mean[3] = {0.485f, 0.456f, 0.406f};
        const float std[3] = {0.229f, 0.224f, 0.225f};
        
        // 归一化和标准化
        for (int i = 0; i < totalPixels; i += 3) {
            // RGB通道
            for (int c = 0; c < 3; ++c) {
                float pixel = imageData[i + c];
                // 归一化到[0,1]
                pixel = pixel / 255.0f;
                // 标准化
                pixel = (pixel - mean[c]) / std[c];
                processed[i + c] = pixel;
            }
        }
        
        LOGI("Image preprocessing completed");
        return processed;
        
    } catch (const std::exception& e) {
        LOGE("Exception in preprocessImage: %s", e.what());
        return std::vector<float>();
    }
}

std::vector<float> ImageProcessor::resizeImage(const float* imageData, int srcWidth, int srcHeight, 
                                              int dstWidth, int dstHeight, int channels) {
    LOGI("Resizing image: %dx%d -> %dx%d", srcWidth, srcHeight, dstWidth, dstHeight);
    
    try {
        std::vector<float> resized(dstWidth * dstHeight * channels);
        
        float scaleX = static_cast<float>(srcWidth) / dstWidth;
        float scaleY = static_cast<float>(srcHeight) / dstHeight;
        
        for (int y = 0; y < dstHeight; ++y) {
            for (int x = 0; x < dstWidth; ++x) {
                // 双线性插值
                float srcX = x * scaleX;
                float srcY = y * scaleY;
                
                int x1 = static_cast<int>(srcX);
                int y1 = static_cast<int>(srcY);
                int x2 = std::min(x1 + 1, srcWidth - 1);
                int y2 = std::min(y1 + 1, srcHeight - 1);
                
                float dx = srcX - x1;
                float dy = srcY - y1;
                
                for (int c = 0; c < channels; ++c) {
                    // 获取四个邻近像素
                    float p11 = imageData[(y1 * srcWidth + x1) * channels + c];
                    float p12 = imageData[(y1 * srcWidth + x2) * channels + c];
                    float p21 = imageData[(y2 * srcWidth + x1) * channels + c];
                    float p22 = imageData[(y2 * srcWidth + x2) * channels + c];
                    
                    // 双线性插值
                    float interpolated = p11 * (1 - dx) * (1 - dy) +
                                       p12 * dx * (1 - dy) +
                                       p21 * (1 - dx) * dy +
                                       p22 * dx * dy;
                    
                    resized[(y * dstWidth + x) * channels + c] = interpolated;
                }
            }
        }
        
        LOGI("Image resizing completed");
        return resized;
        
    } catch (const std::exception& e) {
        LOGE("Exception in resizeImage: %s", e.what());
        return std::vector<float>();
    }
}

std::vector<float> ImageProcessor::augmentImage(const float* imageData, int width, int height, int channels,
                                               AugmentationType type) {
    LOGI("Augmenting image with type: %d", static_cast<int>(type));
    
    try {
        std::vector<float> augmented(width * height * channels);
        
        switch (type) {
            case AugmentationType::HORIZONTAL_FLIP:
                return horizontalFlip(imageData, width, height, channels);
                
            case AugmentationType::ROTATION:
                return rotate(imageData, width, height, channels, 15.0f); // 15度旋转
                
            case AugmentationType::BRIGHTNESS:
                return adjustBrightness(imageData, width, height, channels, 0.2f);
                
            case AugmentationType::CONTRAST:
                return adjustContrast(imageData, width, height, channels, 1.2f);
                
            case AugmentationType::NOISE:
                return addNoise(imageData, width, height, channels, 0.01f);
                
            default:
                // 默认返回原图
                std::copy(imageData, imageData + width * height * channels, augmented.begin());
                return augmented;
        }
        
    } catch (const std::exception& e) {
        LOGE("Exception in augmentImage: %s", e.what());
        return std::vector<float>();
    }
}

std::vector<float> ImageProcessor::horizontalFlip(const float* imageData, int width, int height, int channels) {
    std::vector<float> flipped(width * height * channels);
    
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            int srcIdx = (y * width + x) * channels;
            int dstIdx = (y * width + (width - 1 - x)) * channels;
            
            for (int c = 0; c < channels; ++c) {
                flipped[dstIdx + c] = imageData[srcIdx + c];
            }
        }
    }
    
    return flipped;
}

std::vector<float> ImageProcessor::rotate(const float* imageData, int width, int height, int channels, float angle) {
    std::vector<float> rotated(width * height * channels, 0.0f);
    
    float radians = angle * M_PI / 180.0f;
    float cosA = std::cos(radians);
    float sinA = std::sin(radians);
    
    int centerX = width / 2;
    int centerY = height / 2;
    
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            // 计算旋转后的坐标
            int srcX = static_cast<int>((x - centerX) * cosA - (y - centerY) * sinA + centerX);
            int srcY = static_cast<int>((x - centerX) * sinA + (y - centerY) * cosA + centerY);
            
            if (srcX >= 0 && srcX < width && srcY >= 0 && srcY < height) {
                int srcIdx = (srcY * width + srcX) * channels;
                int dstIdx = (y * width + x) * channels;
                
                for (int c = 0; c < channels; ++c) {
                    rotated[dstIdx + c] = imageData[srcIdx + c];
                }
            }
        }
    }
    
    return rotated;
}

std::vector<float> ImageProcessor::adjustBrightness(const float* imageData, int width, int height, int channels, float factor) {
    std::vector<float> adjusted(width * height * channels);
    
    for (int i = 0; i < width * height * channels; ++i) {
        adjusted[i] = std::clamp(imageData[i] + factor, 0.0f, 1.0f);
    }
    
    return adjusted;
}

std::vector<float> ImageProcessor::adjustContrast(const float* imageData, int width, int height, int channels, float factor) {
    std::vector<float> adjusted(width * height * channels);
    
    for (int i = 0; i < width * height * channels; ++i) {
        adjusted[i] = std::clamp((imageData[i] - 0.5f) * factor + 0.5f, 0.0f, 1.0f);
    }
    
    return adjusted;
}

std::vector<float> ImageProcessor::addNoise(const float* imageData, int width, int height, int channels, float intensity) {
    std::vector<float> noisy(width * height * channels);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<float> dist(0.0f, intensity);
    
    for (int i = 0; i < width * height * channels; ++i) {
        float noise = dist(gen);
        noisy[i] = std::clamp(imageData[i] + noise, 0.0f, 1.0f);
    }
    
    return noisy;
}

std::vector<float> ImageProcessor::centerCrop(const float* imageData, int srcWidth, int srcHeight, 
                                             int cropWidth, int cropHeight, int channels) {
    LOGI("Center cropping: %dx%d from %dx%d", cropWidth, cropHeight, srcWidth, srcHeight);
    
    std::vector<float> cropped(cropWidth * cropHeight * channels);
    
    int startX = (srcWidth - cropWidth) / 2;
    int startY = (srcHeight - cropHeight) / 2;
    
    for (int y = 0; y < cropHeight; ++y) {
        for (int x = 0; x < cropWidth; ++x) {
            int srcIdx = ((startY + y) * srcWidth + (startX + x)) * channels;
            int dstIdx = (y * cropWidth + x) * channels;
            
            for (int c = 0; c < channels; ++c) {
                cropped[dstIdx + c] = imageData[srcIdx + c];
            }
        }
    }
    
    return cropped;
}

std::vector<float> ImageProcessor::randomCrop(const float* imageData, int srcWidth, int srcHeight,
                                             int cropWidth, int cropHeight, int channels) {
    std::random_device rd;
    std::mt19937 gen(rd());
    
    int maxStartX = srcWidth - cropWidth;
    int maxStartY = srcHeight - cropHeight;
    
    std::uniform_int_distribution<int> distX(0, std::max(0, maxStartX));
    std::uniform_int_distribution<int> distY(0, std::max(0, maxStartY));
    
    int startX = distX(gen);
    int startY = distY(gen);
    
    std::vector<float> cropped(cropWidth * cropHeight * channels);
    
    for (int y = 0; y < cropHeight; ++y) {
        for (int x = 0; x < cropWidth; ++x) {
            int srcIdx = ((startY + y) * srcWidth + (startX + x)) * channels;
            int dstIdx = (y * cropWidth + x) * channels;
            
            for (int c = 0; c < channels; ++c) {
                cropped[dstIdx + c] = imageData[srcIdx + c];
            }
        }
    }
    
    return cropped;
}

} // namespace fsl
