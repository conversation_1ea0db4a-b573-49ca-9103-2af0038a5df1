package com.fsl.app.data.database;

import java.lang.System;

@androidx.room.Dao
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0019\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\fH\'J\u001b\u0010\u000e\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u0011\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0011J\u0019\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J)\u0010\u0014\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0016\u001a\u00020\u0017H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0018\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u0019"}, d2 = {"Lcom/fsl/app/data/database/LearnedClassDao;", "", "deleteClass", "", "classEntity", "Lcom/fsl/app/data/database/LearnedClassEntity;", "(Lcom/fsl/app/data/database/LearnedClassEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteClassByName", "className", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllClasses", "Lkotlinx/coroutines/flow/Flow;", "", "getClassByName", "getClassCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertClass", "updateClass", "updateSampleCount", "sampleCount", "updatedAt", "", "(Ljava/lang/String;IJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface LearnedClassDao {
    
    /**
     * 获取所有已学习的类别
     */
    @org.jetbrains.annotations.NotNull
    @androidx.room.Query(value = "SELECT * FROM learned_classes ORDER BY updatedAt DESC")
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.fsl.app.data.database.LearnedClassEntity>> getAllClasses();
    
    /**
     * 根据名称获取类别
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "SELECT * FROM learned_classes WHERE name = :className")
    public abstract java.lang.Object getClassByName(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.fsl.app.data.database.LearnedClassEntity> continuation);
    
    /**
     * 插入或更新类别
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Insert(onConflict = androidx.room.OnConflictStrategy.REPLACE)
    public abstract java.lang.Object insertClass(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.LearnedClassEntity classEntity, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 更新类别
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Update
    public abstract java.lang.Object updateClass(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.LearnedClassEntity classEntity, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 删除类别
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Delete
    public abstract java.lang.Object deleteClass(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.LearnedClassEntity classEntity, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 根据名称删除类别
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "DELETE FROM learned_classes WHERE name = :className")
    public abstract java.lang.Object deleteClassByName(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 获取类别数量
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "SELECT COUNT(*) FROM learned_classes")
    public abstract java.lang.Object getClassCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation);
    
    /**
     * 更新样本数量
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "UPDATE learned_classes SET sampleCount = :sampleCount, updatedAt = :updatedAt WHERE name = :className")
    public abstract java.lang.Object updateSampleCount(@org.jetbrains.annotations.NotNull
    java.lang.String className, int sampleCount, long updatedAt, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
}