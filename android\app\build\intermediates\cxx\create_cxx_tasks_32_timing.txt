# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 68ms
    create-module-model completed in 76ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 64ms
    [gap of 14ms]
  create-initial-cxx-model completed in 170ms
  [gap of 10ms]
create_cxx_tasks completed in 180ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 74ms
    create-module-model completed in 83ms
    create-module-model
      create-cmake-model 69ms
    create-module-model completed in 73ms
    [gap of 21ms]
  create-initial-cxx-model completed in 196ms
  [gap of 10ms]
create_cxx_tasks completed in 207ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 69ms
    create-module-model completed in 79ms
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 68ms
    [gap of 19ms]
  create-initial-cxx-model completed in 184ms
create_cxx_tasks completed in 193ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 76ms
    create-module-model completed in 86ms
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 68ms
    [gap of 19ms]
  create-initial-cxx-model completed in 191ms
create_cxx_tasks completed in 201ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 71ms
    create-module-model completed in 82ms
    create-module-model
      create-cmake-model 68ms
    create-module-model completed in 72ms
    [gap of 17ms]
  create-initial-cxx-model completed in 189ms
  [gap of 11ms]
create_cxx_tasks completed in 200ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 74ms
    create-module-model
      create-cmake-model 60ms
    create-module-model completed in 62ms
    [gap of 17ms]
  create-initial-cxx-model completed in 170ms
create_cxx_tasks completed in 179ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 75ms
    create-module-model completed in 84ms
    create-module-model
      create-cmake-model 74ms
    create-module-model completed in 78ms
    [gap of 17ms]
  create-initial-cxx-model completed in 197ms
create_cxx_tasks completed in 206ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 68ms
    create-module-model
      create-cmake-model 54ms
    create-module-model completed in 61ms
    [gap of 16ms]
  create-initial-cxx-model completed in 161ms
create_cxx_tasks completed in 170ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 73ms
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 68ms
    [gap of 15ms]
  create-initial-cxx-model completed in 172ms
create_cxx_tasks completed in 181ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 56ms
    create-module-model completed in 65ms
    create-module-model
      create-cmake-model 52ms
    create-module-model completed in 57ms
    [gap of 18ms]
  create-initial-cxx-model completed in 155ms
  [gap of 10ms]
create_cxx_tasks completed in 165ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 68ms
    create-module-model completed in 76ms
    create-module-model
      create-cmake-model 60ms
    create-module-model completed in 64ms
    [gap of 17ms]
  create-initial-cxx-model completed in 172ms
create_cxx_tasks completed in 181ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 71ms
    create-module-model completed in 81ms
    create-module-model
      create-cmake-model 97ms
    create-module-model completed in 100ms
    [gap of 15ms]
  create-initial-cxx-model completed in 214ms
  [gap of 10ms]
create_cxx_tasks completed in 224ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 72ms
    create-module-model completed in 80ms
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 68ms
    [gap of 17ms]
  create-initial-cxx-model completed in 185ms
  [gap of 11ms]
create_cxx_tasks completed in 197ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 61ms
    create-module-model completed in 71ms
    create-module-model
      create-cmake-model 54ms
    create-module-model completed in 57ms
    [gap of 16ms]
  create-initial-cxx-model completed in 159ms
create_cxx_tasks completed in 168ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 73ms
    create-module-model completed in 82ms
    create-module-model
      create-cmake-model 116ms
    create-module-model completed in 121ms
    [gap of 16ms]
  create-initial-cxx-model completed in 236ms
create_cxx_tasks completed in 245ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 67ms
    create-module-model
      create-cmake-model 53ms
    create-module-model completed in 56ms
    [gap of 16ms]
  create-initial-cxx-model completed in 155ms
create_cxx_tasks completed in 163ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 73ms
    create-module-model completed in 82ms
    create-module-model
      create-cmake-model 73ms
    create-module-model completed in 78ms
    [gap of 15ms]
  create-initial-cxx-model completed in 190ms
  [gap of 10ms]
create_cxx_tasks completed in 200ms

