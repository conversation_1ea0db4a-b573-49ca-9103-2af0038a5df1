# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 72ms
    create-module-model
      create-cmake-model 55ms
    create-module-model completed in 58ms
    [gap of 15ms]
  create-initial-cxx-model completed in 159ms
create_cxx_tasks completed in 169ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 73ms
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 68ms
    [gap of 15ms]
  create-initial-cxx-model completed in 172ms
create_cxx_tasks completed in 180ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 72ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 64ms
    [gap of 19ms]
  create-initial-cxx-model completed in 174ms
create_cxx_tasks completed in 183ms

