# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 76ms
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 66ms
    [gap of 17ms]
  create-initial-cxx-model completed in 177ms
  [gap of 10ms]
create_cxx_tasks completed in 187ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 73ms
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 66ms
    [gap of 17ms]
  create-initial-cxx-model completed in 173ms
  [gap of 10ms]
create_cxx_tasks completed in 183ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 60ms
    create-module-model completed in 69ms
    create-module-model
      create-cmake-model 55ms
    create-module-model completed in 59ms
    [gap of 17ms]
  create-initial-cxx-model completed in 160ms
create_cxx_tasks completed in 169ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 61ms
    create-module-model completed in 71ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 64ms
    [gap of 15ms]
  create-initial-cxx-model completed in 165ms
  [gap of 12ms]
create_cxx_tasks completed in 177ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 73ms
    create-module-model
      create-cmake-model 57ms
    create-module-model completed in 61ms
    [gap of 36ms]
  create-initial-cxx-model completed in 189ms
  [gap of 22ms]
create_cxx_tasks completed in 211ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 72ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 63ms
    [gap of 15ms]
  create-initial-cxx-model completed in 168ms
  [gap of 13ms]
create_cxx_tasks completed in 181ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 70ms
    create-module-model completed in 78ms
    create-module-model
      create-cmake-model 57ms
    create-module-model completed in 61ms
    [gap of 15ms]
  create-initial-cxx-model completed in 169ms
  [gap of 10ms]
create_cxx_tasks completed in 179ms

