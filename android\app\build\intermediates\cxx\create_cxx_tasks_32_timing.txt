# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 72ms
    create-module-model
      create-cmake-model 55ms
    create-module-model completed in 58ms
    [gap of 15ms]
  create-initial-cxx-model completed in 159ms
create_cxx_tasks completed in 169ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 65ms
    create-module-model completed in 73ms
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 68ms
    [gap of 15ms]
  create-initial-cxx-model completed in 172ms
create_cxx_tasks completed in 180ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 62ms
    create-module-model completed in 72ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 64ms
    [gap of 19ms]
  create-initial-cxx-model completed in 174ms
create_cxx_tasks completed in 183ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 64ms
    create-module-model completed in 75ms
    create-module-model
      create-cmake-model 59ms
    create-module-model completed in 64ms
    [gap of 14ms]
  create-initial-cxx-model completed in 168ms
create_cxx_tasks completed in 177ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 58ms
    create-module-model completed in 66ms
    create-module-model
      create-cmake-model 54ms
    create-module-model completed in 58ms
    [gap of 15ms]
  create-initial-cxx-model completed in 156ms
create_cxx_tasks completed in 165ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 68ms
    create-module-model completed in 78ms
    create-module-model
      create-cmake-model 58ms
    create-module-model completed in 63ms
    [gap of 19ms]
  create-initial-cxx-model completed in 176ms
  [gap of 11ms]
create_cxx_tasks completed in 187ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 67ms
    create-module-model completed in 76ms
    create-module-model
      create-cmake-model 54ms
    create-module-model completed in 59ms
    [gap of 17ms]
  create-initial-cxx-model completed in 169ms
create_cxx_tasks completed in 178ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 74ms
    create-module-model completed in 85ms
    create-module-model
      create-cmake-model 66ms
    create-module-model completed in 72ms
    [gap of 24ms]
  create-initial-cxx-model completed in 202ms
  [gap of 13ms]
create_cxx_tasks completed in 215ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 61ms
    create-module-model completed in 69ms
    create-module-model
      create-cmake-model 63ms
    create-module-model completed in 67ms
    [gap of 15ms]
  create-initial-cxx-model completed in 164ms
create_cxx_tasks completed in 173ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 81ms
    create-module-model completed in 93ms
    create-module-model
      create-cmake-model 84ms
    create-module-model completed in 89ms
    [gap of 19ms]
  create-initial-cxx-model completed in 221ms
  [gap of 13ms]
create_cxx_tasks completed in 234ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 57ms
    create-module-model completed in 65ms
    create-module-model
      create-cmake-model 54ms
    create-module-model completed in 59ms
    [gap of 15ms]
  create-initial-cxx-model completed in 156ms
  [gap of 10ms]
create_cxx_tasks completed in 166ms

