package com.fsl.app.presentation.camera;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001fJ\u0010\u0010 \u001a\u00020\u001c2\b\u0010!\u001a\u0004\u0018\u00010\"J\u0006\u0010#\u001a\u00020\u001cJ\b\u0010$\u001a\u00020\u001fH\u0002J\u0006\u0010%\u001a\u00020&J\u000e\u0010\'\u001a\u00020\u001c2\u0006\u0010(\u001a\u00020)J\u0006\u0010*\u001a\u00020\u001cJ#\u0010+\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\b\u0010\u0017\u001a\u0004\u0018\u00010\u000fH\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010,J\u0006\u0010-\u001a\u00020\u001cJ\u0006\u0010.\u001a\u00020\u001cR\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00110\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\r0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0019\u0010\u0017\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00110\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00110\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0016\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006/"}, d2 = {"Lcom/fsl/app/presentation/camera/CameraViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "classificationUseCase", "Lcom/fsl/app/domain/usecase/ClassificationUseCase;", "imageProcessingUseCase", "Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;", "galleryRepository", "Lcom/fsl/app/data/repository/GalleryRepositoryImpl;", "(Landroid/app/Application;Lcom/fsl/app/domain/usecase/ClassificationUseCase;Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;Lcom/fsl/app/data/repository/GalleryRepositoryImpl;)V", "_cameraState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/camera/CameraState;", "_classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "_isFrontCamera", "", "_isRealTimeMode", "cameraState", "Lkotlinx/coroutines/flow/StateFlow;", "getCameraState", "()Lkotlinx/coroutines/flow/StateFlow;", "classificationResult", "getClassificationResult", "isFrontCamera", "isRealTimeMode", "captureImage", "", "classifyBitmap", "bitmap", "Landroid/graphics/Bitmap;", "classifyImage", "imageProxy", "Landroidx/camera/core/ImageProxy;", "clearClassificationResult", "createMockClassificationBitmap", "getCameraConfig", "Lcom/fsl/app/presentation/camera/CameraConfig;", "handleCameraError", "error", "", "resetCameraState", "saveImageToGallery", "(Landroid/graphics/Bitmap;Lcom/fsl/app/domain/model/ClassificationResult;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "switchCamera", "toggleRealTimeMode", "app_debug"})
public final class CameraViewModel extends androidx.lifecycle.AndroidViewModel {
    private final com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase = null;
    private final com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase = null;
    private final com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.camera.CameraState> _cameraState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.camera.CameraState> cameraState = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.domain.model.ClassificationResult> _classificationResult = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.domain.model.ClassificationResult> classificationResult = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isRealTimeMode = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRealTimeMode = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isFrontCamera = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isFrontCamera = null;
    
    @javax.inject.Inject
    public CameraViewModel(@org.jetbrains.annotations.NotNull
    android.app.Application application, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.camera.CameraState> getCameraState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.domain.model.ClassificationResult> getClassificationResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRealTimeMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isFrontCamera() {
        return null;
    }
    
    /**
     * 分类图像
     *
     * @param imageProxy 相机图像代理
     */
    public final void classifyImage(@org.jetbrains.annotations.Nullable
    androidx.camera.core.ImageProxy imageProxy) {
    }
    
    /**
     * 分类Bitmap图像
     *
     * @param bitmap 图像位图
     */
    public final void classifyBitmap(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 捕获图像（手动模式）
     */
    public final void captureImage() {
    }
    
    /**
     * 切换相机（前置/后置）
     */
    public final void switchCamera() {
    }
    
    /**
     * 切换实时/手动模式
     */
    public final void toggleRealTimeMode() {
    }
    
    /**
     * 清除分类结果
     */
    public final void clearClassificationResult() {
    }
    
    /**
     * 重置相机状态
     */
    public final void resetCameraState() {
    }
    
    /**
     * 处理相机错误
     */
    public final void handleCameraError(@org.jetbrains.annotations.NotNull
    java.lang.String error) {
    }
    
    /**
     * 获取当前相机配置
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.camera.CameraConfig getCameraConfig() {
        return null;
    }
    
    /**
     * 创建模拟分类用的Bitmap
     */
    private final android.graphics.Bitmap createMockClassificationBitmap() {
        return null;
    }
    
    /**
     * 保存图像到图库
     */
    private final java.lang.Object saveImageToGallery(android.graphics.Bitmap bitmap, com.fsl.app.domain.model.ClassificationResult classificationResult, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
}