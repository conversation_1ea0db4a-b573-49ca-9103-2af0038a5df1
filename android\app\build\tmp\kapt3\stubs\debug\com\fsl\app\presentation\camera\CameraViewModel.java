package com.fsl.app.presentation.camera;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B/\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u0006\u0010$\u001a\u00020%J\u000e\u0010&\u001a\u00020%2\u0006\u0010\'\u001a\u00020!J\u0010\u0010(\u001a\u00020%2\b\u0010)\u001a\u0004\u0018\u00010*J\u0006\u0010+\u001a\u00020%J\u0018\u0010,\u001a\u00020!2\u0006\u0010-\u001a\u00020.2\u0006\u0010/\u001a\u000200H\u0002J\u0006\u00101\u001a\u00020%J\u0006\u00102\u001a\u000203J\u000e\u00104\u001a\u00020%2\u0006\u00105\u001a\u00020.J\b\u00106\u001a\u00020%H\u0014J\u0006\u00107\u001a\u00020%J\u0018\u00108\u001a\u00020%2\u0006\u0010\'\u001a\u00020!2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0011J#\u00109\u001a\u00020%2\u0006\u0010\'\u001a\u00020!2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0011H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010:J\u0006\u0010;\u001a\u00020%J\u0006\u0010<\u001a\u00020%R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00130\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00160\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0019\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001bR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00130\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001bR\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00130\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001bR\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00160\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001b\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006="}, d2 = {"Lcom/fsl/app/presentation/camera/CameraViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "classificationUseCase", "Lcom/fsl/app/domain/usecase/ClassificationUseCase;", "imageProcessingUseCase", "Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;", "galleryRepository", "Lcom/fsl/app/data/repository/GalleryRepositoryImpl;", "realTimeInferenceEngine", "Lcom/fsl/app/data/inference/RealTimeInferenceEngine;", "(Landroid/app/Application;Lcom/fsl/app/domain/usecase/ClassificationUseCase;Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;Lcom/fsl/app/data/repository/GalleryRepositoryImpl;Lcom/fsl/app/data/inference/RealTimeInferenceEngine;)V", "_cameraState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/camera/CameraState;", "_classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "_isFrontCamera", "", "_isRealTimeMode", "_trackingResults", "", "Lcom/fsl/app/domain/model/TrackingResult;", "cameraState", "Lkotlinx/coroutines/flow/StateFlow;", "getCameraState", "()Lkotlinx/coroutines/flow/StateFlow;", "classificationResult", "getClassificationResult", "isFrontCamera", "isRealTimeMode", "lastCapturedBitmap", "Landroid/graphics/Bitmap;", "trackingResults", "getTrackingResults", "captureImage", "", "classifyBitmap", "bitmap", "classifyImage", "imageProxy", "Landroidx/camera/core/ImageProxy;", "clearClassificationResult", "createTestBitmap", "className", "", "color", "", "createTestImages", "getCameraConfig", "Lcom/fsl/app/presentation/camera/CameraConfig;", "handleCameraError", "error", "onCleared", "resetCameraState", "saveImageToGallery", "saveImageToGalleryInternal", "(Landroid/graphics/Bitmap;Lcom/fsl/app/domain/model/ClassificationResult;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "switchCamera", "toggleRealTimeMode", "app_debug"})
public final class CameraViewModel extends androidx.lifecycle.AndroidViewModel {
    private final com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase = null;
    private final com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase = null;
    private final com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository = null;
    private final com.fsl.app.data.inference.RealTimeInferenceEngine realTimeInferenceEngine = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.camera.CameraState> _cameraState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.camera.CameraState> cameraState = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.domain.model.ClassificationResult> _classificationResult = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.domain.model.ClassificationResult> classificationResult = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.fsl.app.domain.model.TrackingResult>> _trackingResults = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.fsl.app.domain.model.TrackingResult>> trackingResults = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isRealTimeMode = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRealTimeMode = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isFrontCamera = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isFrontCamera = null;
    private android.graphics.Bitmap lastCapturedBitmap;
    
    @javax.inject.Inject
    public CameraViewModel(@org.jetbrains.annotations.NotNull
    android.app.Application application, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.inference.RealTimeInferenceEngine realTimeInferenceEngine) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.camera.CameraState> getCameraState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.domain.model.ClassificationResult> getClassificationResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.fsl.app.domain.model.TrackingResult>> getTrackingResults() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRealTimeMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isFrontCamera() {
        return null;
    }
    
    /**
     * 分类图像
     *
     * @param imageProxy 相机图像代理
     */
    public final void classifyImage(@org.jetbrains.annotations.Nullable
    androidx.camera.core.ImageProxy imageProxy) {
    }
    
    /**
     * 分类Bitmap图像
     *
     * @param bitmap 图像位图
     */
    public final void classifyBitmap(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 捕获图像（手动模式）
     * 注意：这个方法需要与CameraPreview组件配合，实际捕获相机图像
     */
    public final void captureImage() {
    }
    
    /**
     * 切换相机（前置/后置）
     */
    public final void switchCamera() {
    }
    
    /**
     * 切换实时/手动模式
     */
    public final void toggleRealTimeMode() {
    }
    
    /**
     * 清除分类结果
     */
    public final void clearClassificationResult() {
    }
    
    /**
     * 重置相机状态
     */
    public final void resetCameraState() {
    }
    
    /**
     * 处理相机错误
     */
    public final void handleCameraError(@org.jetbrains.annotations.NotNull
    java.lang.String error) {
    }
    
    /**
     * 获取当前相机配置
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.camera.CameraConfig getCameraConfig() {
        return null;
    }
    
    /**
     * 保存真实图像到图库
     */
    public final void saveImageToGallery(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult classificationResult) {
    }
    
    /**
     * 保存图像到图库（内部实现）
     */
    private final java.lang.Object saveImageToGalleryInternal(android.graphics.Bitmap bitmap, com.fsl.app.domain.model.ClassificationResult classificationResult, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 创建测试图片（用于演示图库功能）
     */
    public final void createTestImages() {
    }
    
    /**
     * 创建测试用的Bitmap
     */
    private final android.graphics.Bitmap createTestBitmap(java.lang.String className, int color) {
        return null;
    }
    
    /**
     * 清理资源
     */
    @java.lang.Override
    protected void onCleared() {
    }
}