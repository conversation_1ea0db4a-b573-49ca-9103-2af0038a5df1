package com.fsl.app.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class TrainingSampleDao_Impl implements TrainingSampleDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TrainingSampleEntity> __insertionAdapterOfTrainingSampleEntity;

  private final EntityDeletionOrUpdateAdapter<TrainingSampleEntity> __deletionAdapterOfTrainingSampleEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteSamplesByClassId;

  public TrainingSampleDao_Impl(RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTrainingSampleEntity = new EntityInsertionAdapter<TrainingSampleEntity>(__db) {
      @Override
      public String createQuery() {
        return "INSERT OR REPLACE INTO `training_samples` (`id`,`classId`,`imagePath`,`features`,`createdAt`) VALUES (?,?,?,?,?)";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, TrainingSampleEntity value) {
        if (value.getId() == null) {
          stmt.bindNull(1);
        } else {
          stmt.bindString(1, value.getId());
        }
        if (value.getClassId() == null) {
          stmt.bindNull(2);
        } else {
          stmt.bindString(2, value.getClassId());
        }
        if (value.getImagePath() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getImagePath());
        }
        if (value.getFeatures() == null) {
          stmt.bindNull(4);
        } else {
          stmt.bindBlob(4, value.getFeatures());
        }
        stmt.bindLong(5, value.getCreatedAt());
      }
    };
    this.__deletionAdapterOfTrainingSampleEntity = new EntityDeletionOrUpdateAdapter<TrainingSampleEntity>(__db) {
      @Override
      public String createQuery() {
        return "DELETE FROM `training_samples` WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, TrainingSampleEntity value) {
        if (value.getId() == null) {
          stmt.bindNull(1);
        } else {
          stmt.bindString(1, value.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteSamplesByClassId = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM training_samples WHERE classId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertSample(final TrainingSampleEntity sample,
      final Continuation<? super Unit> continuation) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfTrainingSampleEntity.insert(sample);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, continuation);
  }

  @Override
  public Object insertSamples(final List<TrainingSampleEntity> samples,
      final Continuation<? super Unit> continuation) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfTrainingSampleEntity.insert(samples);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, continuation);
  }

  @Override
  public Object deleteSample(final TrainingSampleEntity sample,
      final Continuation<? super Unit> continuation) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTrainingSampleEntity.handle(sample);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, continuation);
  }

  @Override
  public Object deleteSamplesByClassId(final String classId,
      final Continuation<? super Unit> continuation) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteSamplesByClassId.acquire();
        int _argIndex = 1;
        if (classId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, classId);
        }
        __db.beginTransaction();
        try {
          _stmt.executeUpdateDelete();
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
          __preparedStmtOfDeleteSamplesByClassId.release(_stmt);
        }
      }
    }, continuation);
  }

  @Override
  public Flow<List<TrainingSampleEntity>> getSamplesByClassId(final String classId) {
    final String _sql = "SELECT * FROM training_samples WHERE classId = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (classId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, classId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[]{"training_samples"}, new Callable<List<TrainingSampleEntity>>() {
      @Override
      public List<TrainingSampleEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfClassId = CursorUtil.getColumnIndexOrThrow(_cursor, "classId");
          final int _cursorIndexOfImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "imagePath");
          final int _cursorIndexOfFeatures = CursorUtil.getColumnIndexOrThrow(_cursor, "features");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<TrainingSampleEntity> _result = new ArrayList<TrainingSampleEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final TrainingSampleEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpClassId;
            if (_cursor.isNull(_cursorIndexOfClassId)) {
              _tmpClassId = null;
            } else {
              _tmpClassId = _cursor.getString(_cursorIndexOfClassId);
            }
            final String _tmpImagePath;
            if (_cursor.isNull(_cursorIndexOfImagePath)) {
              _tmpImagePath = null;
            } else {
              _tmpImagePath = _cursor.getString(_cursorIndexOfImagePath);
            }
            final byte[] _tmpFeatures;
            if (_cursor.isNull(_cursorIndexOfFeatures)) {
              _tmpFeatures = null;
            } else {
              _tmpFeatures = _cursor.getBlob(_cursorIndexOfFeatures);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new TrainingSampleEntity(_tmpId,_tmpClassId,_tmpImagePath,_tmpFeatures,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSampleCountByClassId(final String classId,
      final Continuation<? super Integer> continuation) {
    final String _sql = "SELECT COUNT(*) FROM training_samples WHERE classId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (classId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, classId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if(_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, continuation);
  }

  @Override
  public Object getTotalSampleCount(final Continuation<? super Integer> continuation) {
    final String _sql = "SELECT COUNT(*) FROM training_samples";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if(_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, continuation);
  }

  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
