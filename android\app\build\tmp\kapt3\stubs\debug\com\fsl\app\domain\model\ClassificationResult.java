package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 分类结果
 *
 * @property className 预测的类别名称
 * @property confidence 置信度分数 (0.0 - 1.0)
 * @property allScores 所有类别的分数映射
 * @property inferenceTime 推理耗时（毫秒）
 * @property timestamp 分类时间戳
 */
@kotlinx.parcelize.Parcelize
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010$\n\u0000\n\u0002\u0010\t\n\u0002\b\u0012\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B?\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\u0015\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050\u0007H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\tH\u00c6\u0003J\t\u0010\u0019\u001a\u00020\tH\u00c6\u0003JG\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\tH\u00c6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u00d6\u0003J\u0006\u0010!\u001a\u00020\u0003J\u0006\u0010\"\u001a\u00020#J\"\u0010$\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050&0%2\b\b\u0002\u0010\'\u001a\u00020\u001cJ\t\u0010(\u001a\u00020\u001cH\u00d6\u0001J\u0010\u0010)\u001a\u00020\u001e2\b\b\u0002\u0010*\u001a\u00020\u0005J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020\u001cH\u00d6\u0001R\u001d\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013\u00a8\u00061"}, d2 = {"Lcom/fsl/app/domain/model/ClassificationResult;", "Landroid/os/Parcelable;", "className", "", "confidence", "", "allScores", "", "inferenceTime", "", "timestamp", "(Ljava/lang/String;FLjava/util/Map;JJ)V", "getAllScores", "()Ljava/util/Map;", "getClassName", "()Ljava/lang/String;", "getConfidence", "()F", "getInferenceTime", "()J", "getTimestamp", "component1", "component2", "component3", "component4", "component5", "copy", "describeContents", "", "equals", "", "other", "", "getConfidencePercentage", "getQualityLevel", "Lcom/fsl/app/domain/model/QualityLevel;", "getTopNClasses", "", "Lkotlin/Pair;", "n", "hashCode", "isHighConfidence", "threshold", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
public final class ClassificationResult implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String className = null;
    private final float confidence = 0.0F;
    @org.jetbrains.annotations.NotNull
    private final java.util.Map<java.lang.String, java.lang.Float> allScores = null;
    private final long inferenceTime = 0L;
    private final long timestamp = 0L;
    public static final android.os.Parcelable.Creator<com.fsl.app.domain.model.ClassificationResult> CREATOR = null;
    
    /**
     * 分类结果
     *
     * @property className 预测的类别名称
     * @property confidence 置信度分数 (0.0 - 1.0)
     * @property allScores 所有类别的分数映射
     * @property inferenceTime 推理耗时（毫秒）
     * @property timestamp 分类时间戳
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.ClassificationResult copy(@org.jetbrains.annotations.NotNull
    java.lang.String className, float confidence, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.Float> allScores, long inferenceTime, long timestamp) {
        return null;
    }
    
    /**
     * 分类结果
     *
     * @property className 预测的类别名称
     * @property confidence 置信度分数 (0.0 - 1.0)
     * @property allScores 所有类别的分数映射
     * @property inferenceTime 推理耗时（毫秒）
     * @property timestamp 分类时间戳
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 分类结果
     *
     * @property className 预测的类别名称
     * @property confidence 置信度分数 (0.0 - 1.0)
     * @property allScores 所有类别的分数映射
     * @property inferenceTime 推理耗时（毫秒）
     * @property timestamp 分类时间戳
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 分类结果
     *
     * @property className 预测的类别名称
     * @property confidence 置信度分数 (0.0 - 1.0)
     * @property allScores 所有类别的分数映射
     * @property inferenceTime 推理耗时（毫秒）
     * @property timestamp 分类时间戳
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ClassificationResult(@org.jetbrains.annotations.NotNull
    java.lang.String className, float confidence, @org.jetbrains.annotations.NotNull
    java.util.Map<java.lang.String, java.lang.Float> allScores, long inferenceTime, long timestamp) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getClassName() {
        return null;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.Float> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.Float> getAllScores() {
        return null;
    }
    
    public final long component4() {
        return 0L;
    }
    
    public final long getInferenceTime() {
        return 0L;
    }
    
    public final long component5() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    /**
     * 获取置信度百分比字符串
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getConfidencePercentage() {
        return null;
    }
    
    /**
     * 判断是否为高置信度结果
     */
    public final boolean isHighConfidence(float threshold) {
        return false;
    }
    
    /**
     * 获取前N个最高分数的类别
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<kotlin.Pair<java.lang.String, java.lang.Float>> getTopNClasses(int n) {
        return null;
    }
    
    /**
     * 获取分类质量等级
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.QualityLevel getQualityLevel() {
        return null;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 3)
    public static final class Creator implements android.os.Parcelable.Creator<com.fsl.app.domain.model.ClassificationResult> {
        
        public Creator() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.ClassificationResult createFromParcel(@org.jetbrains.annotations.NotNull
        android.os.Parcel in) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.ClassificationResult[] newArray(int size) {
            return null;
        }
    }
}