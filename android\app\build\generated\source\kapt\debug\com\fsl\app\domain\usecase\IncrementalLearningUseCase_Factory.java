// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.domain.usecase;

import com.fsl.app.domain.repository.IImageProcessingRepository;
import com.fsl.app.domain.repository.IInferenceRepository;
import com.fsl.app.domain.repository.IModelRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class IncrementalLearningUseCase_Factory implements Factory<IncrementalLearningUseCase> {
  private final Provider<IModelRepository> modelRepositoryProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  private final Provider<IImageProcessingRepository> imageProcessingRepositoryProvider;

  public IncrementalLearningUseCase_Factory(Provider<IModelRepository> modelRepositoryProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IImageProcessingRepository> imageProcessingRepositoryProvider) {
    this.modelRepositoryProvider = modelRepositoryProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
    this.imageProcessingRepositoryProvider = imageProcessingRepositoryProvider;
  }

  @Override
  public IncrementalLearningUseCase get() {
    return newInstance(modelRepositoryProvider.get(), inferenceRepositoryProvider.get(), imageProcessingRepositoryProvider.get());
  }

  public static IncrementalLearningUseCase_Factory create(
      Provider<IModelRepository> modelRepositoryProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IImageProcessingRepository> imageProcessingRepositoryProvider) {
    return new IncrementalLearningUseCase_Factory(modelRepositoryProvider, inferenceRepositoryProvider, imageProcessingRepositoryProvider);
  }

  public static IncrementalLearningUseCase newInstance(IModelRepository modelRepository,
      IInferenceRepository inferenceRepository,
      IImageProcessingRepository imageProcessingRepository) {
    return new IncrementalLearningUseCase(modelRepository, inferenceRepository, imageProcessingRepository);
  }
}
