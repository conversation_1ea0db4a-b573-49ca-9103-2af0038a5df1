package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.fsl.app.FSLApplication",
    rootPackage = "com.fsl.app",
    originatingRoot = "com.fsl.app.FSLApplication",
    originatingRootPackage = "com.fsl.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "FSLApplication",
    originatingRootSimpleNames = "FSLApplication"
)
public class _com_fsl_app_FSLApplication {
}
