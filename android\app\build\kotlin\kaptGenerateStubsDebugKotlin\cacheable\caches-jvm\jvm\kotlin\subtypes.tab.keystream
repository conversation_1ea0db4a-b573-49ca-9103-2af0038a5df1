+com.fsl.app.domain.usecase.ValidationResult+com.fsl.app.presentation.camera.CameraState#androidx.lifecycle.AndroidViewModelandroidx.lifecycle.ViewModelkotlin.Enumandroid.os.Parcelableandroid.app.Application#androidx.activity.ComponentActivity/com.fsl.app.domain.repository.GalleryRepository8com.fsl.app.domain.repository.IImageProcessingRepository.com.fsl.app.domain.repository.IModelRepository2com.fsl.app.domain.repository.IInferenceRepositoryandroidx.room.RoomDatabase                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   