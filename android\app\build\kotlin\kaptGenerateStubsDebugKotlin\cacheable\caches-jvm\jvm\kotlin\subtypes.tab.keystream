+com.fsl.app.domain.usecase.ValidationResult+com.fsl.app.presentation.camera.CameraStateandroidx.lifecycle.ViewModelandroid.os.Parcelableandroid.app.Application#androidx.activity.ComponentActivity8com.fsl.app.domain.repository.IImageProcessingRepository.com.fsl.app.domain.repository.IModelRepository2com.fsl.app.domain.repository.IInferenceRepositorykotlin.Enumandroidx.room.RoomDatabase/com.fsl.app.domain.repository.GalleryRepository#androidx.lifecycle.AndroidViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   