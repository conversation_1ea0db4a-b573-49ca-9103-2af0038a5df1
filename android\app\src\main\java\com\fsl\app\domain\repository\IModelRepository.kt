/**
 * 模型仓库接口
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.repository

import android.graphics.Bitmap
import com.fsl.app.domain.model.LearnedClass
import kotlinx.coroutines.flow.Flow

/**
 * 模型仓库接口
 */
interface IModelRepository {
    
    /**
     * 获取所有已学习的类别
     */
    fun getLearnedClasses(): Flow<List<LearnedClass>>
    
    /**
     * 添加新类别
     */
    suspend fun addClass(className: String, samples: List<Bitmap>): Result<Unit>
    
    /**
     * 添加样本到现有类别
     */
    suspend fun addSamplesToClass(className: String, samples: List<Bitmap>): Result<Unit>
    
    /**
     * 删除类别
     */
    suspend fun deleteClass(className: String): Result<Unit>
    
    /**
     * 检查类别是否存在
     */
    suspend fun classExists(className: String): Boolean
    
    /**
     * 获取类别数量
     */
    suspend fun getClassCount(): Int
    
    /**
     * 获取总样本数量
     */
    suspend fun getTotalSampleCount(): Int
    
    /**
     * 获取指定类别的样本数量
     */
    suspend fun getSampleCount(className: String): Int
}
