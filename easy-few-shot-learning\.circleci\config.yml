version: 2.1

orbs:

executors:
  base-python:
    working_directory: ~/project
    docker:
      - image: cimg/python:3.11.3

commands:
  get_code_and_cached_dependencies:
    steps:
      - run:
          name: Set python 3.11.3
          command: |
            pyenv global 3.11.3
            python -m pip install --upgrade pip setuptools wheel
      - checkout
      - restore_cache:
          key: docker-3.11.3-{{ checksum "setup.py" }}-{{ checksum "dev_requirements.txt" }}


jobs:
  install:
    parameters:
      python-version:
        type: string
    working_directory: ~/project
    docker:
      - image: cimg/python:<< parameters.python-version >>
    steps:
      - run:
          name: Set python << parameters.python-version >>
          command: |
            pyenv versions
            pyenv global << parameters.python-version >>
            python -m pip install --upgrade pip setuptools wheel
      - checkout
      # Download and cache dependencies
      - restore_cache:
          keys:
            - docker-<< parameters.python-version >>-{{ checksum "setup.py" }}-{{ checksum "dev_requirements.txt" }}
      - run:
          name: Install dependencies
          command: |
            python -m venv venv
            . venv/bin/activate
            pip install --upgrade pip
            pip install -r dev_requirements.txt
      - save_cache:
          paths:
            - ./venv
          key: docker-<< parameters.python-version >>-{{ checksum "setup.py" }}-{{ checksum "dev_requirements.txt" }}
  lint:
    executor: base-python
    steps:
      - get_code_and_cached_dependencies
      - run:
          name: run pylint
          command: |
            . venv/bin/activate
            pylint --version
            make lint
  black:
    executor: base-python
    steps:
      - get_code_and_cached_dependencies
      - run:
          name: run black
          command: |
            . venv/bin/activate
            make black-check
  isort:
    executor: base-python
    steps:
      - get_code_and_cached_dependencies
      - run:
          name: run isort
          command: |
            . venv/bin/activate
            make isort-check
  mypy:
    executor: base-python
    steps:
      - get_code_and_cached_dependencies
      - run:
          name: run mypy
          command: |
            . venv/bin/activate
            make mypy
  test:
    parameters:
      python-version:
        type: string
    working_directory: ~/project
    docker:
      - image: cimg/python:<< parameters.python-version >>
    steps:
      - run:
          name: Set python << parameters.python-version >>
          command: |
            pyenv global << parameters.python-version >>
            python -m pip install --upgrade pip setuptools wheel
      - checkout
      - restore_cache:
          key: docker-<< parameters.python-version >>-{{ checksum "setup.py" }}-{{ checksum "dev_requirements.txt" }}
      - run:
          name: run pytest
          command: |
            . venv/bin/activate
            make test

workflows:
  main:
    jobs:
      - install:
          matrix:
            parameters:
              python-version: ["3.7.10", "3.8.8", "3.9.11", "3.10.10", "3.11.3"]
      - lint:
          requires:
            - install-3.11.3
      - black:
          requires:
            - install-3.11.3
      - isort:
          requires:
            - install-3.11.3
      - mypy:
          requires:
            - install-3.11.3
      - test:
          matrix:
            parameters:
              python-version: ["3.7.10", "3.8.8", "3.9.11", "3.10.10", "3.11.3"]
          requires:
            - install-<< matrix.python-version >>
