{"buildFiles": ["F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\androidsdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\androidsdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"fsl_native::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "fsl_native", "output": "F:\\geek\\fsl\\android\\app\\build\\intermediates\\cxx\\Debug\\6w13315g\\obj\\arm64-v8a\\libfsl_native.so", "runtimeFiles": ["D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\29\\liblog.so", "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\29\\libandroid.so", "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\29\\libneuralnetworks.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}