// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.inference;

import android.content.Context;
import com.fsl.app.data.utils.AssetUtils;
import com.fsl.app.data.utils.ImageProcessor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class PyTorchInferenceEngine_Factory implements Factory<PyTorchInferenceEngine> {
  private final Provider<Context> contextProvider;

  private final Provider<ImageProcessor> imageProcessorProvider;

  private final Provider<AssetUtils> assetUtilsProvider;

  private final Provider<NativeInferenceEngine> nativeEngineProvider;

  public PyTorchInferenceEngine_Factory(Provider<Context> contextProvider,
      Provider<ImageProcessor> imageProcessorProvider, Provider<AssetUtils> assetUtilsProvider,
      Provider<NativeInferenceEngine> nativeEngineProvider) {
    this.contextProvider = contextProvider;
    this.imageProcessorProvider = imageProcessorProvider;
    this.assetUtilsProvider = assetUtilsProvider;
    this.nativeEngineProvider = nativeEngineProvider;
  }

  @Override
  public PyTorchInferenceEngine get() {
    return newInstance(contextProvider.get(), imageProcessorProvider.get(), assetUtilsProvider.get(), nativeEngineProvider.get());
  }

  public static PyTorchInferenceEngine_Factory create(Provider<Context> contextProvider,
      Provider<ImageProcessor> imageProcessorProvider, Provider<AssetUtils> assetUtilsProvider,
      Provider<NativeInferenceEngine> nativeEngineProvider) {
    return new PyTorchInferenceEngine_Factory(contextProvider, imageProcessorProvider, assetUtilsProvider, nativeEngineProvider);
  }

  public static PyTorchInferenceEngine newInstance(Context context, ImageProcessor imageProcessor,
      AssetUtils assetUtils, NativeInferenceEngine nativeEngine) {
    return new PyTorchInferenceEngine(context, imageProcessor, assetUtils, nativeEngine);
  }
}
