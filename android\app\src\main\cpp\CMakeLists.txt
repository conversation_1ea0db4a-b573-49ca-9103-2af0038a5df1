# CMakeLists.txt for FSL Android Native Library with NNAPI

cmake_minimum_required(VERSION 3.22.1)

# 设置项目名称
project("fsl_native")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找必需的包
find_library(log-lib log)
find_library(android-lib android)
find_library(neuralnetworks-lib neuralnetworks)

# 包含头文件目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/eigen
)

# 源文件
set(SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/fsl_inference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/prototypical_network.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/feature_extractor.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/image_processor.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/nnapi_engine.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/object_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/jni_interface.cpp
)

# 创建共享库
add_library(
    fsl_native
    SHARED
    ${SOURCES}
)

# 链接库
target_link_libraries(
    fsl_native
    ${log-lib}
    ${android-lib}
    ${neuralnetworks-lib}
    jnigraphics
)

# 编译选项
target_compile_options(fsl_native PRIVATE
    -O3
    -ffast-math
    -DEIGEN_NO_DEBUG
    -DEIGEN_DONT_PARALLELIZE
    -DUSE_NNAPI
)

# 预处理器定义
target_compile_definitions(fsl_native PRIVATE
    ANDROID
    USE_NNAPI
    __ANDROID_API__=${ANDROID_PLATFORM_LEVEL}
)
