/**
 * 模型管理用例
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.usecase

import com.fsl.app.domain.repository.IInferenceRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ModelManagementUseCase @Inject constructor(
    private val inferenceRepository: IInferenceRepository
) {
    
    /**
     * 获取模型信息
     */
    suspend fun getModelInfo(): Result<Map<String, Any>> {
        return try {
            val info = inferenceRepository.getModelInfo()
            Result.success(info)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 初始化模型
     */
    suspend fun initializeModel(): Result<Unit> {
        return inferenceRepository.initialize()
    }
    
    /**
     * 保存模型
     */
    suspend fun saveModel(): Result<Unit> {
        return inferenceRepository.saveModel()
    }
    
    /**
     * 加载模型
     */
    suspend fun loadModel(): Result<Unit> {
        return inferenceRepository.loadModel()
    }
    
    /**
     * 检查模型是否已初始化
     */
    fun isModelInitialized(): <PERSON><PERSON><PERSON> {
        return inferenceRepository.isInitialized()
    }
}
