// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.learning;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.internal.lifecycle.HiltViewModelMap.KeySet")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class LearningViewModel_HiltModules_KeyModule_ProvideFactory implements Factory<String> {
  @Override
  public String get() {
    return provide();
  }

  public static LearningViewModel_HiltModules_KeyModule_ProvideFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static String provide() {
    return Preconditions.checkNotNullFromProvides(LearningViewModel_HiltModules.KeyModule.provide());
  }

  private static final class InstanceHolder {
    private static final LearningViewModel_HiltModules_KeyModule_ProvideFactory INSTANCE = new LearningViewModel_HiltModules_KeyModule_ProvideFactory();
  }
}
