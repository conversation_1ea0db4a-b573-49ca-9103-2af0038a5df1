/**
 * 应用依赖注入模块
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.di

import android.content.Context
import androidx.room.Room
import com.fsl.app.data.database.AppDatabase
import com.fsl.app.data.database.LearnedClassDao
import com.fsl.app.data.database.TrainingSampleDao
import com.fsl.app.data.inference.NativeInferenceEngine
import com.fsl.app.data.inference.PyTorchInferenceEngine
import com.fsl.app.data.repository.ImageProcessingRepository
import com.fsl.app.data.repository.ModelRepository
import com.fsl.app.data.repository.GalleryRepositoryImpl
import com.fsl.app.data.utils.AssetUtils
import com.fsl.app.data.utils.FileManager
import com.fsl.app.data.utils.ImageProcessor
import com.fsl.app.domain.repository.IImageProcessingRepository
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IModelRepository
import com.fsl.app.domain.repository.GalleryRepository
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideImageProcessor(): ImageProcessor {
        return ImageProcessor()
    }

    @Provides
    @Singleton
    fun provideAssetUtils(@ApplicationContext context: Context): AssetUtils {
        return AssetUtils(context)
    }

    @Provides
    @Singleton
    fun provideFileManager(@ApplicationContext context: Context): FileManager {
        return FileManager(context)
    }

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "fsl_database"
        ).build()
    }

    @Provides
    @Singleton
    fun provideLearnedClassDao(database: AppDatabase): LearnedClassDao {
        return database.learnedClassDao()
    }

    @Provides
    @Singleton
    fun provideTrainingSampleDao(database: AppDatabase): TrainingSampleDao {
        return database.trainingSampleDao()
    }

    @Provides
    @Singleton
    fun provideNativeInferenceEngine(): NativeInferenceEngine? {
        return if (NativeInferenceEngine.isNativeLibraryAvailable()) {
            NativeInferenceEngine()
        } else {
            null // 返回null表示native引擎不可用
        }
    }
}

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    abstract fun bindInferenceRepository(
        pyTorchInferenceEngine: PyTorchInferenceEngine
    ): IInferenceRepository

    @Binds
    abstract fun bindImageProcessingRepository(
        imageProcessingRepository: ImageProcessingRepository
    ): IImageProcessingRepository

    @Binds
    abstract fun bindModelRepository(
        modelRepository: ModelRepository
    ): IModelRepository

    @Binds
    abstract fun bindGalleryRepository(
        galleryRepositoryImpl: GalleryRepositoryImpl
    ): GalleryRepository
}
