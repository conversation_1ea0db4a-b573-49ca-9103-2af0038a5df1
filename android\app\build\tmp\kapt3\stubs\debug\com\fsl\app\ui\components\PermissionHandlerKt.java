package com.fsl.app.ui.components;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a6\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0011\u0010\u0004\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u00062\u0011\u0010\u0007\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u0006H\u0007\u00a8\u0006\b"}, d2 = {"PermissionHandler", "", "permission", "", "onPermissionGranted", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "onPermissionDenied", "app_debug"})
public final class PermissionHandlerKt {
    
    /**
     * 权限处理器
     *
     * @param permission 需要的权限
     * @param onPermissionGranted 权限授予时的回调
     * @param onPermissionDenied 权限拒绝时的回调
     */
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {com.google.accompanist.permissions.ExperimentalPermissionsApi.class})
    public static final void PermissionHandler(@org.jetbrains.annotations.NotNull
    java.lang.String permission, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionGranted, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionDenied) {
    }
}