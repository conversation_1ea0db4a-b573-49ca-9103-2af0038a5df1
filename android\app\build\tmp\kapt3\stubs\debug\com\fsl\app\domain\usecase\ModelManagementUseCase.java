package com.fsl.app.domain.usecase;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J.\u0010\u0005\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00070\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\t\u0010\nJ\"\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\r\u0010\nJ\u0006\u0010\u000e\u001a\u00020\u000fJ\"\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\f0\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\nJ\"\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\f0\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0013\u0010\nR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u0014"}, d2 = {"Lcom/fsl/app/domain/usecase/ModelManagementUseCase;", "", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "(Lcom/fsl/app/domain/repository/IInferenceRepository;)V", "getModelInfo", "Lkotlin/Result;", "", "", "getModelInfo-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initializeModel", "", "initializeModel-IoAF18A", "isModelInitialized", "", "loadModel", "loadModel-IoAF18A", "saveModel", "saveModel-IoAF18A", "app_debug"})
@javax.inject.Singleton
public final class ModelManagementUseCase {
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    
    @javax.inject.Inject
    public ModelManagementUseCase(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository) {
        super();
    }
    
    /**
     * 检查模型是否已初始化
     */
    public final boolean isModelInitialized() {
        return false;
    }
}