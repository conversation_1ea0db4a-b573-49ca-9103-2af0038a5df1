# Simple Android Project Check

Write-Host "Checking Android project..." -ForegroundColor Green

$errors = 0

# Check required files
$files = @(
    "app/build.gradle",
    "app/src/main/AndroidManifest.xml", 
    "app/src/main/java/com/fsl/app/MainActivity.kt",
    "app/src/main/java/com/fsl/app/FSLApplication.kt"
)

Write-Host "`nChecking files..." -ForegroundColor Yellow
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  OK: $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
        $errors++
    }
}

# Check Kotlin files
Write-Host "`nChecking Kotlin files..." -ForegroundColor Yellow
$kotlinFiles = Get-ChildItem -Path "app/src/main/java" -Filter "*.kt" -Recurse -ErrorAction SilentlyContinue

if ($kotlinFiles) {
    Write-Host "  Found $($kotlinFiles.Count) Kotlin files" -ForegroundColor Green
    foreach ($file in $kotlinFiles) {
        $name = $file.Name
        Write-Host "    - $name" -ForegroundColor White
    }
} else {
    Write-Host "  No Kotlin files found" -ForegroundColor Red
    $errors++
}

# Check resources
Write-Host "`nChecking resources..." -ForegroundColor Yellow
$resFiles = @(
    "app/src/main/res/values/strings.xml",
    "app/src/main/res/values/colors.xml"
)

foreach ($file in $resFiles) {
    if (Test-Path $file) {
        Write-Host "  OK: $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
        $errors++
    }
}

# Summary
Write-Host "`nSummary:" -ForegroundColor Cyan
if ($errors -eq 0) {
    Write-Host "  All checks passed! Project looks good." -ForegroundColor Green
    Write-Host "  Ready for compilation." -ForegroundColor Green
} else {
    Write-Host "  Found $errors issues." -ForegroundColor Red
    Write-Host "  Please fix issues before compiling." -ForegroundColor Red
}

Write-Host "`nProject Features:" -ForegroundColor Cyan
Write-Host "  - Jetpack Compose UI" -ForegroundColor White
Write-Host "  - MVVM Architecture" -ForegroundColor White  
Write-Host "  - Hilt Dependency Injection" -ForegroundColor White
Write-Host "  - Few-Shot Learning Engine" -ForegroundColor White
Write-Host "  - Real-time Camera Classification" -ForegroundColor White
Write-Host "  - Incremental Learning" -ForegroundColor White

exit $errors
