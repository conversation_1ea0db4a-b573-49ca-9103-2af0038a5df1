// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.camera;

import android.app.Application;
import com.fsl.app.data.inference.RealTimeInferenceEngine;
import com.fsl.app.data.repository.GalleryRepositoryImpl;
import com.fsl.app.domain.usecase.ClassificationUseCase;
import com.fsl.app.domain.usecase.ImageProcessingUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class CameraViewModel_Factory implements Factory<CameraViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<ClassificationUseCase> classificationUseCaseProvider;

  private final Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider;

  private final Provider<GalleryRepositoryImpl> galleryRepositoryProvider;

  private final Provider<RealTimeInferenceEngine> realTimeInferenceEngineProvider;

  public CameraViewModel_Factory(Provider<Application> applicationProvider,
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider,
      Provider<GalleryRepositoryImpl> galleryRepositoryProvider,
      Provider<RealTimeInferenceEngine> realTimeInferenceEngineProvider) {
    this.applicationProvider = applicationProvider;
    this.classificationUseCaseProvider = classificationUseCaseProvider;
    this.imageProcessingUseCaseProvider = imageProcessingUseCaseProvider;
    this.galleryRepositoryProvider = galleryRepositoryProvider;
    this.realTimeInferenceEngineProvider = realTimeInferenceEngineProvider;
  }

  @Override
  public CameraViewModel get() {
    return newInstance(applicationProvider.get(), classificationUseCaseProvider.get(), imageProcessingUseCaseProvider.get(), galleryRepositoryProvider.get(), realTimeInferenceEngineProvider.get());
  }

  public static CameraViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider,
      Provider<GalleryRepositoryImpl> galleryRepositoryProvider,
      Provider<RealTimeInferenceEngine> realTimeInferenceEngineProvider) {
    return new CameraViewModel_Factory(applicationProvider, classificationUseCaseProvider, imageProcessingUseCaseProvider, galleryRepositoryProvider, realTimeInferenceEngineProvider);
  }

  public static CameraViewModel newInstance(Application application,
      ClassificationUseCase classificationUseCase, ImageProcessingUseCase imageProcessingUseCase,
      GalleryRepositoryImpl galleryRepository, RealTimeInferenceEngine realTimeInferenceEngine) {
    return new CameraViewModel(application, classificationUseCase, imageProcessingUseCase, galleryRepository, realTimeInferenceEngine);
  }
}
