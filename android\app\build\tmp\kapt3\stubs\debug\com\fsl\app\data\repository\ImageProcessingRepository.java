package com.fsl.app.data.repository;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0014\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\u0007H\u0016J\b\u0010\t\u001a\u00020\u0007H\u0002J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\b\u001a\u00020\u0007H\u0016J\u0010\u0010\f\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0007H\u0016J\u0010\u0010\r\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u000fH\u0016J \u0010\u0010\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0012H\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/fsl/app/data/repository/ImageProcessingRepository;", "Lcom/fsl/app/domain/repository/IImageProcessingRepository;", "imageProcessor", "Lcom/fsl/app/data/utils/ImageProcessor;", "(Lcom/fsl/app/data/utils/ImageProcessor;)V", "augmentImage", "", "Landroid/graphics/Bitmap;", "bitmap", "createTestBitmap", "normalizeImage", "", "preprocessImage", "preprocessImageProxy", "imageProxy", "Landroidx/camera/core/ImageProxy;", "resizeImage", "width", "", "height", "app_debug"})
@javax.inject.Singleton
public final class ImageProcessingRepository implements com.fsl.app.domain.repository.IImageProcessingRepository {
    private final com.fsl.app.data.utils.ImageProcessor imageProcessor = null;
    
    @javax.inject.Inject
    public ImageProcessingRepository(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.utils.ImageProcessor imageProcessor) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public android.graphics.Bitmap preprocessImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public android.graphics.Bitmap preprocessImageProxy(@org.jetbrains.annotations.NotNull
    androidx.camera.core.ImageProxy imageProxy) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.util.List<android.graphics.Bitmap> augmentImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public android.graphics.Bitmap resizeImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, int width, int height) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public float[] normalizeImage(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 创建测试用的Bitmap
     */
    private final android.graphics.Bitmap createTestBitmap() {
        return null;
    }
}