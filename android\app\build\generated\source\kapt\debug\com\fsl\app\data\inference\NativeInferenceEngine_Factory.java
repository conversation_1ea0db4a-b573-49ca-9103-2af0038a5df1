// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.inference;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class NativeInferenceEngine_Factory implements Factory<NativeInferenceEngine> {
  @Override
  public NativeInferenceEngine get() {
    return newInstance();
  }

  public static NativeInferenceEngine_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static NativeInferenceEngine newInstance() {
    return new NativeInferenceEngine();
  }

  private static final class InstanceHolder {
    private static final NativeInferenceEngine_Factory INSTANCE = new NativeInferenceEngine_Factory();
  }
}
