"""
少样本学习训练与推理栈
基于easy-few-shot-learning的本地化实现

作者: AI Assistant
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

from .core import *
from .datasets import *
from .methods import *
from .training import *
from .inference import *
from .utils import *

__all__ = [
    # 核心模块
    "FewShotClassifier",
    "TaskSampler",
    
    # 数据集
    "LocalDataset",
    "FeaturesDataset",
    
    # 方法
    "PrototypicalNetworks",
    "SimpleShot",
    "FEAT",
    
    # 训练
    "EpisodicTrainer",
    "ClassicalTrainer",
    
    # 推理
    "InferenceEngine",
    "BatchInference",
    
    # 工具
    "ConfigManager",
    "ModelManager",
    "evaluate",
]
