// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.learning;

import android.app.Application;
import com.fsl.app.data.repository.GalleryRepositoryImpl;
import com.fsl.app.domain.repository.IInferenceRepository;
import com.fsl.app.domain.repository.IModelRepository;
import com.fsl.app.domain.usecase.ClassificationUseCase;
import com.fsl.app.domain.usecase.ImageProcessingUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class SampleCollectionViewModel_Factory implements Factory<SampleCollectionViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  private final Provider<IModelRepository> modelRepositoryProvider;

  private final Provider<ClassificationUseCase> classificationUseCaseProvider;

  private final Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider;

  private final Provider<GalleryRepositoryImpl> galleryRepositoryProvider;

  public SampleCollectionViewModel_Factory(Provider<Application> applicationProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IModelRepository> modelRepositoryProvider,
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider,
      Provider<GalleryRepositoryImpl> galleryRepositoryProvider) {
    this.applicationProvider = applicationProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
    this.modelRepositoryProvider = modelRepositoryProvider;
    this.classificationUseCaseProvider = classificationUseCaseProvider;
    this.imageProcessingUseCaseProvider = imageProcessingUseCaseProvider;
    this.galleryRepositoryProvider = galleryRepositoryProvider;
  }

  @Override
  public SampleCollectionViewModel get() {
    return newInstance(applicationProvider.get(), inferenceRepositoryProvider.get(), modelRepositoryProvider.get(), classificationUseCaseProvider.get(), imageProcessingUseCaseProvider.get(), galleryRepositoryProvider.get());
  }

  public static SampleCollectionViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<IModelRepository> modelRepositoryProvider,
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<ImageProcessingUseCase> imageProcessingUseCaseProvider,
      Provider<GalleryRepositoryImpl> galleryRepositoryProvider) {
    return new SampleCollectionViewModel_Factory(applicationProvider, inferenceRepositoryProvider, modelRepositoryProvider, classificationUseCaseProvider, imageProcessingUseCaseProvider, galleryRepositoryProvider);
  }

  public static SampleCollectionViewModel newInstance(Application application,
      IInferenceRepository inferenceRepository, IModelRepository modelRepository,
      ClassificationUseCase classificationUseCase, ImageProcessingUseCase imageProcessingUseCase,
      GalleryRepositoryImpl galleryRepository) {
    return new SampleCollectionViewModel(application, inferenceRepository, modelRepository, classificationUseCase, imageProcessingUseCase, galleryRepository);
  }
}
