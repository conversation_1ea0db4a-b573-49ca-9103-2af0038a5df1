// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation.learning;

import android.app.Application;
import com.fsl.app.data.repository.GalleryRepositoryImpl;
import com.fsl.app.domain.repository.IInferenceRepository;
import com.fsl.app.domain.usecase.ClassificationUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class SampleCollectionViewModel_Factory implements Factory<SampleCollectionViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  private final Provider<ClassificationUseCase> classificationUseCaseProvider;

  private final Provider<GalleryRepositoryImpl> galleryRepositoryProvider;

  public SampleCollectionViewModel_Factory(Provider<Application> applicationProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<GalleryRepositoryImpl> galleryRepositoryProvider) {
    this.applicationProvider = applicationProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
    this.classificationUseCaseProvider = classificationUseCaseProvider;
    this.galleryRepositoryProvider = galleryRepositoryProvider;
  }

  @Override
  public SampleCollectionViewModel get() {
    return newInstance(applicationProvider.get(), inferenceRepositoryProvider.get(), classificationUseCaseProvider.get(), galleryRepositoryProvider.get());
  }

  public static SampleCollectionViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<ClassificationUseCase> classificationUseCaseProvider,
      Provider<GalleryRepositoryImpl> galleryRepositoryProvider) {
    return new SampleCollectionViewModel_Factory(applicationProvider, inferenceRepositoryProvider, classificationUseCaseProvider, galleryRepositoryProvider);
  }

  public static SampleCollectionViewModel newInstance(Application application,
      IInferenceRepository inferenceRepository, ClassificationUseCase classificationUseCase,
      GalleryRepositoryImpl galleryRepository) {
    return new SampleCollectionViewModel(application, inferenceRepository, classificationUseCase, galleryRepository);
  }
}
