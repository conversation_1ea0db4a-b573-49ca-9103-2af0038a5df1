package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 图库图像数据类
 *
 * @param file 图像文件
 * @param classificationResult 分类结果
 * @param timestamp 创建时间戳
 * @param isLearned 是否为学习样本
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b\u0017\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0007H\u00c6\u0003J\t\u0010%\u001a\u00020\tH\u00c6\u0003J3\u0010&\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u00c6\u0001J\u0013\u0010\'\u001a\u00020\t2\b\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010)\u001a\u00020*H\u00d6\u0001J\t\u0010+\u001a\u00020\fH\u00d6\u0001R\u0011\u0010\u000b\u001a\u00020\f8F\u00a2\u0006\u0006\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0011\u001a\u00020\u00128F\u00a2\u0006\u0006\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0015\u001a\u00020\f8F\u00a2\u0006\u0006\u001a\u0004\b\u0016\u0010\u000eR\u0011\u0010\u0017\u001a\u00020\t8F\u00a2\u0006\u0006\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u001c\u001a\u00020\f8F\u00a2\u0006\u0006\u001a\u0004\b\u001d\u0010\u000eR\u0011\u0010\u001e\u001a\u00020\u00078F\u00a2\u0006\u0006\u001a\u0004\b\u001f\u0010 R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010 \u00a8\u0006,"}, d2 = {"Lcom/fsl/app/domain/model/GalleryImage;", "", "file", "Ljava/io/File;", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "timestamp", "", "isLearned", "", "(Ljava/io/File;Lcom/fsl/app/domain/model/ClassificationResult;JZ)V", "classLabel", "", "getClassLabel", "()Ljava/lang/String;", "getClassificationResult", "()Lcom/fsl/app/domain/model/ClassificationResult;", "confidence", "", "getConfidence", "()F", "confidencePercent", "getConfidencePercent", "exists", "getExists", "()Z", "getFile", "()Ljava/io/File;", "fileName", "getFileName", "fileSizeKB", "getFileSizeKB", "()J", "getTimestamp", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class GalleryImage {
    @org.jetbrains.annotations.NotNull
    private final java.io.File file = null;
    @org.jetbrains.annotations.Nullable
    private final com.fsl.app.domain.model.ClassificationResult classificationResult = null;
    private final long timestamp = 0L;
    private final boolean isLearned = false;
    
    /**
     * 图库图像数据类
     *
     * @param file 图像文件
     * @param classificationResult 分类结果
     * @param timestamp 创建时间戳
     * @param isLearned 是否为学习样本
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.GalleryImage copy(@org.jetbrains.annotations.NotNull
    java.io.File file, @org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult classificationResult, long timestamp, boolean isLearned) {
        return null;
    }
    
    /**
     * 图库图像数据类
     *
     * @param file 图像文件
     * @param classificationResult 分类结果
     * @param timestamp 创建时间戳
     * @param isLearned 是否为学习样本
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 图库图像数据类
     *
     * @param file 图像文件
     * @param classificationResult 分类结果
     * @param timestamp 创建时间戳
     * @param isLearned 是否为学习样本
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 图库图像数据类
     *
     * @param file 图像文件
     * @param classificationResult 分类结果
     * @param timestamp 创建时间戳
     * @param isLearned 是否为学习样本
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public GalleryImage(@org.jetbrains.annotations.NotNull
    java.io.File file, @org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult classificationResult, long timestamp, boolean isLearned) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.io.File component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.io.File getFile() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.fsl.app.domain.model.ClassificationResult component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.fsl.app.domain.model.ClassificationResult getClassificationResult() {
        return null;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isLearned() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFileName() {
        return null;
    }
    
    public final long getFileSizeKB() {
        return 0L;
    }
    
    public final boolean getExists() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getClassLabel() {
        return null;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getConfidencePercent() {
        return null;
    }
}