// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class GalleryRepositoryImpl_Factory implements Factory<GalleryRepositoryImpl> {
  private final Provider<Context> contextProvider;

  public GalleryRepositoryImpl_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GalleryRepositoryImpl get() {
    return newInstance(contextProvider.get());
  }

  public static GalleryRepositoryImpl_Factory create(Provider<Context> contextProvider) {
    return new GalleryRepositoryImpl_Factory(contextProvider);
  }

  public static GalleryRepositoryImpl newInstance(Context context) {
    return new GalleryRepositoryImpl(context);
  }
}
