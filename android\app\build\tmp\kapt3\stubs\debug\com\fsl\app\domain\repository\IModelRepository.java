package com.fsl.app.domain.repository;

import java.lang.System;

/**
 * 模型仓库接口
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J8\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ8\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\r\u0010\u000bJ\u0019\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0010J*\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0010J\u0011\u0010\u0013\u001a\u00020\u0014H\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0015J\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\b0\u0017H&J\u0019\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0010J\u0011\u0010\u001a\u001a\u00020\u0014H\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0015\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u001b"}, d2 = {"Lcom/fsl/app/domain/repository/IModelRepository;", "", "addClass", "Lkotlin/Result;", "", "className", "", "samples", "", "Landroid/graphics/Bitmap;", "addClass-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addSamplesToClass", "addSamplesToClass-0E7RQCE", "classExists", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteClass", "deleteClass-gIAlu-s", "getClassCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLearnedClasses", "Lkotlinx/coroutines/flow/Flow;", "Lcom/fsl/app/domain/model/LearnedClass;", "getSampleCount", "getTotalSampleCount", "app_debug"})
public abstract interface IModelRepository {
    
    /**
     * 获取所有已学习的类别
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.fsl.app.domain.model.LearnedClass>> getLearnedClasses();
    
    /**
     * 检查类别是否存在
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object classExists(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> continuation);
    
    /**
     * 获取类别数量
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getClassCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation);
    
    /**
     * 获取总样本数量
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalSampleCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation);
    
    /**
     * 获取指定类别的样本数量
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getSampleCount(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation);
}