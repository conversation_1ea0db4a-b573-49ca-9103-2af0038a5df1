# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: fsl_native
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target fsl_native


#############################################
# Order-only phony target for fsl_native

build cmake_object_order_depends_target_fsl_native: phony || CMakeFiles/fsl_native.dir

build CMakeFiles/fsl_native.dir/fsl_inference.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/fsl_inference.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\fsl_inference.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb

build CMakeFiles/fsl_native.dir/prototypical_network.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/prototypical_network.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\prototypical_network.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb

build CMakeFiles/fsl_native.dir/feature_extractor.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/feature_extractor.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\feature_extractor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb

build CMakeFiles/fsl_native.dir/image_processor.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/image_processor.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\image_processor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb

build CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\nnapi_engine.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb

build CMakeFiles/fsl_native.dir/object_tracker.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/object_tracker.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\object_tracker.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb

build CMakeFiles/fsl_native.dir/jni_interface.cpp.o: CXX_COMPILER__fsl_native_Debug F$:/geek/fsl/android/app/src/main/cpp/jni_interface.cpp || cmake_object_order_depends_target_fsl_native
  DEFINES = -DANDROID -DUSE_NNAPI -D__ANDROID_API__=29 -Dfsl_native_EXPORTS
  DEP_FILE = CMakeFiles\fsl_native.dir\jni_interface.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17
  INCLUDES = -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  OBJECT_FILE_DIR = CMakeFiles\fsl_native.dir
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target fsl_native


#############################################
# Link the shared library F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.so

build F$:/geek/fsl/android/app/build/intermediates/cxx/Debug/6w13315g/obj/armeabi-v7a/libfsl_native.so: CXX_SHARED_LIBRARY_LINKER__fsl_native_Debug CMakeFiles/fsl_native.dir/fsl_inference.cpp.o CMakeFiles/fsl_native.dir/prototypical_network.cpp.o CMakeFiles/fsl_native.dir/feature_extractor.cpp.o CMakeFiles/fsl_native.dir/image_processor.cpp.o CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o CMakeFiles/fsl_native.dir/object_tracker.cpp.o CMakeFiles/fsl_native.dir/jni_interface.cpp.o | D$:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/29/liblog.so D$:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/29/libandroid.so D$:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/29/libneuralnetworks.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/29/liblog.so  D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/29/libandroid.so  D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/arm-linux-androideabi/29/libneuralnetworks.so  -ljnigraphics  -latomic -lm
  OBJECT_DIR = CMakeFiles\fsl_native.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libfsl_native.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\fsl_native.dir\
  TARGET_FILE = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.so
  TARGET_PDB = F:\geek\fsl\android\app\build\intermediates\cxx\Debug\6w13315g\obj\armeabi-v7a\libfsl_native.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\geek\fsl\android\app\.cxx\Debug\6w13315g\armeabi-v7a && D:\androidsdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\geek\fsl\android\app\.cxx\Debug\6w13315g\armeabi-v7a && D:\androidsdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SF:\geek\fsl\android\app\src\main\cpp -BF:\geek\fsl\android\app\.cxx\Debug\6w13315g\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build fsl_native: phony F$:/geek/fsl/android/app/build/intermediates/cxx/Debug/6w13315g/obj/armeabi-v7a/libfsl_native.so

build libfsl_native.so: phony F$:/geek/fsl/android/app/build/intermediates/cxx/Debug/6w13315g/obj/armeabi-v7a/libfsl_native.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/geek/fsl/android/app/.cxx/Debug/6w13315g/armeabi-v7a

build all: phony F$:/geek/fsl/android/app/build/intermediates/cxx/Debug/6w13315g/obj/armeabi-v7a/libfsl_native.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/abis.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/android-legacy.toolchain.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/android.toolchain.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/flags.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Clang.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Determine.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Initialize.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/platforms.cmake F$:/geek/fsl/android/app/src/main/cpp/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/androidsdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/abis.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/android-legacy.toolchain.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/android.toolchain.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/flags.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Clang.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Determine.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android-Initialize.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Android.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/androidsdk/ndk/27.0.11718014/build/cmake/platforms.cmake F$:/geek/fsl/android/app/src/main/cpp/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
