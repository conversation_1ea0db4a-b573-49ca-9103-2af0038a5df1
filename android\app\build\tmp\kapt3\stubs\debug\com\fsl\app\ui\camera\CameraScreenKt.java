package com.fsl.app.ui.camera;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000J\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a>\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\t\u001a\u00020\nH\u0003\u001a\u0086\u0001\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\u0010\f\u001a\u0004\u0018\u00010\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0011\u001a\u00020\u00052\u0014\u0010\u0012\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0014\u0012\u0004\u0012\u00020\u00010\u00132\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\"\u0010\u0017\u001a\u00020\u00012\u000e\b\u0002\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0007\u001a\b\u0010\u001a\u001a\u00020\u0001H\u0003\u001a6\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\t\u001a\u00020\nH\u0003\u00a8\u0006\u001c"}, d2 = {"BottomControlBar", "", "cameraState", "Lcom/fsl/app/presentation/camera/CameraState;", "isRealTimeMode", "", "onCaptureClick", "Lkotlin/Function0;", "onSwitchCamera", "modifier", "Landroidx/compose/ui/Modifier;", "CameraContent", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "trackingResults", "", "Lcom/fsl/app/domain/model/TrackingResult;", "isFrontCamera", "onImageCaptured", "Lkotlin/Function1;", "Landroidx/camera/core/ImageProxy;", "onToggleRealTimeMode", "onNavigateToLearning", "CameraScreen", "viewModel", "Lcom/fsl/app/presentation/camera/CameraViewModel;", "PermissionDeniedContent", "TopControlBar", "app_debug"})
public final class CameraScreenKt {
    
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    public static final void CameraScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToLearning, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.camera.CameraViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void CameraContent(com.fsl.app.presentation.camera.CameraState cameraState, com.fsl.app.domain.model.ClassificationResult classificationResult, java.util.List<com.fsl.app.domain.model.TrackingResult> trackingResults, boolean isRealTimeMode, boolean isFrontCamera, kotlin.jvm.functions.Function1<? super androidx.camera.core.ImageProxy, kotlin.Unit> onImageCaptured, kotlin.jvm.functions.Function0<kotlin.Unit> onCaptureClick, kotlin.jvm.functions.Function0<kotlin.Unit> onSwitchCamera, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleRealTimeMode, kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToLearning) {
    }
    
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    private static final void TopControlBar(boolean isRealTimeMode, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleRealTimeMode, kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToLearning, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void BottomControlBar(com.fsl.app.presentation.camera.CameraState cameraState, boolean isRealTimeMode, kotlin.jvm.functions.Function0<kotlin.Unit> onCaptureClick, kotlin.jvm.functions.Function0<kotlin.Unit> onSwitchCamera, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void PermissionDeniedContent() {
    }
}