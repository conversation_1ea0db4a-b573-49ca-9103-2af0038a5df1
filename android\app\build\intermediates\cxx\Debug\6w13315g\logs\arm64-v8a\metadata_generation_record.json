[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1553352535}, {"level_": 0, "message_": "rebuilding JSON F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a\\android_gradle_build.json due to:", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 487244750}, {"level_": 0, "message_": "- a dependent build file changed", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1201956759}, {"level_": 0, "message_": "  - F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -332917094}, {"level_": 0, "message_": "keeping json folder 'F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a' but regenerating project", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -23742366}, {"level_": 0, "message_": "executing cmake @echo off\n\"D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\geek\\\\fsl\\\\android\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\androidsdk\\\\ndk\\\\27.0.11718014\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\geek\\\\fsl\\\\android\\\\app\\\\.cxx\\\\Debug\\\\6w13315g\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_PLATFORM=android-29\" ^\n  \"-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\"\n", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1181829135}, {"level_": 0, "message_": "@echo off\n\"D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\geek\\\\fsl\\\\android\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\androidsdk\\\\ndk\\\\27.0.11718014\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\androidsdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17 -fexceptions -frtti -DUSE_NNAPI\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\geek\\\\fsl\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6w13315g\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\geek\\\\fsl\\\\android\\\\app\\\\.cxx\\\\Debug\\\\6w13315g\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_PLATFORM=android-29\" ^\n  \"-DANDROID_NDK=D:/androidsdk/ndk/27.0.11718014\"\n", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1739725714}, {"level_": 0, "message_": "Exiting generation of F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a\\compile_commands.json.bin normally", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -135943468}, {"level_": 0, "message_": "done executing cmake", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1957135929}, {"level_": 0, "message_": "write command file F:\\geek\\fsl\\android\\app\\.cxx\\Debug\\6w13315g\\arm64-v8a\\metadata_generation_command.txt", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1056540610}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\geek\\fsl\\android\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -399758283}]