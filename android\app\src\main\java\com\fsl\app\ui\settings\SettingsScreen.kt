/**
 * 设置界面
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.ModelTraining
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.R
import com.fsl.app.presentation.settings.SettingsViewModel

/**
 * 设置界面
 */
@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val modelInfo by viewModel.modelInfo.collectAsState()
    var showModelInfoDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = stringResource(R.string.settings),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 模型设置
        SettingsSection(title = "模型设置") {
            SettingsItem(
                icon = Icons.Default.ModelTraining,
                title = "模型信息",
                subtitle = "查看当前模型状态",
                onClick = { showModelInfoDialog = true }
            )

            SettingsItem(
                icon = Icons.Default.Storage,
                title = "导出模型",
                subtitle = "导出训练好的模型",
                onClick = { viewModel.exportModel() }
            )

            SettingsItem(
                icon = Icons.Default.Storage,
                title = "导入模型",
                subtitle = "从文件导入模型",
                onClick = { viewModel.importModel() }
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 应用信息
        SettingsSection(title = "应用信息") {
            SettingsItem(
                icon = Icons.Default.Info,
                title = "版本信息",
                subtitle = "FSL v1.0.0",
                onClick = { }
            )

            SettingsItem(
                icon = Icons.Default.Info,
                title = "关于",
                subtitle = "少样本学习演示应用",
                onClick = { }
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 模型统计
        if (modelInfo.isNotEmpty()) {
            SettingsSection(title = "模型统计") {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        ModelStatItem(
                            label = "已初始化",
                            value = if (modelInfo["isInitialized"] == true) "是" else "否"
                        )

                        ModelStatItem(
                            label = "类别数量",
                            value = "${modelInfo["classCount"] ?: 0}"
                        )

                        ModelStatItem(
                            label = "原型数量",
                            value = "${modelInfo["prototypeCount"] ?: 0}"
                        )
                    }
                }
            }
        }
    }

    // 模型信息对话框
    if (showModelInfoDialog) {
        ModelInfoDialog(
            modelInfo = modelInfo,
            onDismiss = { showModelInfoDialog = false }
        )
    }
}

/**
 * 设置分组
 */
@Composable
private fun SettingsSection(
    title: String,
    content: @Composable () -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(8.dp))

        content()
    }
}

/**
 * 设置项
 */
@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
private fun SettingsItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 模型统计项
 */
@Composable
private fun ModelStatItem(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * 模型信息对话框
 */
@Composable
private fun ModelInfoDialog(
    modelInfo: Map<String, Any>,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(stringResource(R.string.model_info))
        },
        text = {
            Column {
                modelInfo.forEach { (key, value) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 2.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = key,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = value.toString(),
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}
