package com.fsl.app.data.database;

import java.lang.System;

@androidx.room.Dao
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0007\bg\u0018\u00002\u00020\u0001J\u0019\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u0019\u0010\u000b\u001a\u00020\f2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\b\u001a\u00020\tH\'J\u0011\u0010\u0010\u001a\u00020\fH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0011J\u0019\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u001f\u0010\u0013\u001a\u00020\u00032\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0015\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u0016"}, d2 = {"Lcom/fsl/app/data/database/TrainingSampleDao;", "", "deleteSample", "", "sample", "Lcom/fsl/app/data/database/TrainingSampleEntity;", "(Lcom/fsl/app/data/database/TrainingSampleEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSamplesByClassId", "classId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSampleCountByClassId", "", "getSamplesByClassId", "Lkotlinx/coroutines/flow/Flow;", "", "getTotalSampleCount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertSample", "insertSamples", "samples", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface TrainingSampleDao {
    
    /**
     * 获取指定类别的所有样本
     */
    @org.jetbrains.annotations.NotNull
    @androidx.room.Query(value = "SELECT * FROM training_samples WHERE classId = :classId ORDER BY createdAt DESC")
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.fsl.app.data.database.TrainingSampleEntity>> getSamplesByClassId(@org.jetbrains.annotations.NotNull
    java.lang.String classId);
    
    /**
     * 插入训练样本
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Insert(onConflict = androidx.room.OnConflictStrategy.REPLACE)
    public abstract java.lang.Object insertSample(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.TrainingSampleEntity sample, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 批量插入训练样本
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Insert(onConflict = androidx.room.OnConflictStrategy.REPLACE)
    public abstract java.lang.Object insertSamples(@org.jetbrains.annotations.NotNull
    java.util.List<com.fsl.app.data.database.TrainingSampleEntity> samples, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 删除训练样本
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Delete
    public abstract java.lang.Object deleteSample(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.TrainingSampleEntity sample, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 删除指定类别的所有样本
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "DELETE FROM training_samples WHERE classId = :classId")
    public abstract java.lang.Object deleteSamplesByClassId(@org.jetbrains.annotations.NotNull
    java.lang.String classId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation);
    
    /**
     * 获取指定类别的样本数量
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "SELECT COUNT(*) FROM training_samples WHERE classId = :classId")
    public abstract java.lang.Object getSampleCountByClassId(@org.jetbrains.annotations.NotNull
    java.lang.String classId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation);
    
    /**
     * 获取所有样本数量
     */
    @org.jetbrains.annotations.Nullable
    @androidx.room.Query(value = "SELECT COUNT(*) FROM training_samples")
    public abstract java.lang.Object getTotalSampleCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> continuation);
}