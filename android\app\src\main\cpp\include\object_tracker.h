/**
 * 对象跟踪器头文件
 * 
 * 实现多对象跟踪算法
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef OBJECT_TRACKER_H
#define OBJECT_TRACKER_H

#include <vector>
#include <string>
#include <memory>
#include <map>

namespace fsl {

/**
 * 边界框结构
 */
struct BoundingBox {
    float x, y, width, height; // 归一化坐标 (0-1)
    float confidence;
    
    BoundingBox() : x(0), y(0), width(0), height(0), confidence(0) {}
    BoundingBox(float x_, float y_, float w_, float h_, float conf_)
        : x(x_), y(y_), width(w_), height(h_), confidence(conf_) {}
    
    // 计算IoU
    float iou(const BoundingBox& other) const;
    
    // 计算中心点
    std::pair<float, float> center() const;
    
    // 计算面积
    float area() const;
};

/**
 * 检测结果结构
 */
struct Detection {
    BoundingBox bbox;
    std::string className;
    float confidence;
    long timestamp;
    
    Detection() : confidence(0), timestamp(0) {}
    Detection(const BoundingBox& box, const std::string& cls, float conf, long ts)
        : bbox(box), className(cls), confidence(conf), timestamp(ts) {}
};

/**
 * 跟踪目标结构
 */
struct TrackedObject {
    int trackId;
    BoundingBox bbox;
    std::string className;
    float confidence;
    int age;           // 跟踪帧数
    int hitStreak;     // 连续检测到的帧数
    int timeSinceUpdate; // 自上次更新以来的帧数
    std::vector<BoundingBox> history; // 历史位置
    
    TrackedObject() : trackId(-1), confidence(0), age(0), hitStreak(0), timeSinceUpdate(0) {}
    
    // 预测下一帧位置
    BoundingBox predict() const;
    
    // 更新跟踪状态
    void update(const Detection& detection);
    
    // 获取速度
    std::pair<float, float> getVelocity() const;
};

/**
 * 卡尔曼滤波器
 */
class KalmanFilter {
public:
    KalmanFilter();
    ~KalmanFilter();
    
    /**
     * 初始化滤波器
     * @param initialState 初始状态 [x, y, vx, vy]
     */
    void initialize(const std::vector<float>& initialState);
    
    /**
     * 预测下一状态
     */
    std::vector<float> predict();
    
    /**
     * 更新状态
     * @param measurement 测量值 [x, y]
     */
    void update(const std::vector<float>& measurement);
    
    /**
     * 获取当前状态
     */
    std::vector<float> getState() const;

private:
    std::vector<std::vector<float>> m_state;      // 状态向量 [x, y, vx, vy]
    std::vector<std::vector<float>> m_covariance; // 协方差矩阵
    std::vector<std::vector<float>> m_transitionMatrix; // 状态转移矩阵
    std::vector<std::vector<float>> m_observationMatrix; // 观测矩阵
    std::vector<std::vector<float>> m_processNoise;     // 过程噪声
    std::vector<std::vector<float>> m_measurementNoise; // 测量噪声
    
    bool m_initialized;
    
    // 矩阵运算辅助函数
    std::vector<std::vector<float>> matrixMultiply(
        const std::vector<std::vector<float>>& a,
        const std::vector<std::vector<float>>& b) const;
    
    std::vector<std::vector<float>> matrixAdd(
        const std::vector<std::vector<float>>& a,
        const std::vector<std::vector<float>>& b) const;
    
    std::vector<std::vector<float>> matrixTranspose(
        const std::vector<std::vector<float>>& matrix) const;
    
    std::vector<std::vector<float>> matrixInverse(
        const std::vector<std::vector<float>>& matrix) const;
};

/**
 * 多对象跟踪器
 */
class ObjectTracker {
public:
    ObjectTracker();
    ~ObjectTracker();
    
    /**
     * 初始化跟踪器
     */
    bool initialize();
    
    /**
     * 更新跟踪
     * @param detections 当前帧的检测结果
     * @return 跟踪结果列表
     */
    std::vector<TrackedObject> update(const std::vector<Detection>& detections);
    
    /**
     * 获取当前跟踪的对象
     */
    std::vector<TrackedObject> getTrackedObjects() const;
    
    /**
     * 清除所有跟踪
     */
    void clear();
    
    /**
     * 设置跟踪参数
     */
    void setParameters(float iouThreshold, int maxAge, int minHits);
    
    /**
     * 获取跟踪统计信息
     */
    struct TrackingStats {
        int totalTracks;
        int activeTracks;
        int lostTracks;
        double avgTrackLength;
        
        TrackingStats() : totalTracks(0), activeTracks(0), lostTracks(0), avgTrackLength(0) {}
    };
    
    TrackingStats getTrackingStats() const;

private:
    std::vector<TrackedObject> m_trackedObjects;
    int m_nextTrackId;
    
    // 跟踪参数
    float m_iouThreshold;    // IoU阈值
    int m_maxAge;           // 最大跟踪年龄
    int m_minHits;          // 最小命中次数
    
    // 统计信息
    mutable TrackingStats m_stats;
    std::vector<int> m_trackLengths;
    
    /**
     * 数据关联 - 匈牙利算法
     */
    std::vector<std::pair<int, int>> associateDetectionsToTracks(
        const std::vector<Detection>& detections,
        const std::vector<TrackedObject>& tracks);
    
    /**
     * 计算IoU矩阵
     */
    std::vector<std::vector<float>> computeIoUMatrix(
        const std::vector<Detection>& detections,
        const std::vector<TrackedObject>& tracks) const;
    
    /**
     * 匈牙利算法实现
     */
    std::vector<std::pair<int, int>> hungarianAlgorithm(
        const std::vector<std::vector<float>>& costMatrix) const;
    
    /**
     * 创建新跟踪
     */
    TrackedObject createNewTrack(const Detection& detection);
    
    /**
     * 更新跟踪统计
     */
    void updateTrackingStats();
    
    /**
     * 移除过期跟踪
     */
    void removeExpiredTracks();
};

} // namespace fsl

#endif // OBJECT_TRACKER_H
