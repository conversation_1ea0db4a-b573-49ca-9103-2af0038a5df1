1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fsl.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:5:5-65
11-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:5:22-62
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:6:5-80
12-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:7:5-81
13-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:7:22-78
14
15    <uses-feature
15-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:9:5-11:35
16        android:name="android.hardware.camera"
16-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:10:9-47
17        android:required="true" />
17-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:11:9-32
18    <uses-feature
18-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:12:5-14:36
19        android:name="android.hardware.camera.autofocus"
19-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:13:9-57
20        android:required="false" />
20-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:14:9-33
21
22    <permission
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.fsl.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.fsl.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:16:5-37:19
29        android:name="com.fsl.app.FSLApplication"
29-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:17:9-39
30        android:allowBackup="true"
30-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:18:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5037e9d4ddaa957f7ac422280b19ed1f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:19:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:20:9-54
36        android:icon="@drawable/ic_launcher"
36-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:21:9-45
37        android:label="@string/app_name"
37-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:22:9-41
38        android:roundIcon="@drawable/ic_launcher_round"
38-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:23:9-56
39        android:supportsRtl="true"
39-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:24:9-35
40        android:theme="@style/Theme.FSL" >
40-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:25:9-41
41        <activity
41-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:27:9-36:20
42            android:name="com.fsl.app.MainActivity"
42-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:28:13-41
43            android:exported="true"
43-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:29:13-36
44            android:label="@string/app_name"
44-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:30:13-45
45            android:theme="@style/Theme.FSL" >
45-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:31:13-45
46            <intent-filter>
46-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:32:13-35:29
47                <action android:name="android.intent.action.MAIN" />
47-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:33:17-69
47-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:33:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:34:17-77
49-->F:\geek\fsl\android\app\src\main\AndroidManifest.xml:34:27-74
50            </intent-filter>
51        </activity>
52
53        <service
53-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:24:9-33:19
54            android:name="androidx.camera.core.impl.MetadataHolderService"
54-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:25:13-75
55            android:enabled="false"
55-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:26:13-36
56            android:exported="false" >
56-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:30:13-32:89
58                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
58-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:31:17-103
59                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
59-->[androidx.camera:camera-camera2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8b7981b0e212e929273085845f986a2\transformed\jetified-camera-camera2-1.2.0\AndroidManifest.xml:32:17-86
60        </service>
61
62        <activity
62-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
63            android:name="androidx.activity.ComponentActivity"
63-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
64            android:exported="true" />
64-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\aae616e8ec1d013515284160eadf1a66\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
65        <activity
65-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
66            android:name="androidx.compose.ui.tooling.PreviewActivity"
66-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
67            android:exported="true" />
67-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\627e03b6833454d0767978954b09c54b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
68
69        <provider
69-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
70            android:name="androidx.startup.InitializationProvider"
70-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
71            android:authorities="com.fsl.app.androidx-startup"
71-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
72            android:exported="false" >
72-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
73            <meta-data
73-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.emoji2.text.EmojiCompatInitializer"
74-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
75                android:value="androidx.startup" />
75-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\40b4bd9167a1a30f3950723d85de44dd\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
78                android:value="androidx.startup" />
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db3287a6935f39b0c4d01e5218b448e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
81                android:value="androidx.startup" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
82        </provider>
83
84        <service
84-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:25:9-28:40
85            android:name="androidx.room.MultiInstanceInvalidationService"
85-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:26:13-74
86            android:directBootAware="true"
86-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:27:13-43
87            android:exported="false" />
87-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed3be6f40472071d25eeb65377ffebea\transformed\room-runtime-2.4.3\AndroidManifest.xml:28:13-37
88
89        <receiver
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
90            android:name="androidx.profileinstaller.ProfileInstallReceiver"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
91            android:directBootAware="false"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
92            android:enabled="true"
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
93            android:exported="true"
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
94            android:permission="android.permission.DUMP" >
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
96                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
99                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
102                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
105                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\38f5386739065f89b8d6ced467c7b8cc\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
106            </intent-filter>
107        </receiver>
108    </application>
109
110</manifest>
