/**
 * 应用程序类
 * 
 * 负责应用的全局初始化和依赖注入配置
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class FSLApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化应用程序
        initializeApp()
    }
    
    /**
     * 初始化应用程序
     */
    private fun initializeApp() {
        // 这里可以添加全局初始化逻辑
        // 例如：日志配置、崩溃报告、性能监控等
    }
}
