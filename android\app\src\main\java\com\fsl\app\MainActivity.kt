/**
 * 主活动类
 *
 * 负责应用的主要导航和全局状态管理
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
// import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.fsl.app.ui.camera.CameraScreen
import com.fsl.app.ui.gallery.GalleryScreen
import com.fsl.app.ui.learning.LearningScreen
import com.fsl.app.ui.learning.SampleCollectionScreen
import com.fsl.app.ui.settings.SettingsScreen
import com.fsl.app.ui.components.BottomNavigationBar
import com.fsl.app.ui.theme.FSLTheme
import com.fsl.app.presentation.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // enableEdgeToEdge()

        setContent {
            FSLTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen()
                }
            }
        }
    }
}

@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavHostController = rememberNavController(),
    viewModel: MainViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        bottomBar = {
            BottomNavigationBar(
                currentRoute = getCurrentRoute(navController),
                onNavigate = { route ->
                    navController.navigate(route) {
                        // 避免重复导航到同一个目的地
                        popUpTo(navController.graph.startDestinationId) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                }
            )
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = "camera",
            modifier = Modifier.padding(paddingValues)
        ) {
            composable("camera") {
                CameraScreen(
                    onNavigateToLearning = {
                        navController.navigate("learning")
                    }
                )
            }
            composable("gallery") {
                GalleryScreen(
                    onNavigateToCamera = {
                        navController.navigate("camera")
                    }
                )
            }
            composable("learning") {
                LearningScreen(
                    onNavigateToCamera = {
                        navController.navigate("camera")
                    },
                    onNavigateToSampleCollection = { className ->
                        navController.navigate("sample_collection/$className")
                    }
                )
            }
            composable("sample_collection/{className}") { backStackEntry ->
                val className = backStackEntry.arguments?.getString("className") ?: ""
                val learningViewModel: com.fsl.app.presentation.learning.LearningViewModel = hiltViewModel()

                SampleCollectionScreen(
                    className = className,
                    onSamplesCollected = { collectedClassName, samples ->
                        android.util.Log.i("MainActivity", "开始处理样本收集: $collectedClassName, 样本数: ${samples.size}")

                        // 调用LearningViewModel添加类别
                        learningViewModel.addClassFromSamples(collectedClassName, samples)

                        // 延迟导航，确保数据处理完成
                        CoroutineScope(Dispatchers.Main).launch {
                            delay(1000) // 等待1秒确保处理完成
                            android.util.Log.i("MainActivity", "样本处理完成，导航回学习页面")
                            navController.navigate("learning") {
                                popUpTo("learning") { inclusive = true }
                            }
                        }
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            composable("settings") {
                SettingsScreen()
            }
        }
    }
}

@Composable
private fun getCurrentRoute(navController: NavHostController): String? {
    return navController.currentBackStackEntry?.destination?.route
}
