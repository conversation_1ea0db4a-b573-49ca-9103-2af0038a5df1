/**
 * 主活动类
 *
 * 负责应用的主要导航和全局状态管理
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
// import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.fsl.app.ui.camera.CameraScreen
import com.fsl.app.ui.gallery.GalleryScreen
import com.fsl.app.ui.learning.LearningScreen
import com.fsl.app.ui.learning.SampleCollectionScreen
import com.fsl.app.ui.settings.SettingsScreen
import com.fsl.app.ui.components.BottomNavigationBar
import com.fsl.app.ui.theme.FSLTheme
import com.fsl.app.presentation.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val cameraGranted = permissions[Manifest.permission.CAMERA] ?: false
        val storageGranted = permissions[Manifest.permission.WRITE_EXTERNAL_STORAGE] ?: false

        when {
            cameraGranted && storageGranted -> {
                android.util.Log.i("MainActivity", "所有权限已授予")
                Toast.makeText(this, "权限已授予，可以正常使用应用", Toast.LENGTH_SHORT).show()
            }
            cameraGranted && !storageGranted -> {
                android.util.Log.w("MainActivity", "相机权限已授予，但存储权限被拒绝")
                Toast.makeText(this, "需要存储权限才能保存图片", Toast.LENGTH_LONG).show()
            }
            !cameraGranted && storageGranted -> {
                android.util.Log.w("MainActivity", "存储权限已授予，但相机权限被拒绝")
                Toast.makeText(this, "需要相机权限才能拍摄图片", Toast.LENGTH_LONG).show()
            }
            else -> {
                android.util.Log.e("MainActivity", "所有权限都被拒绝")
                Toast.makeText(this, "应用需要相机和存储权限才能正常工作", Toast.LENGTH_LONG).show()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // enableEdgeToEdge()

        // 检查并请求权限
        checkAndRequestPermissions()

        setContent {
            FSLTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen()
                }
            }
        }
    }

    /**
     * 检查并请求必要的权限
     */
    private fun checkAndRequestPermissions() {
        val requiredPermissions = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        val missingPermissions = requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            android.util.Log.i("MainActivity", "请求权限: ${missingPermissions.joinToString()}")
            permissionLauncher.launch(missingPermissions.toTypedArray())
        } else {
            android.util.Log.i("MainActivity", "所有权限已授予")
        }
    }
}

@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavHostController = rememberNavController(),
    viewModel: MainViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        bottomBar = {
            BottomNavigationBar(
                currentRoute = getCurrentRoute(navController),
                onNavigate = { route ->
                    navController.navigate(route) {
                        // 避免重复导航到同一个目的地
                        popUpTo(navController.graph.startDestinationId) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                }
            )
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = "camera",
            modifier = Modifier.padding(paddingValues)
        ) {
            composable("camera") {
                CameraScreen(
                    onNavigateToLearning = {
                        navController.navigate("learning")
                    }
                )
            }
            composable("gallery") {
                GalleryScreen(
                    onNavigateToCamera = {
                        navController.navigate("camera")
                    }
                )
            }
            composable("learning") {
                LearningScreen(
                    onNavigateToCamera = {
                        navController.navigate("camera")
                    },
                    onNavigateToSampleCollection = { className ->
                        navController.navigate("sample_collection/$className")
                    }
                )
            }
            composable("sample_collection/{className}") { backStackEntry ->
                val className = backStackEntry.arguments?.getString("className") ?: ""
                val learningViewModel: com.fsl.app.presentation.learning.LearningViewModel = hiltViewModel()

                SampleCollectionScreen(
                    className = className,
                    onSamplesCollected = { collectedClassName, samples ->
                        android.util.Log.i("MainActivity", "开始处理样本收集: $collectedClassName, 样本数: ${samples.size}")

                        // 调用LearningViewModel添加类别
                        learningViewModel.addClassFromSamples(collectedClassName, samples)

                        // 延迟导航，确保数据处理完成
                        CoroutineScope(Dispatchers.Main).launch {
                            delay(1000) // 等待1秒确保处理完成
                            android.util.Log.i("MainActivity", "样本处理完成，导航回学习页面")
                            navController.navigate("learning") {
                                popUpTo("learning") { inclusive = true }
                            }
                        }
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            composable("settings") {
                SettingsScreen()
            }
        }
    }
}

@Composable
private fun getCurrentRoute(navController: NavHostController): String? {
    return navController.currentBackStackEntry?.destination?.route
}
