// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.di;

import android.content.Context;
import com.fsl.app.data.utils.FileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AppModule_ProvideFileManagerFactory implements Factory<FileManager> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideFileManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FileManager get() {
    return provideFileManager(contextProvider.get());
  }

  public static AppModule_ProvideFileManagerFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideFileManagerFactory(contextProvider);
  }

  public static FileManager provideFileManager(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideFileManager(context));
  }
}
