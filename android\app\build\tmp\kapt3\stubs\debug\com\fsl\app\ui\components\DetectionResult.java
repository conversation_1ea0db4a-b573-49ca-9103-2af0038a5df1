package com.fsl.app.ui.components;

import java.lang.System;

/**
 * 检测结果数据类
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001a"}, d2 = {"Lcom/fsl/app/ui/components/DetectionResult;", "", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "boundingBox", "Landroidx/compose/ui/geometry/Rect;", "timestamp", "", "(Lcom/fsl/app/domain/model/ClassificationResult;Landroidx/compose/ui/geometry/Rect;J)V", "getBoundingBox", "()Landroidx/compose/ui/geometry/Rect;", "getClassificationResult", "()Lcom/fsl/app/domain/model/ClassificationResult;", "getTimestamp", "()J", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class DetectionResult {
    @org.jetbrains.annotations.NotNull
    private final com.fsl.app.domain.model.ClassificationResult classificationResult = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.compose.ui.geometry.Rect boundingBox = null;
    private final long timestamp = 0L;
    
    /**
     * 检测结果数据类
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.ui.components.DetectionResult copy(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult classificationResult, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.geometry.Rect boundingBox, long timestamp) {
        return null;
    }
    
    /**
     * 检测结果数据类
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 检测结果数据类
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 检测结果数据类
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public DetectionResult(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.ClassificationResult classificationResult, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.geometry.Rect boundingBox, long timestamp) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.ClassificationResult component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.ClassificationResult getClassificationResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.geometry.Rect component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.geometry.Rect getBoundingBox() {
        return null;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
}