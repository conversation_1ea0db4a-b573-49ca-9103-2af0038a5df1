package com.fsl.app.presentation.learning;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0092\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0014\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u0007\u0018\u00002\u00020\u0001B7\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001fJ\u0006\u0010 \u001a\u00020\u0013J\u0006\u0010!\u001a\u00020\u001dJ\u0011\u0010\"\u001a\u00020\u001fH\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u0006\u0010$\u001a\u00020\u001dJ\u0006\u0010%\u001a\u00020\u001dJ\u0006\u0010&\u001a\u00020\u001dJ\u000e\u0010\'\u001a\u00020\u001d2\u0006\u0010(\u001a\u00020)J\b\u0010*\u001a\u00020+H\u0002J\u0010\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020)H\u0002J\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u001f00J\f\u00101\u001a\b\u0012\u0004\u0012\u00020+00J\u0006\u00102\u001a\u000203J\u0006\u00104\u001a\u00020\u0013J\u000e\u00105\u001a\u00020\u001d2\u0006\u00106\u001a\u000207J\u0006\u00108\u001a\u00020\u001dJ\u0006\u00109\u001a\u00020\u001dJ\u0006\u0010:\u001a\u00020\u001dJ\u0006\u0010;\u001a\u00020\u001dJ!\u0010<\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010=\u001a\u00020>H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010?J\u000e\u0010@\u001a\u00020\u001d2\u0006\u0010=\u001a\u00020>J\u0010\u0010A\u001a\u00020\u001d2\b\u0010B\u001a\u0004\u0018\u00010\u0019J\u000e\u0010C\u001a\u00020\u001d2\u0006\u0010D\u001a\u00020)J\u0006\u0010E\u001a\u00020\u001dJ!\u0010F\u001a\u00020\u001d2\u0006\u0010G\u001a\u0002032\u0006\u0010H\u001a\u00020>H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010IJ\u001a\u0010J\u001a\u00020\u00132\b\u0010\u001e\u001a\u0004\u0018\u00010\u001f2\u0006\u0010K\u001a\u00020>H\u0002R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00130\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0017\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006L"}, d2 = {"Lcom/fsl/app/presentation/learning/SampleCollectionViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "modelRepository", "Lcom/fsl/app/domain/repository/IModelRepository;", "classificationUseCase", "Lcom/fsl/app/domain/usecase/ClassificationUseCase;", "imageProcessingUseCase", "Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;", "galleryRepository", "Lcom/fsl/app/data/repository/GalleryRepositoryImpl;", "(Landroid/app/Application;Lcom/fsl/app/domain/repository/IInferenceRepository;Lcom/fsl/app/domain/repository/IModelRepository;Lcom/fsl/app/domain/usecase/ClassificationUseCase;Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;Lcom/fsl/app/data/repository/GalleryRepositoryImpl;)V", "_collectionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/learning/SampleCollectionState;", "_shouldCaptureFrame", "", "collectionState", "Lkotlinx/coroutines/flow/StateFlow;", "getCollectionState", "()Lkotlinx/coroutines/flow/StateFlow;", "currentImageProxy", "Landroidx/camera/core/ImageProxy;", "shouldCaptureFrame", "getShouldCaptureFrame", "addSample", "", "bitmap", "Landroid/graphics/Bitmap;", "canCompleteCollection", "captureCurrentFrame", "captureRealCameraFrame", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearError", "clearSamples", "confirmAndLearnAllSamples", "confirmAndLearnSample", "sampleIndex", "", "createDefaultFeatures", "", "estimateLearningTime", "", "sampleCount", "getCollectedBitmaps", "", "getCollectedFeatures", "getCollectionProgress", "", "isCollectionComplete", "removeSample", "sample", "Lcom/fsl/app/presentation/learning/CollectedSample;", "requestCameraFrame", "reset", "resetCaptureRequest", "resetLearningCompletedState", "saveToGallery", "className", "", "(Landroid/graphics/Bitmap;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setClassName", "setCurrentImageProxy", "imageProxy", "setTargetSampleCount", "count", "toggleGpuAcceleration", "updateLearningProgress", "progress", "stage", "(FLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateBitmap", "context", "app_debug"})
public final class SampleCollectionViewModel extends androidx.lifecycle.AndroidViewModel {
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final com.fsl.app.domain.repository.IModelRepository modelRepository = null;
    private final com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase = null;
    private final com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase = null;
    private final com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.learning.SampleCollectionState> _collectionState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.learning.SampleCollectionState> collectionState = null;
    private androidx.camera.core.ImageProxy currentImageProxy;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _shouldCaptureFrame = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> shouldCaptureFrame = null;
    
    @javax.inject.Inject
    public SampleCollectionViewModel(@org.jetbrains.annotations.NotNull
    android.app.Application application, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IModelRepository modelRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.learning.SampleCollectionState> getCollectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getShouldCaptureFrame() {
        return null;
    }
    
    /**
     * 设置当前相机帧
     * 由CameraPreview组件调用，提供实时相机帧
     */
    public final void setCurrentImageProxy(@org.jetbrains.annotations.Nullable
    androidx.camera.core.ImageProxy imageProxy) {
    }
    
    /**
     * 请求捕获相机帧
     * 触发相机组件提供当前帧
     */
    public final void requestCameraFrame() {
    }
    
    /**
     * 重置帧捕获请求
     */
    public final void resetCaptureRequest() {
    }
    
    /**
     * 切换GPU加速
     */
    public final void toggleGpuAcceleration() {
    }
    
    /**
     * 重置学习完成状态
     */
    public final void resetLearningCompletedState() {
    }
    
    /**
     * 设置要收集的类别名称
     */
    public final void setClassName(@org.jetbrains.annotations.NotNull
    java.lang.String className) {
    }
    
    /**
     * 设置目标样本数量
     */
    public final void setTargetSampleCount(int count) {
    }
    
    /**
     * 添加样本
     */
    public final void addSample(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 移除样本
     */
    public final void removeSample(@org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.learning.CollectedSample sample) {
    }
    
    /**
     * 清除所有样本
     */
    public final void clearSamples() {
    }
    
    /**
     * 检查是否可以完成收集
     */
    public final boolean canCompleteCollection() {
        return false;
    }
    
    /**
     * 获取收集的样本位图列表
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.Bitmap> getCollectedBitmaps() {
        return null;
    }
    
    /**
     * 获取收集的特征列表
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<float[]> getCollectedFeatures() {
        return null;
    }
    
    /**
     * 重置收集状态
     */
    public final void reset() {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 获取收集进度
     */
    public final float getCollectionProgress() {
        return 0.0F;
    }
    
    /**
     * 是否收集完成
     */
    public final boolean isCollectionComplete() {
        return false;
    }
    
    /**
     * 捕获当前帧（优化版本：快速拍照，异步学习）
     * 在真实应用中，这里应该与CameraPreview组件集成
     */
    public final void captureCurrentFrame() {
    }
    
    /**
     * 从相机获取当前帧
     * 真实实现：使用真实相机捕获帧并进行缩放处理
     */
    private final java.lang.Object captureRealCameraFrame(kotlin.coroutines.Continuation<? super android.graphics.Bitmap> continuation) {
        return null;
    }
    
    /**
     * 创建默认特征向量
     * 当特征提取失败时使用
     */
    private final float[] createDefaultFeatures() {
        return null;
    }
    
    /**
     * 验证Bitmap是否有效
     */
    private final boolean validateBitmap(android.graphics.Bitmap bitmap, java.lang.String context) {
        return false;
    }
    
    /**
     * 确认样本并开始学习
     * 用户确认样本后调用此方法进行异步学习
     */
    public final void confirmAndLearnSample(int sampleIndex) {
    }
    
    /**
     * 批量确认并学习所有样本
     * 用户确认所有样本后调用此方法
     * 支持GPU加速和实时进度显示
     */
    public final void confirmAndLearnAllSamples() {
    }
    
    /**
     * 更新学习进度
     * @param progress 进度值 (0.0-1.0)
     * @param stage 当前阶段描述
     */
    private final java.lang.Object updateLearningProgress(float progress, java.lang.String stage, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 估算学习时间
     * @param sampleCount 样本数量
     * @return 估计的学习时间（毫秒）
     */
    private final long estimateLearningTime(int sampleCount) {
        return 0L;
    }
    
    /**
     * 保存样本到图库
     */
    private final java.lang.Object saveToGallery(android.graphics.Bitmap bitmap, java.lang.String className, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
}