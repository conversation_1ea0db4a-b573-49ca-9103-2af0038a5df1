package com.fsl.app.presentation.learning;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0014\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B7\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0019J\u0006\u0010\u001a\u001a\u00020\u001bJ\u0006\u0010\u001c\u001a\u00020\u0017J\u0011\u0010\u001d\u001a\u00020\u0019H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001eJ\u0006\u0010\u001f\u001a\u00020\u0017J\u0006\u0010 \u001a\u00020\u0017J\u0006\u0010!\u001a\u00020\u0017J\u000e\u0010\"\u001a\u00020\u00172\u0006\u0010#\u001a\u00020$J\b\u0010%\u001a\u00020&H\u0002J\u0010\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020$H\u0002J\f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00190+J\f\u0010,\u001a\b\u0012\u0004\u0012\u00020&0+J\u0006\u0010-\u001a\u00020.J\u0006\u0010/\u001a\u00020\u001bJ\u000e\u00100\u001a\u00020\u00172\u0006\u00101\u001a\u000202J\u0006\u00103\u001a\u00020\u0017J!\u00104\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u00105\u001a\u000206H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00107J\u000e\u00108\u001a\u00020\u00172\u0006\u00105\u001a\u000206J\u000e\u00109\u001a\u00020\u00172\u0006\u0010:\u001a\u00020$J\u0006\u0010;\u001a\u00020\u0017J!\u0010<\u001a\u00020\u00172\u0006\u0010=\u001a\u00020.2\u0006\u0010>\u001a\u000206H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010?R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00110\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006@"}, d2 = {"Lcom/fsl/app/presentation/learning/SampleCollectionViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "modelRepository", "Lcom/fsl/app/domain/repository/IModelRepository;", "classificationUseCase", "Lcom/fsl/app/domain/usecase/ClassificationUseCase;", "imageProcessingUseCase", "Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;", "galleryRepository", "Lcom/fsl/app/data/repository/GalleryRepositoryImpl;", "(Landroid/app/Application;Lcom/fsl/app/domain/repository/IInferenceRepository;Lcom/fsl/app/domain/repository/IModelRepository;Lcom/fsl/app/domain/usecase/ClassificationUseCase;Lcom/fsl/app/domain/usecase/ImageProcessingUseCase;Lcom/fsl/app/data/repository/GalleryRepositoryImpl;)V", "_collectionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/learning/SampleCollectionState;", "collectionState", "Lkotlinx/coroutines/flow/StateFlow;", "getCollectionState", "()Lkotlinx/coroutines/flow/StateFlow;", "addSample", "", "bitmap", "Landroid/graphics/Bitmap;", "canCompleteCollection", "", "captureCurrentFrame", "captureRealCameraFrame", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearError", "clearSamples", "confirmAndLearnAllSamples", "confirmAndLearnSample", "sampleIndex", "", "createDefaultFeatures", "", "estimateLearningTime", "", "sampleCount", "getCollectedBitmaps", "", "getCollectedFeatures", "getCollectionProgress", "", "isCollectionComplete", "removeSample", "sample", "Lcom/fsl/app/presentation/learning/CollectedSample;", "reset", "saveToGallery", "className", "", "(Landroid/graphics/Bitmap;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setClassName", "setTargetSampleCount", "count", "toggleGpuAcceleration", "updateLearningProgress", "progress", "stage", "(FLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SampleCollectionViewModel extends androidx.lifecycle.AndroidViewModel {
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final com.fsl.app.domain.repository.IModelRepository modelRepository = null;
    private final com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase = null;
    private final com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase = null;
    private final com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.learning.SampleCollectionState> _collectionState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.learning.SampleCollectionState> collectionState = null;
    
    @javax.inject.Inject
    public SampleCollectionViewModel(@org.jetbrains.annotations.NotNull
    android.app.Application application, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IModelRepository modelRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ImageProcessingUseCase imageProcessingUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.repository.GalleryRepositoryImpl galleryRepository) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.learning.SampleCollectionState> getCollectionState() {
        return null;
    }
    
    /**
     * 设置要收集的类别名称
     */
    public final void setClassName(@org.jetbrains.annotations.NotNull
    java.lang.String className) {
    }
    
    /**
     * 设置目标样本数量
     */
    public final void setTargetSampleCount(int count) {
    }
    
    /**
     * 添加样本
     */
    public final void addSample(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 移除样本
     */
    public final void removeSample(@org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.learning.CollectedSample sample) {
    }
    
    /**
     * 清除所有样本
     */
    public final void clearSamples() {
    }
    
    /**
     * 检查是否可以完成收集
     */
    public final boolean canCompleteCollection() {
        return false;
    }
    
    /**
     * 获取收集的样本位图列表
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.Bitmap> getCollectedBitmaps() {
        return null;
    }
    
    /**
     * 获取收集的特征列表
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<float[]> getCollectedFeatures() {
        return null;
    }
    
    /**
     * 重置收集状态
     */
    public final void reset() {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 获取收集进度
     */
    public final float getCollectionProgress() {
        return 0.0F;
    }
    
    /**
     * 是否收集完成
     */
    public final boolean isCollectionComplete() {
        return false;
    }
    
    /**
     * 捕获当前帧（优化版本：快速拍照，异步学习）
     * 在真实应用中，这里应该与CameraPreview组件集成
     */
    public final void captureCurrentFrame() {
    }
    
    /**
     * 从相机获取当前帧
     * 真实实现：应该与相机预览组件集成
     */
    private final java.lang.Object captureRealCameraFrame(kotlin.coroutines.Continuation<? super android.graphics.Bitmap> continuation) {
        return null;
    }
    
    /**
     * 创建默认特征向量
     * 当特征提取失败时使用
     */
    private final float[] createDefaultFeatures() {
        return null;
    }
    
    /**
     * 确认样本并开始学习
     * 用户确认样本后调用此方法进行异步学习
     */
    public final void confirmAndLearnSample(int sampleIndex) {
    }
    
    /**
     * 批量确认并学习所有样本
     * 用户确认所有样本后调用此方法
     * 支持GPU加速和实时进度显示
     */
    public final void confirmAndLearnAllSamples() {
    }
    
    /**
     * 更新学习进度
     * @param progress 进度值 (0.0-1.0)
     * @param stage 当前阶段描述
     */
    private final java.lang.Object updateLearningProgress(float progress, java.lang.String stage, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 估算学习时间
     * @param sampleCount 样本数量
     * @return 估计的学习时间（毫秒）
     */
    private final long estimateLearningTime(int sampleCount) {
        return 0L;
    }
    
    /**
     * 切换GPU加速模式
     */
    public final void toggleGpuAcceleration() {
    }
    
    /**
     * 保存样本到图库
     */
    private final java.lang.Object saveToGallery(android.graphics.Bitmap bitmap, java.lang.String className, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
}