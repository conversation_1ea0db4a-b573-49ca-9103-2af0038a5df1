/**
 * 权限处理组件
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState

/**
 * 权限处理器
 * 
 * @param permission 需要的权限
 * @param onPermissionGranted 权限授予时的回调
 * @param onPermissionDenied 权限拒绝时的回调
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionHandler(
    permission: String,
    onPermissionGranted: @Composable () -> Unit,
    onPermissionDenied: @Composable () -> Unit
) {
    val permissionState = rememberPermissionState(permission)
    var hasRequestedPermission by remember { mutableStateOf(false) }

    LaunchedEffect(permissionState.status) {
        if (!permissionState.status.isGranted && !hasRequestedPermission) {
            permissionState.launchPermissionRequest()
            hasRequestedPermission = true
        }
    }

    if (permissionState.status.isGranted) {
        onPermissionGranted()
    } else {
        onPermissionDenied()
    }
}
