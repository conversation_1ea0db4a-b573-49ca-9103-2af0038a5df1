/**
 * 应用数据库
 * 
 * Room数据库配置
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import android.content.Context

@Database(
    entities = [LearnedClassEntity::class, TrainingSampleEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun learnedClassDao(): LearnedClassDao
    abstract fun trainingSampleDao(): TrainingSampleDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "fsl_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
