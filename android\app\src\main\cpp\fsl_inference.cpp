/**
 * FSL推理引擎实现
 *
 * 少样本学习推理引擎的C++实现
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "include/fsl_inference.h"
#include "include/prototypical_network.h"
#include "include/feature_extractor.h"
#include "include/nnapi_engine.h"
#include "include/object_tracker.h"
#include <chrono>
#include <algorithm>
#include <cmath>
#include <fstream>
#include <sstream>
#include <android/log.h>

#define LOG_TAG "FSL_Native"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

namespace fsl {

FSLInferenceEngine::FSLInferenceEngine()
    : m_initialized(false), m_confidenceThreshold(0.5f) {
    m_prototypicalNetwork = std::make_unique<PrototypicalNetwork>();
    m_featureExtractor = std::make_unique<FeatureExtractor>();
    m_nnapiEngine = std::make_unique<NNAPIEngine>();
    m_objectTracker = std::make_unique<ObjectTracker>();
}

FSLInferenceEngine::~FSLInferenceEngine() = default;

bool FSLInferenceEngine::initialize() {
    LOGI("Initializing FSL Inference Engine with NNAPI...");

    try {
        // 1. 初始化NNAPI引擎
        if (!initializeNNAPI()) {
            LOGE("Failed to initialize NNAPI engine");
            // 继续使用CPU后备方案
        }

        // 2. 初始化特征提取器
        if (!m_featureExtractor->initialize()) {
            LOGE("Failed to initialize feature extractor");
            return false;
        }

        // 3. 初始化原型网络
        int featureDim = m_featureExtractor->getFeatureDim();
        if (!m_prototypicalNetwork->initialize(featureDim)) {
            LOGE("Failed to initialize prototypical network");
            return false;
        }

        // 4. 初始化对象跟踪器
        if (!initializeTracker()) {
            LOGE("Failed to initialize object tracker");
            return false;
        }

        // 5. 初始化一些默认类别用于演示
        initializeDefaultClasses();

        m_initialized = true;
        LOGI("FSL Inference Engine initialized successfully");
        return true;

    } catch (const std::exception& e) {
        LOGE("Exception during initialization: %s", e.what());
        return false;
    }
}

ClassificationResult FSLInferenceEngine::classify(const float* imageData, int width, int height) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return ClassificationResult("Error", 0.0f, std::map<std::string, float>(), 0);
    }

    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // 提取特征
        FeatureVector features = m_featureExtractor->extractFeatures(imageData, width, height);

        // 使用原型网络进行分类
        auto result = m_prototypicalNetwork->classify(features);
        auto allScores = m_prototypicalNetwork->computeAllScores(features);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        ClassificationResult classResult;
        classResult.className = result.first;
        classResult.confidence = result.second;
        classResult.allScores = allScores;
        classResult.inferenceTime = duration.count();

        LOGI("Classification result: %s (%.2f%%) in %ldms",
             result.first.c_str(), result.second * 100, duration.count());

        return classResult;

    } catch (const std::exception& e) {
        LOGE("Exception during classification: %s", e.what());
        return ClassificationResult("Error", 0.0f, std::map<std::string, float>(), 0);
    }
}

FeatureVector FSLInferenceEngine::extractFeatures(const float* imageData, int width, int height) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return FeatureVector();
    }

    try {
        return m_featureExtractor->extractFeatures(imageData, width, height);
    } catch (const std::exception& e) {
        LOGE("Exception during feature extraction: %s", e.what());
        return FeatureVector();
    }
}

bool FSLInferenceEngine::addClass(const std::string& className, const FeatureMatrix& features) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return false;
    }

    if (features.empty()) {
        LOGE("No features provided for class: %s", className.c_str());
        return false;
    }

    try {
        // 计算原型
        FeatureVector prototype = computePrototype(features);

        // 添加到网络
        bool success = m_prototypicalNetwork->addPrototype(className, prototype);

        if (success) {
            LOGI("Added class: %s with %zu samples", className.c_str(), features.size());
        } else {
            LOGE("Failed to add class: %s", className.c_str());
        }

        return success;

    } catch (const std::exception& e) {
        LOGE("Exception while adding class %s: %s", className.c_str(), e.what());
        return false;
    }
}

bool FSLInferenceEngine::updateClass(const std::string& className, const FeatureMatrix& features) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return false;
    }

    try {
        // 计算新的原型
        FeatureVector newPrototype = computePrototype(features);

        // 更新网络中的原型
        bool success = m_prototypicalNetwork->updatePrototype(className, newPrototype);

        if (success) {
            LOGI("Updated class: %s with %zu samples", className.c_str(), features.size());
        } else {
            LOGE("Failed to update class: %s", className.c_str());
        }

        return success;

    } catch (const std::exception& e) {
        LOGE("Exception while updating class %s: %s", className.c_str(), e.what());
        return false;
    }
}

bool FSLInferenceEngine::removeClass(const std::string& className) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return false;
    }

    try {
        bool success = m_prototypicalNetwork->removePrototype(className);

        if (success) {
            LOGI("Removed class: %s", className.c_str());
        } else {
            LOGE("Failed to remove class: %s", className.c_str());
        }

        return success;

    } catch (const std::exception& e) {
        LOGE("Exception while removing class %s: %s", className.c_str(), e.what());
        return false;
    }
}

std::vector<std::string> FSLInferenceEngine::getClassNames() const {
    if (!m_initialized) {
        return std::vector<std::string>();
    }

    std::vector<std::string> classNames;
    const auto& prototypes = m_prototypicalNetwork->getPrototypes();

    for (const auto& pair : prototypes) {
        classNames.push_back(pair.first);
    }

    return classNames;
}

bool FSLInferenceEngine::saveModel(const std::string& filePath) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return false;
    }

    try {
        std::ofstream file(filePath, std::ios::binary);
        if (!file.is_open()) {
            LOGE("Failed to open file for writing: %s", filePath.c_str());
            return false;
        }

        const auto& prototypes = m_prototypicalNetwork->getPrototypes();

        // 写入类别数量
        size_t classCount = prototypes.size();
        file.write(reinterpret_cast<const char*>(&classCount), sizeof(classCount));

        // 写入特征维度
        int featureDim = m_prototypicalNetwork->getFeatureDim();
        file.write(reinterpret_cast<const char*>(&featureDim), sizeof(featureDim));

        // 写入每个类别的原型
        for (const auto& pair : prototypes) {
            // 写入类别名称长度和名称
            size_t nameLength = pair.first.length();
            file.write(reinterpret_cast<const char*>(&nameLength), sizeof(nameLength));
            file.write(pair.first.c_str(), nameLength);

            // 写入原型向量
            const auto& prototype = pair.second;
            file.write(reinterpret_cast<const char*>(prototype.data()),
                      prototype.size() * sizeof(float));
        }

        file.close();
        LOGI("Model saved to: %s", filePath.c_str());
        return true;

    } catch (const std::exception& e) {
        LOGE("Exception while saving model: %s", e.what());
        return false;
    }
}

bool FSLInferenceEngine::loadModel(const std::string& filePath) {
    if (!m_initialized) {
        LOGE("Engine not initialized");
        return false;
    }

    try {
        std::ifstream file(filePath, std::ios::binary);
        if (!file.is_open()) {
            LOGE("Failed to open file for reading: %s", filePath.c_str());
            return false;
        }

        // 清空现有原型
        m_prototypicalNetwork->clear();

        // 读取类别数量
        size_t classCount;
        file.read(reinterpret_cast<char*>(&classCount), sizeof(classCount));

        // 读取特征维度
        int featureDim;
        file.read(reinterpret_cast<char*>(&featureDim), sizeof(featureDim));

        // 验证特征维度
        if (featureDim != m_prototypicalNetwork->getFeatureDim()) {
            LOGE("Feature dimension mismatch: expected %d, got %d",
                 m_prototypicalNetwork->getFeatureDim(), featureDim);
            return false;
        }

        // 读取每个类别的原型
        for (size_t i = 0; i < classCount; ++i) {
            // 读取类别名称
            size_t nameLength;
            file.read(reinterpret_cast<char*>(&nameLength), sizeof(nameLength));

            std::string className(nameLength, '\0');
            file.read(&className[0], nameLength);

            // 读取原型向量
            FeatureVector prototype(featureDim);
            file.read(reinterpret_cast<char*>(prototype.data()),
                     featureDim * sizeof(float));

            // 添加到网络
            m_prototypicalNetwork->addPrototype(className, prototype);
        }

        file.close();
        LOGI("Model loaded from: %s (%zu classes)", filePath.c_str(), classCount);
        return true;

    } catch (const std::exception& e) {
        LOGE("Exception while loading model: %s", e.what());
        return false;
    }
}

std::map<std::string, std::string> FSLInferenceEngine::getModelInfo() const {
    std::map<std::string, std::string> info;

    info["initialized"] = m_initialized ? "true" : "false";
    info["classCount"] = std::to_string(m_prototypicalNetwork ? m_prototypicalNetwork->getClassCount() : 0);
    info["featureDim"] = std::to_string(m_featureExtractor ? m_featureExtractor->getFeatureDim() : 0);
    info["nnapiAvailable"] = NNAPIEngine::isNNAPIAvailable() ? "true" : "false";
    info["trackingEnabled"] = (m_objectTracker != nullptr) ? "true" : "false";

    if (m_initialized) {
        auto classNames = getClassNames();
        std::stringstream ss;
        for (size_t i = 0; i < classNames.size(); ++i) {
            if (i > 0) ss << ",";
            ss << classNames[i];
        }
        info["classNames"] = ss.str();

        // 添加跟踪统计信息
        if (m_objectTracker) {
            auto stats = m_objectTracker->getTrackingStats();
            info["activeTracks"] = std::to_string(stats.activeTracks);
            info["totalTracks"] = std::to_string(stats.totalTracks);
        }
    }

    return info;
}

FeatureVector FSLInferenceEngine::computePrototype(const FeatureMatrix& features) {
    if (features.empty()) {
        return FeatureVector();
    }

    int featureDim = features[0].size();
    FeatureVector prototype(featureDim, 0.0f);

    // 计算均值
    for (const auto& feature : features) {
        for (int i = 0; i < featureDim; ++i) {
            prototype[i] += feature[i];
        }
    }

    float numSamples = static_cast<float>(features.size());
    for (int i = 0; i < featureDim; ++i) {
        prototype[i] /= numSamples;
    }

    // L2归一化
    return normalizeL2(prototype);
}

float FSLInferenceEngine::cosineSimilarity(const FeatureVector& a, const FeatureVector& b) {
    if (a.size() != b.size()) {
        return 0.0f;
    }

    float dotProduct = 0.0f;
    float normA = 0.0f;
    float normB = 0.0f;

    for (size_t i = 0; i < a.size(); ++i) {
        dotProduct += a[i] * b[i];
        normA += a[i] * a[i];
        normB += b[i] * b[i];
    }

    if (normA == 0.0f || normB == 0.0f) {
        return 0.0f;
    }

    return dotProduct / (std::sqrt(normA) * std::sqrt(normB));
}

FeatureVector FSLInferenceEngine::normalizeL2(const FeatureVector& vector) {
    float norm = 0.0f;
    for (float val : vector) {
        norm += val * val;
    }
    norm = std::sqrt(norm);

    if (norm == 0.0f) {
        return vector;
    }

    FeatureVector normalized(vector.size());
    for (size_t i = 0; i < vector.size(); ++i) {
        normalized[i] = vector[i] / norm;
    }

    return normalized;
}

void FSLInferenceEngine::initializeDefaultClasses() {
    // 初始化一些默认类别用于演示
    std::vector<std::string> defaultClasses = {"cat", "dog", "bird", "flower", "car"};

    for (size_t i = 0; i < defaultClasses.size(); ++i) {
        // 生成模拟原型
        FeatureVector prototype(512);
        for (int j = 0; j < 512; ++j) {
            prototype[j] = static_cast<float>(std::sin(i * 0.1 + j * 0.01)) * 0.5f +
                          static_cast<float>(i) * 0.2f;
        }

        prototype = normalizeL2(prototype);
        m_prototypicalNetwork->addPrototype(defaultClasses[i], prototype);
    }

    LOGI("Initialized %zu default classes", defaultClasses.size());
}

// 新增方法实现

bool FSLInferenceEngine::initializeNNAPI() {
    LOGI("Initializing NNAPI engine...");

    if (!NNAPIEngine::isNNAPIAvailable()) {
        LOGI("NNAPI not available on this device");
        return false;
    }

    if (!m_nnapiEngine->initialize()) {
        LOGE("Failed to initialize NNAPI engine");
        return false;
    }

    LOGI("NNAPI engine initialized successfully");
    return true;
}

bool FSLInferenceEngine::initializeTracker() {
    LOGI("Initializing object tracker...");

    if (!m_objectTracker->initialize()) {
        LOGE("Failed to initialize object tracker");
        return false;
    }

    // 设置默认跟踪参数
    m_objectTracker->setParameters(0.3f, 30, 3);

    LOGI("Object tracker initialized successfully");
    return true;
}

std::vector<TrackingResult> FSLInferenceEngine::trackAndClassify(const float* imageData, int width, int height) {
    std::vector<TrackingResult> results;

    if (!m_initialized) {
        LOGE("Engine not initialized");
        return results;
    }

    try {
        // 1. 执行分类检测
        auto classResult = classify(imageData, width, height);

        // 2. 创建检测结果（简化版本，实际应该有目标检测）
        std::vector<Detection> detections;
        if (classResult.confidence > m_confidenceThreshold) {
            Detection detection;
            detection.bbox = BoundingBox(0.1f, 0.1f, 0.8f, 0.8f, classResult.confidence);
            detection.className = classResult.className;
            detection.confidence = classResult.confidence;
            detection.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            detections.push_back(detection);
        }

        // 3. 更新跟踪器
        auto trackedObjects = m_objectTracker->update(detections);

        // 4. 转换为跟踪结果
        for (const auto& obj : trackedObjects) {
            TrackingResult result;
            result.trackId = obj.trackId;
            result.className = obj.className;
            result.confidence = obj.confidence;
            result.x = obj.bbox.x;
            result.y = obj.bbox.y;
            result.width = obj.bbox.width;
            result.height = obj.bbox.height;
            result.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            results.push_back(result);
        }

        LOGI("Tracked %zu objects", results.size());

    } catch (const std::exception& e) {
        LOGE("Exception during tracking: %s", e.what());
    }

    return results;
}

void FSLInferenceEngine::clearTracking() {
    if (m_objectTracker) {
        m_objectTracker->clear();
        LOGI("Tracking state cleared");
    }
}

void FSLInferenceEngine::setTrackingParams(float iouThreshold, int maxAge, int minHits) {
    if (m_objectTracker) {
        m_objectTracker->setParameters(iouThreshold, maxAge, minHits);
        LOGI("Tracking parameters updated: IoU=%.2f, MaxAge=%d, MinHits=%d",
             iouThreshold, maxAge, minHits);
    }
}

} // namespace fsl
