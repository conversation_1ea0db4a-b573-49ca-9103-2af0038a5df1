package com.fsl.app.presentation.learning;

import java.lang.System;

/**
 * 收集的样本数据
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0014\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J)\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u0096\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0016J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001a"}, d2 = {"Lcom/fsl/app/presentation/learning/CollectedSample;", "", "bitmap", "Landroid/graphics/Bitmap;", "timestamp", "", "features", "", "(Landroid/graphics/Bitmap;J[F)V", "getBitmap", "()Landroid/graphics/Bitmap;", "getFeatures", "()[F", "getTimestamp", "()J", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class CollectedSample {
    @org.jetbrains.annotations.NotNull
    private final android.graphics.Bitmap bitmap = null;
    private final long timestamp = 0L;
    @org.jetbrains.annotations.Nullable
    private final float[] features = null;
    
    /**
     * 收集的样本数据
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.presentation.learning.CollectedSample copy(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, long timestamp, @org.jetbrains.annotations.Nullable
    float[] features) {
        return null;
    }
    
    /**
     * 收集的样本数据
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public CollectedSample(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, long timestamp, @org.jetbrains.annotations.Nullable
    float[] features) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap getBitmap() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final float[] component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final float[] getFeatures() {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
}