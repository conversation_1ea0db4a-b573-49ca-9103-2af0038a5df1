package com.fsl.app.data.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\bJ\u000e\u0010\u000b\u001a\u00020\b2\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/fsl/app/data/utils/AssetUtils;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "assetExists", "", "fileName", "", "copyAssetToFile", "assetFileName", "loadAssetAsString", "app_debug"})
@javax.inject.Singleton
public final class AssetUtils {
    private final android.content.Context context = null;
    
    @javax.inject.Inject
    public AssetUtils(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        super();
    }
    
    /**
     * 从assets复制文件到内部存储
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String copyAssetToFile(@org.jetbrains.annotations.NotNull
    java.lang.String assetFileName) {
        return null;
    }
    
    /**
     * 检查assets中是否存在文件
     */
    public final boolean assetExists(@org.jetbrains.annotations.NotNull
    java.lang.String fileName) {
        return false;
    }
    
    /**
     * 加载Asset文件为字符串
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String loadAssetAsString(@org.jetbrains.annotations.NotNull
    java.lang.String fileName) {
        return null;
    }
}