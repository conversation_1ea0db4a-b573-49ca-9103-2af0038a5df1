package com.fsl.app.data.repository;

import java.lang.System;

/**
 * 图库仓库实现类
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 72\u00020\u0001:\u000278B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000f\u0010\u0010J(\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0013J\b\u0010\u0014\u001a\u00020\u0015H\u0002J.\u0010\u0016\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00190\u00170\fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001a\u0010\u0013J0\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\f2\u0006\u0010\u001c\u001a\u00020\u0018H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u0014\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070 H\u0016J(\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\"\u0010\u0013J\b\u0010#\u001a\u00020\u0015H\u0002J2\u0010$\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\b2\u0006\u0010%\u001a\u00020\u0018H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b&\u0010\'J\"\u0010(\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b)\u0010\u0013J-\u0010*\u001a\u00020\r2\u0006\u0010+\u001a\u00020\u00182\b\u0010,\u001a\u0004\u0018\u00010-2\b\b\u0002\u0010.\u001a\u00020/H\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u00100J1\u00101\u001a\u00020\r2\u001e\u00102\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020504\u0012\u0004\u0012\u00020\r03H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u00106R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00069"}, d2 = {"Lcom/fsl/app/data/repository/GalleryRepositoryImpl;", "Lcom/fsl/app/domain/repository/GalleryRepository;", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_imagesFlow", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/fsl/app/domain/model/GalleryImage;", "gson", "Lcom/google/gson/Gson;", "deleteImage", "Lkotlin/Result;", "", "image", "deleteImage-gIAlu-s", "(Lcom/fsl/app/domain/model/GalleryImage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllImages", "getAllImages-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getGalleryDir", "Ljava/io/File;", "getGalleryStats", "", "", "", "getGalleryStats-IoAF18A", "getImagesByLabel", "label", "getImagesByLabel-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getImagesFlow", "Lkotlinx/coroutines/flow/Flow;", "getLearnedImages", "getLearnedImages-IoAF18A", "getMetadataFile", "markAsLearned", "className", "markAsLearned-0E7RQCE", "(Lcom/fsl/app/domain/model/GalleryImage;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshGallery", "refreshGallery-IoAF18A", "saveImageMetadata", "fileName", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "timestamp", "", "(Ljava/lang/String;Lcom/fsl/app/domain/model/ClassificationResult;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMetadata", "updater", "Lkotlin/Function1;", "", "Lcom/fsl/app/data/repository/GalleryRepositoryImpl$ImageMetadata;", "(Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "ImageMetadata", "app_debug"})
@javax.inject.Singleton
public final class GalleryRepositoryImpl implements com.fsl.app.domain.repository.GalleryRepository {
    private final android.content.Context context = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.fsl.app.domain.model.GalleryImage>> _imagesFlow = null;
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.repository.GalleryRepositoryImpl.Companion Companion = null;
    private static final java.lang.String GALLERY_DIR = "FSL_Gallery";
    private static final java.lang.String METADATA_FILE = "image_metadata.json";
    
    @javax.inject.Inject
    public GalleryRepositoryImpl(@org.jetbrains.annotations.NotNull
    @dagger.hilt.android.qualifiers.ApplicationContext
    android.content.Context context) {
        super();
    }
    
    /**
     * 获取图库目录
     */
    private final java.io.File getGalleryDir() {
        return null;
    }
    
    /**
     * 获取元数据文件
     */
    private final java.io.File getMetadataFile() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public kotlinx.coroutines.flow.Flow<java.util.List<com.fsl.app.domain.model.GalleryImage>> getImagesFlow() {
        return null;
    }
    
    /**
     * 更新元数据文件
     */
    private final java.lang.Object updateMetadata(kotlin.jvm.functions.Function1<? super java.util.Map<java.lang.String, com.fsl.app.data.repository.GalleryRepositoryImpl.ImageMetadata>, kotlin.Unit> updater, kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 保存图像元数据
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveImageMetadata(@org.jetbrains.annotations.NotNull
    java.lang.String fileName, @org.jetbrains.annotations.Nullable
    com.fsl.app.domain.model.ClassificationResult classificationResult, long timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> continuation) {
        return null;
    }
    
    /**
     * 图像元数据数据类
     */
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0010\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\tH\u00c6\u0003J5\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00072\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\tH\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\rR\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001c"}, d2 = {"Lcom/fsl/app/data/repository/GalleryRepositoryImpl$ImageMetadata;", "", "timestamp", "", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "isLearned", "", "learnedClassName", "", "(JLcom/fsl/app/domain/model/ClassificationResult;ZLjava/lang/String;)V", "getClassificationResult", "()Lcom/fsl/app/domain/model/ClassificationResult;", "()Z", "getLearnedClassName", "()Ljava/lang/String;", "getTimestamp", "()J", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    static final class ImageMetadata {
        private final long timestamp = 0L;
        @org.jetbrains.annotations.Nullable
        private final com.fsl.app.domain.model.ClassificationResult classificationResult = null;
        private final boolean isLearned = false;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String learnedClassName = null;
        
        /**
         * 图像元数据数据类
         */
        @org.jetbrains.annotations.NotNull
        public final com.fsl.app.data.repository.GalleryRepositoryImpl.ImageMetadata copy(long timestamp, @org.jetbrains.annotations.Nullable
        com.fsl.app.domain.model.ClassificationResult classificationResult, boolean isLearned, @org.jetbrains.annotations.Nullable
        java.lang.String learnedClassName) {
            return null;
        }
        
        /**
         * 图像元数据数据类
         */
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        /**
         * 图像元数据数据类
         */
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        /**
         * 图像元数据数据类
         */
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public ImageMetadata(long timestamp, @org.jetbrains.annotations.Nullable
        com.fsl.app.domain.model.ClassificationResult classificationResult, boolean isLearned, @org.jetbrains.annotations.Nullable
        java.lang.String learnedClassName) {
            super();
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.Nullable
        public final com.fsl.app.domain.model.ClassificationResult component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final com.fsl.app.domain.model.ClassificationResult getClassificationResult() {
            return null;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final boolean isLearned() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getLearnedClassName() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/fsl/app/data/repository/GalleryRepositoryImpl$Companion;", "", "()V", "GALLERY_DIR", "", "METADATA_FILE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}