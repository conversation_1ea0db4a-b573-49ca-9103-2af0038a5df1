// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.di;

import android.content.Context;
import com.fsl.app.data.utils.AssetUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class AppModule_ProvideAssetUtilsFactory implements Factory<AssetUtils> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideAssetUtilsFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AssetUtils get() {
    return provideAssetUtils(contextProvider.get());
  }

  public static AppModule_ProvideAssetUtilsFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideAssetUtilsFactory(contextProvider);
  }

  public static AssetUtils provideAssetUtils(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideAssetUtils(context));
  }
}
