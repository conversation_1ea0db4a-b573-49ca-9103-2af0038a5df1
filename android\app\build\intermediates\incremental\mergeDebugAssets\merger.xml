<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\geek\fsl\android\app\src\main\assets"><file name="prototypical_network.ptl" path="F:\geek\fsl\android\app\src\main\assets\prototypical_network.ptl"/><file name="class_names.json" path="F:\geek\fsl\android\app\src\main\assets\class_names.json"/><file name="model_info.json" path="F:\geek\fsl\android\app\src\main\assets\model_info.json"/><file name="mobilenet_v3_small.ptl" path="F:\geek\fsl\android\app\src\main\assets\mobilenet_v3_small.ptl"/><file name="imagenet_classes.json" path="F:\geek\fsl\android\app\src\main\assets\imagenet_classes.json"/></source><source path="F:\geek\fsl\android\app\build\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\geek\fsl\android\app\src\debug\assets"/></dataSet></merger>