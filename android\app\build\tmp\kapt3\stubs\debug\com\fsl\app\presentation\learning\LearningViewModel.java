package com.fsl.app.presentation.learning;

import java.lang.System;

@dagger.hilt.android.lifecycle.HiltViewModel
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u0017J\u001c\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u0017J\u001c\u0010\u001a\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u0017J\u0006\u0010\u001b\u001a\u00020\u0013J\u000e\u0010\u001c\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015J\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u001f0\u001eJ\b\u0010 \u001a\u00020\u0013H\u0002J\u0006\u0010!\u001a\u00020\u0013J\u0006\u0010\"\u001a\u00020\u0013R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\r0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/fsl/app/presentation/learning/LearningViewModel;", "Landroidx/lifecycle/ViewModel;", "classificationUseCase", "Lcom/fsl/app/domain/usecase/ClassificationUseCase;", "incrementalLearningUseCase", "Lcom/fsl/app/domain/usecase/IncrementalLearningUseCase;", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "modelRepository", "Lcom/fsl/app/domain/repository/IModelRepository;", "(Lcom/fsl/app/domain/usecase/ClassificationUseCase;Lcom/fsl/app/domain/usecase/IncrementalLearningUseCase;Lcom/fsl/app/domain/repository/IInferenceRepository;Lcom/fsl/app/domain/repository/IModelRepository;)V", "_learningState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/presentation/learning/LearningState;", "learningState", "Lkotlinx/coroutines/flow/StateFlow;", "getLearningState", "()Lkotlinx/coroutines/flow/StateFlow;", "addClass", "", "className", "", "samples", "", "Landroid/graphics/Bitmap;", "addClassFromSamples", "addSamplesToClass", "clearError", "deleteClass", "getClassStatistics", "", "", "loadLearnedClasses", "refreshLearnedClasses", "resetTrainingState", "app_debug"})
public final class LearningViewModel extends androidx.lifecycle.ViewModel {
    private final com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase = null;
    private final com.fsl.app.domain.usecase.IncrementalLearningUseCase incrementalLearningUseCase = null;
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final com.fsl.app.domain.repository.IModelRepository modelRepository = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.presentation.learning.LearningState> _learningState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.learning.LearningState> learningState = null;
    
    @javax.inject.Inject
    public LearningViewModel(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.ClassificationUseCase classificationUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.usecase.IncrementalLearningUseCase incrementalLearningUseCase, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IModelRepository modelRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.presentation.learning.LearningState> getLearningState() {
        return null;
    }
    
    /**
     * 观察已学习的类别 - 从数据库加载真实数据
     */
    private final void loadLearnedClasses() {
    }
    
    /**
     * 添加新类别
     */
    public final void addClass(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.Bitmap> samples) {
    }
    
    /**
     * 删除类别
     */
    public final void deleteClass(@org.jetbrains.annotations.NotNull
    java.lang.String className) {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 从样本收集界面添加类别
     */
    public final void addClassFromSamples(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.Bitmap> samples) {
    }
    
    /**
     * 添加样本到现有类别
     */
    public final void addSamplesToClass(@org.jetbrains.annotations.NotNull
    java.lang.String className, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.Bitmap> samples) {
    }
    
    /**
     * 重置训练状态
     */
    public final void resetTrainingState() {
    }
    
    /**
     * 获取类别统计信息
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.Map<java.lang.String, java.lang.Object> getClassStatistics() {
        return null;
    }
    
    /**
     * 刷新学习类别列表
     */
    public final void refreshLearnedClasses() {
    }
}