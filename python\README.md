# 少样本学习训练与推理框架

基于PyTorch的少样本学习框架，提供完整的训练、评估和推理功能。

## 特性

- 🚀 **多算法支持**: 支持Prototypical Networks、SimpleShot、FEAT等主流算法
- 🔧 **模块化设计**: 清晰的模块分离，易于扩展和定制
- 📊 **完整流程**: 从数据加载到模型训练再到推理部署的完整流程
- ⚡ **高效推理**: 优化的推理引擎，支持实时和批量推理
- 📈 **丰富监控**: 详细的训练监控和评估指标
- 🛠️ **易于使用**: 简洁的API设计和丰富的示例代码

## 安装依赖

```bash
pip install torch torchvision tqdm pillow pandas numpy
```

## 快速开始

### 1. 准备数据

将您的数据按以下结构组织：

```
data/
├── class_0/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── class_1/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── ...
```

### 2. 基础训练示例

```python
from datasets import LocalDataset
from methods import PrototypicalNetworks, get_backbone
from core import TaskSampler
from training import EpisodicTrainer, TrainingConfig
from torch.utils.data import DataLoader

# 创建数据集
dataset = LocalDataset("path/to/your/data", image_size=84, training=True)

# 创建任务采样器
sampler = TaskSampler(
    dataset=dataset,
    n_way=5,
    n_shot=5, 
    n_query=15,
    n_tasks=1000
)

# 创建数据加载器
loader = DataLoader(
    dataset,
    batch_sampler=sampler,
    collate_fn=sampler.episodic_collate_fn
)

# 创建模型
backbone = get_backbone("resnet12", feature_dim=512)
model = PrototypicalNetworks(backbone=backbone)

# 配置训练
config = TrainingConfig(
    num_epochs=100,
    learning_rate=0.001,
    n_way=5,
    n_shot=5,
    n_query=15
)

# 训练
trainer = EpisodicTrainer(model, loader, config=config)
results = trainer.train()
```

### 3. 推理示例

```python
from inference import InferenceEngine

# 创建推理引擎
engine = InferenceEngine(model)

# 设置支持集
engine.set_support_set(support_images, support_labels, class_names)

# 单样本推理
result = engine.predict_single(query_image, return_confidence=True)
print(f"预测类别: {result['predicted_class']}")
print(f"置信度: {result['confidence']:.4f}")

# 批量推理
results = engine.predict_batch(query_images)
```

## 项目结构

```
python/
├── core/                   # 核心模块
│   ├── base_classifier.py  # 少样本分类器基类
│   ├── task_sampler.py     # 任务采样器
│   └── utils.py           # 工具函数
├── datasets/              # 数据集模块
│   ├── local_dataset.py   # 本地数据集
│   ├── transforms.py      # 图像变换
│   └── base_dataset.py    # 数据集基类
├── methods/               # 算法模块
│   ├── prototypical_networks.py  # 原型网络
│   ├── simple_shot.py     # SimpleShot算法
│   ├── backbones.py       # 骨干网络
│   └── ...
├── training/              # 训练模块
│   ├── episodic_trainer.py # Episodic训练器
│   ├── trainer_utils.py   # 训练工具
│   └── ...
├── inference/             # 推理模块
│   ├── inference_engine.py # 推理引擎
│   └── ...
├── examples/              # 示例代码
│   └── train_example.py   # 训练示例
└── configs/               # 配置文件
    └── default_config.json
```

## 支持的算法

- **Prototypical Networks**: 基于原型的分类方法
- **SimpleShot**: 基于余弦距离的简单方法
- **FEAT**: 特征增强变换
- **Matching Networks**: 注意力机制匹配
- **Relation Networks**: 关系网络
- **Fine-Tune**: 微调方法

## 支持的骨干网络

- **ResNet12**: 专为少样本学习设计的轻量级ResNet
- **ConvNet**: 简单的卷积网络
- **PretrainedBackbone**: 预训练模型包装器（ResNet18/50, EfficientNet等）

## 配置文件

使用JSON配置文件可以方便地管理训练参数：

```json
{
  "model": {
    "name": "prototypical_networks",
    "backbone": {
      "name": "resnet12",
      "feature_dim": 512
    }
  },
  "training": {
    "num_epochs": 100,
    "learning_rate": 0.001,
    "n_way": 5,
    "n_shot": 5,
    "n_query": 15
  }
}
```

## 高级功能

### 自定义算法

继承`FewShotClassifier`基类来实现自定义算法：

```python
from core import FewShotClassifier

class CustomAlgorithm(FewShotClassifier):
    def forward(self, query_images):
        # 实现自定义前向传播逻辑
        query_features = self.compute_features(query_images)
        scores = self.custom_distance_function(query_features)
        return self.softmax_if_specified(scores)
    
    @staticmethod
    def is_transductive():
        return False  # 或True，取决于算法类型
```

### 自定义数据集

继承`FewShotDataset`基类来支持新的数据格式：

```python
from datasets import FewShotDataset

class CustomDataset(FewShotDataset):
    def __init__(self, data_path):
        # 初始化数据集
        pass
    
    def __getitem__(self, index):
        # 返回(image, label)元组
        pass
    
    def __len__(self):
        # 返回数据集大小
        pass
    
    def get_labels(self):
        # 返回所有样本的标签列表
        pass
```

### 训练监控

框架提供了丰富的训练监控功能：

- 实时损失和准确率跟踪
- 学习率调度
- 早停机制
- 模型检查点保存
- TensorBoard支持（可选）

### 推理优化

推理引擎提供了多种优化选项：

- 支持集缓存
- 批量推理
- GPU加速
- 置信度计算
- 性能统计

## 性能基准

在标准数据集上的性能表现：

| 算法 | miniImageNet 5-way 1-shot | miniImageNet 5-way 5-shot |
|------|---------------------------|---------------------------|
| Prototypical Networks | 63.6% | 80.4% |
| SimpleShot | 63.6% | 80.5% |
| FEAT | 64.7% | 80.1% |

## 常见问题

### Q: 如何处理不平衡数据集？
A: 使用`BalancedTaskSampler`来确保每个类别被采样的次数大致相等。

### Q: 如何加速训练？
A: 可以尝试以下方法：
- 使用更大的batch size
- 启用混合精度训练
- 使用预训练的骨干网络
- 将小数据集加载到内存中

### Q: 如何调试模型？
A: 框架提供了详细的日志和可视化工具：
- 查看训练日志了解损失变化
- 使用推理引擎的统计功能分析性能
- 检查模型的原型和特征分布

## 贡献

欢迎提交Issue和Pull Request来改进这个框架！

## 许可证

MIT License
