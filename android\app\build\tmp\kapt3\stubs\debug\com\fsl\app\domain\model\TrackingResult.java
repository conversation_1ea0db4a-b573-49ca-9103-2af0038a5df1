package com.fsl.app.domain.model;

import java.lang.System;

/**
 * 跟踪结果
 *
 * @param trackId 跟踪ID
 * @param className 分类名称
 * @param confidence 置信度 (0.0-1.0)
 * @param x 边界框左上角X坐标 (归一化坐标 0.0-1.0)
 * @param y 边界框左上角Y坐标 (归一化坐标 0.0-1.0)
 * @param width 边界框宽度 (归一化坐标 0.0-1.0)
 * @param height 边界框高度 (归一化坐标 0.0-1.0)
 * @param timestamp 时间戳 (毫秒)
 */
@kotlinx.parcelize.Parcelize
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u00002\u00020\u0001:\u0001;BE\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\u0007\u0012\u0006\u0010\u000b\u001a\u00020\u0007\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u0000J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003J\t\u0010 \u001a\u00020\u0007H\u00c6\u0003J\t\u0010!\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0007H\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\rH\u00c6\u0003JY\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u00072\b\b\u0002\u0010\u000b\u001a\u00020\u00072\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\t\u0010&\u001a\u00020\u0003H\u00d6\u0001J\u0013\u0010\'\u001a\u00020(2\b\u0010\u001c\u001a\u0004\u0018\u00010)H\u00d6\u0003J\u0006\u0010*\u001a\u00020\u0007J\u0006\u0010+\u001a\u00020\u0007J\u0006\u0010,\u001a\u00020\u0007J\u0006\u0010-\u001a\u00020.J\t\u0010/\u001a\u00020\u0003H\u00d6\u0001J\u0006\u00100\u001a\u00020(J\u0016\u00101\u001a\u0002022\u0006\u00103\u001a\u00020\u00032\u0006\u00104\u001a\u00020\u0003J\b\u00105\u001a\u00020\u0005H\u0016J\u0019\u00106\u001a\u0002072\u0006\u00108\u001a\u0002092\u0006\u0010:\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\n\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012\u00a8\u0006<"}, d2 = {"Lcom/fsl/app/domain/model/TrackingResult;", "Landroid/os/Parcelable;", "trackId", "", "className", "", "confidence", "", "x", "y", "width", "height", "timestamp", "", "(ILjava/lang/String;FFFFFJ)V", "getClassName", "()Ljava/lang/String;", "getConfidence", "()F", "getHeight", "getTimestamp", "()J", "getTrackId", "()I", "getWidth", "getX", "getY", "calculateIoU", "other", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "describeContents", "equals", "", "", "getArea", "getCenterX", "getCenterY", "getConfidenceLevel", "Lcom/fsl/app/domain/model/TrackingResult$ConfidenceLevel;", "hashCode", "isValid", "toPixelCoordinates", "Lcom/fsl/app/domain/model/PixelBoundingBox;", "imageWidth", "imageHeight", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "ConfidenceLevel", "app_debug"})
public final class TrackingResult implements android.os.Parcelable {
    private final int trackId = 0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String className = null;
    private final float confidence = 0.0F;
    private final float x = 0.0F;
    private final float y = 0.0F;
    private final float width = 0.0F;
    private final float height = 0.0F;
    private final long timestamp = 0L;
    public static final android.os.Parcelable.Creator<com.fsl.app.domain.model.TrackingResult> CREATOR = null;
    
    /**
     * 跟踪结果
     *
     * @param trackId 跟踪ID
     * @param className 分类名称
     * @param confidence 置信度 (0.0-1.0)
     * @param x 边界框左上角X坐标 (归一化坐标 0.0-1.0)
     * @param y 边界框左上角Y坐标 (归一化坐标 0.0-1.0)
     * @param width 边界框宽度 (归一化坐标 0.0-1.0)
     * @param height 边界框高度 (归一化坐标 0.0-1.0)
     * @param timestamp 时间戳 (毫秒)
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.TrackingResult copy(int trackId, @org.jetbrains.annotations.NotNull
    java.lang.String className, float confidence, float x, float y, float width, float height, long timestamp) {
        return null;
    }
    
    /**
     * 跟踪结果
     *
     * @param trackId 跟踪ID
     * @param className 分类名称
     * @param confidence 置信度 (0.0-1.0)
     * @param x 边界框左上角X坐标 (归一化坐标 0.0-1.0)
     * @param y 边界框左上角Y坐标 (归一化坐标 0.0-1.0)
     * @param width 边界框宽度 (归一化坐标 0.0-1.0)
     * @param height 边界框高度 (归一化坐标 0.0-1.0)
     * @param timestamp 时间戳 (毫秒)
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 跟踪结果
     *
     * @param trackId 跟踪ID
     * @param className 分类名称
     * @param confidence 置信度 (0.0-1.0)
     * @param x 边界框左上角X坐标 (归一化坐标 0.0-1.0)
     * @param y 边界框左上角Y坐标 (归一化坐标 0.0-1.0)
     * @param width 边界框宽度 (归一化坐标 0.0-1.0)
     * @param height 边界框高度 (归一化坐标 0.0-1.0)
     * @param timestamp 时间戳 (毫秒)
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    public TrackingResult(int trackId, @org.jetbrains.annotations.NotNull
    java.lang.String className, float confidence, float x, float y, float width, float height, long timestamp) {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int getTrackId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getClassName() {
        return null;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float getX() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final float getY() {
        return 0.0F;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    public final float getWidth() {
        return 0.0F;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float getHeight() {
        return 0.0F;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    /**
     * 获取边界框中心点坐标
     */
    public final float getCenterX() {
        return 0.0F;
    }
    
    /**
     * 获取边界框中心点坐标
     */
    public final float getCenterY() {
        return 0.0F;
    }
    
    /**
     * 获取边界框面积
     */
    public final float getArea() {
        return 0.0F;
    }
    
    /**
     * 转换为像素坐标
     *
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 像素坐标的边界框
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.PixelBoundingBox toPixelCoordinates(int imageWidth, int imageHeight) {
        return null;
    }
    
    /**
     * 检查是否为有效的跟踪结果
     */
    public final boolean isValid() {
        return false;
    }
    
    /**
     * 计算与另一个跟踪结果的IoU (Intersection over Union)
     */
    public final float calculateIoU(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.TrackingResult other) {
        return 0.0F;
    }
    
    /**
     * 获取置信度等级
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.model.TrackingResult.ConfidenceLevel getConfidenceLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 3)
    public static final class Creator implements android.os.Parcelable.Creator<com.fsl.app.domain.model.TrackingResult> {
        
        public Creator() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.TrackingResult createFromParcel(@org.jetbrains.annotations.NotNull
        android.os.Parcel in) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public final com.fsl.app.domain.model.TrackingResult[] newArray(int size) {
            return null;
        }
    }
    
    /**
     * 置信度等级枚举
     */
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/fsl/app/domain/model/TrackingResult$ConfidenceLevel;", "", "(Ljava/lang/String;I)V", "VERY_LOW", "LOW", "MEDIUM", "HIGH", "app_debug"})
    public static enum ConfidenceLevel {
        /*public static final*/ VERY_LOW /* = new VERY_LOW() */,
        /*public static final*/ LOW /* = new LOW() */,
        /*public static final*/ MEDIUM /* = new MEDIUM() */,
        /*public static final*/ HIGH /* = new HIGH() */;
        
        ConfidenceLevel() {
        }
    }
}