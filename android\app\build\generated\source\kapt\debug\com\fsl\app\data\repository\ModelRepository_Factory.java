// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.repository;

import com.fsl.app.data.database.AppDatabase;
import com.fsl.app.data.utils.FileManager;
import com.fsl.app.domain.repository.IInferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class ModelRepository_Factory implements Factory<ModelRepository> {
  private final Provider<AppDatabase> databaseProvider;

  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  private final Provider<FileManager> fileManagerProvider;

  public ModelRepository_Factory(Provider<AppDatabase> databaseProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<FileManager> fileManagerProvider) {
    this.databaseProvider = databaseProvider;
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
    this.fileManagerProvider = fileManagerProvider;
  }

  @Override
  public ModelRepository get() {
    return newInstance(databaseProvider.get(), inferenceRepositoryProvider.get(), fileManagerProvider.get());
  }

  public static ModelRepository_Factory create(Provider<AppDatabase> databaseProvider,
      Provider<IInferenceRepository> inferenceRepositoryProvider,
      Provider<FileManager> fileManagerProvider) {
    return new ModelRepository_Factory(databaseProvider, inferenceRepositoryProvider, fileManagerProvider);
  }

  public static ModelRepository newInstance(AppDatabase database,
      IInferenceRepository inferenceRepository, FileManager fileManager) {
    return new ModelRepository(database, inferenceRepository, fileManager);
  }
}
