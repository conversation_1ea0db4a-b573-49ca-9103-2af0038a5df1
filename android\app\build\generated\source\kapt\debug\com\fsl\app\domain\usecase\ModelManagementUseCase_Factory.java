// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.domain.usecase;

import com.fsl.app.domain.repository.IInferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class ModelManagementUseCase_Factory implements Factory<ModelManagementUseCase> {
  private final Provider<IInferenceRepository> inferenceRepositoryProvider;

  public ModelManagementUseCase_Factory(
      Provider<IInferenceRepository> inferenceRepositoryProvider) {
    this.inferenceRepositoryProvider = inferenceRepositoryProvider;
  }

  @Override
  public ModelManagementUseCase get() {
    return newInstance(inferenceRepositoryProvider.get());
  }

  public static ModelManagementUseCase_Factory create(
      Provider<IInferenceRepository> inferenceRepositoryProvider) {
    return new ModelManagementUseCase_Factory(inferenceRepositoryProvider);
  }

  public static ModelManagementUseCase newInstance(IInferenceRepository inferenceRepository) {
    return new ModelManagementUseCase(inferenceRepository);
  }
}
