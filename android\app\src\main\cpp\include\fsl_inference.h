/**
 * FSL推理引擎头文件
 *
 * 少样本学习推理引擎的C++实现，集成Android NNAPI
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef FSL_INFERENCE_H
#define FSL_INFERENCE_H

#include <vector>
#include <string>
#include <map>
#include <memory>

namespace fsl {

// 前向声明
class PrototypicalNetwork;
class FeatureExtractor;
class ObjectTracker;
class NNAPIEngine;

/**
 * 分类结果结构体
 */
struct ClassificationResult {
    std::string className;
    float confidence;
    std::map<std::string, float> allScores;
    long inferenceTime;

    ClassificationResult() : confidence(0.0f), inferenceTime(0L) {}

    ClassificationResult(const std::string& name, float conf,
                        const std::map<std::string, float>& scores, long time)
        : className(name), confidence(conf), allScores(scores), inferenceTime(time) {}
};

/**
 * 跟踪结果结构体
 */
struct TrackingResult {
    int trackId;
    std::string className;
    float confidence;
    float x, y, width, height; // 归一化坐标 (0-1)
    long timestamp;

    TrackingResult() : trackId(-1), confidence(0.0f),
                      x(0), y(0), width(0), height(0), timestamp(0) {}
};

/**
 * 特征向量类型定义
 */
using FeatureVector = std::vector<float>;
using FeatureMatrix = std::vector<FeatureVector>;

/**
 * FSL推理引擎类
 */
class FSLInferenceEngine {
public:
    FSLInferenceEngine();
    ~FSLInferenceEngine();

    /**
     * 初始化推理引擎
     */
    bool initialize();

    /**
     * 分类图像
     * @param imageData 图像数据 (RGB, 224x224x3)
     * @param width 图像宽度
     * @param height 图像高度
     * @return 分类结果
     */
    ClassificationResult classify(const float* imageData, int width, int height);

    /**
     * 提取特征
     * @param imageData 图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 特征向量
     */
    FeatureVector extractFeatures(const float* imageData, int width, int height);

    /**
     * 添加新类别
     * @param className 类别名称
     * @param features 特征向量列表
     * @return 是否成功
     */
    bool addClass(const std::string& className, const FeatureMatrix& features);

    /**
     * 更新类别原型
     * @param className 类别名称
     * @param features 新的特征向量列表
     * @return 是否成功
     */
    bool updateClass(const std::string& className, const FeatureMatrix& features);

    /**
     * 删除类别
     * @param className 类别名称
     * @return 是否成功
     */
    bool removeClass(const std::string& className);

    /**
     * 获取所有类别名称
     * @return 类别名称列表
     */
    std::vector<std::string> getClassNames() const;

    /**
     * 保存模型
     * @param filePath 文件路径
     * @return 是否成功
     */
    bool saveModel(const std::string& filePath);

    /**
     * 加载模型
     * @param filePath 文件路径
     * @return 是否成功
     */
    bool loadModel(const std::string& filePath);

    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }

    /**
     * 实时跟踪和分类
     * @param imageData 图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 跟踪结果列表
     */
    std::vector<TrackingResult> trackAndClassify(const float* imageData, int width, int height);

    /**
     * 清除所有跟踪状态
     */
    void clearTracking();

    /**
     * 设置跟踪参数
     * @param iouThreshold IoU阈值
     * @param maxAge 最大跟踪年龄
     * @param minHits 最小命中次数
     */
    void setTrackingParams(float iouThreshold, int maxAge, int minHits);

    /**
     * 获取模型信息
     * @return 模型信息映射
     */
    std::map<std::string, std::string> getModelInfo() const;

private:
    // 核心组件
    bool m_initialized;
    std::unique_ptr<PrototypicalNetwork> m_prototypicalNetwork;
    std::unique_ptr<FeatureExtractor> m_featureExtractor;
    std::unique_ptr<NNAPIEngine> m_nnapiEngine;
    std::unique_ptr<ObjectTracker> m_objectTracker;

    // 配置参数
    float m_confidenceThreshold;

    /**
     * 计算原型
     * @param features 特征向量列表
     * @return 原型向量
     */
    FeatureVector computePrototype(const FeatureMatrix& features);

    /**
     * 计算余弦相似度
     * @param a 向量a
     * @param b 向量b
     * @return 相似度
     */
    float cosineSimilarity(const FeatureVector& a, const FeatureVector& b);

    /**
     * L2归一化
     * @param vector 输入向量
     * @return 归一化后的向量
     */
    FeatureVector normalizeL2(const FeatureVector& vector);

    /**
     * 初始化NNAPI引擎
     */
    bool initializeNNAPI();

    /**
     * 初始化对象跟踪器
     */
    bool initializeTracker();

    /**
     * 初始化默认类别
     */
    void initializeDefaultClasses();
};

} // namespace fsl

#endif // FSL_INFERENCE_H
