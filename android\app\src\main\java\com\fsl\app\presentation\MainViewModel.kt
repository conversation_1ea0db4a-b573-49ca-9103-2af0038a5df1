/**
 * 主ViewModel
 * 
 * 管理应用的全局状态和导航逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fsl.app.domain.usecase.ModelManagementUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主界面UI状态
 */
data class MainUiState(
    val isLoading: Boolean = false,
    val isModelInitialized: Boolean = false,
    val error: String? = null,
    val currentTab: String = "camera"
)

@HiltViewModel
class MainViewModel @Inject constructor(
    private val modelManagementUseCase: ModelManagementUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    init {
        initializeApp()
    }
    
    /**
     * 初始化应用
     */
    private fun initializeApp() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 检查模型状态
                val modelInfo = modelManagementUseCase.getModelInfo()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isModelInitialized = modelInfo.isSuccess,
                    error = if (modelInfo.isFailure) modelInfo.exceptionOrNull()?.message else null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 切换标签页
     */
    fun switchTab(tab: String) {
        _uiState.value = _uiState.value.copy(currentTab = tab)
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 重新初始化应用
     */
    fun retryInitialization() {
        initializeApp()
    }
}
