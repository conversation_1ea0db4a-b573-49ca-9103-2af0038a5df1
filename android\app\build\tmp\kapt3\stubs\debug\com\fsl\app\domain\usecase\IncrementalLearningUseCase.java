package com.fsl.app.domain.usecase;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ8\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J8\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u0012J*\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0016\u0010\u0017J\u0012\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001a0\u000f0\u0019J\"\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\nH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u0011\u0010\u001f\u001a\u00020 H\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001eJ\u000e\u0010!\u001a\u00020\"2\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010#\u001a\u00020\"2\u0006\u0010$\u001a\u00020%R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006&"}, d2 = {"Lcom/fsl/app/domain/usecase/IncrementalLearningUseCase;", "", "modelRepository", "Lcom/fsl/app/domain/repository/IModelRepository;", "inferenceRepository", "Lcom/fsl/app/domain/repository/IInferenceRepository;", "imageProcessingRepository", "Lcom/fsl/app/domain/repository/IImageProcessingRepository;", "(Lcom/fsl/app/domain/repository/IModelRepository;Lcom/fsl/app/domain/repository/IInferenceRepository;Lcom/fsl/app/domain/repository/IImageProcessingRepository;)V", "addClass", "Lkotlin/Result;", "", "className", "", "samples", "", "Landroid/graphics/Bitmap;", "addClass-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addSamples", "addSamples-0E7RQCE", "deleteClass", "deleteClass-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLearnedClasses", "Lkotlinx/coroutines/flow/Flow;", "Lcom/fsl/app/domain/model/LearnedClass;", "getLearningStats", "Lcom/fsl/app/domain/usecase/LearningStats;", "getLearningStats-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getModelSize", "", "validateClassName", "Lcom/fsl/app/domain/usecase/ValidationResult;", "validateSampleCount", "count", "", "app_debug"})
@javax.inject.Singleton
public final class IncrementalLearningUseCase {
    private final com.fsl.app.domain.repository.IModelRepository modelRepository = null;
    private final com.fsl.app.domain.repository.IInferenceRepository inferenceRepository = null;
    private final com.fsl.app.domain.repository.IImageProcessingRepository imageProcessingRepository = null;
    
    @javax.inject.Inject
    public IncrementalLearningUseCase(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IModelRepository modelRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IInferenceRepository inferenceRepository, @org.jetbrains.annotations.NotNull
    com.fsl.app.domain.repository.IImageProcessingRepository imageProcessingRepository) {
        super();
    }
    
    /**
     * 获取已学习的类别
     */
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.fsl.app.domain.model.LearnedClass>> getLearnedClasses() {
        return null;
    }
    
    /**
     * 验证类别名称
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.usecase.ValidationResult validateClassName(@org.jetbrains.annotations.NotNull
    java.lang.String className) {
        return null;
    }
    
    /**
     * 验证样本数量
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.domain.usecase.ValidationResult validateSampleCount(int count) {
        return null;
    }
    
    /**
     * 获取模型大小（模拟实现）
     */
    private final java.lang.Object getModelSize(kotlin.coroutines.Continuation<? super java.lang.Long> continuation) {
        return null;
    }
}