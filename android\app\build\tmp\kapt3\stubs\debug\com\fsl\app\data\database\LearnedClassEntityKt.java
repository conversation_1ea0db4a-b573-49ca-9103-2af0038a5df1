package com.fsl.app.data.database;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\u0014\u0010\u0003\u001a\u00020\u0002*\u00020\u00012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a8\u0006\u0006"}, d2 = {"toDomainModel", "Lcom/fsl/app/domain/model/LearnedClass;", "Lcom/fsl/app/data/database/LearnedClassEntity;", "toEntity", "id", "", "app_debug"})
public final class LearnedClassEntityKt {
    
    /**
     * 扩展函数：将实体转换为领域模型
     */
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.domain.model.LearnedClass toDomainModel(@org.jetbrains.annotations.NotNull
    com.fsl.app.data.database.LearnedClassEntity $this$toDomainModel) {
        return null;
    }
    
    /**
     * 扩展函数：将领域模型转换为实体
     */
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.database.LearnedClassEntity toEntity(@org.jetbrains.annotations.NotNull
    com.fsl.app.domain.model.LearnedClass $this$toEntity, @org.jetbrains.annotations.NotNull
    java.lang.String id) {
        return null;
    }
}