package com.fsl.app.ui.components;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\u0006"}, d2 = {"BottomNavigationBar", "", "currentRoute", "", "onNavigate", "Lkotlin/Function1;", "app_debug"})
public final class BottomNavigationBarKt {
    
    /**
     * 底部导航栏
     */
    @androidx.compose.runtime.Composable
    public static final void BottomNavigationBar(@org.jetbrains.annotations.Nullable
    java.lang.String currentRoute, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigate) {
    }
}