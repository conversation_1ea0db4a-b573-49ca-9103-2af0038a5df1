#!/usr/bin/env python3
"""
创建真正的PyTorch Mobile模型
用于Android FSL应用的真实推理
"""

import torch
import torch.nn as nn
import torchvision.models as models
from torch.utils.mobile_optimizer import optimize_for_mobile
import os
import sys

def create_mobilenet_v3_model():
    """
    创建MobileNetV3-Small模型用于特征提取
    """
    print("创建MobileNetV3-Small模型...")
    
    # 加载预训练的MobileNetV3-Small
    model = models.mobilenet_v3_small(pretrained=True)
    
    # 移除分类器，只保留特征提取部分
    # MobileNetV3的特征提取器输出576维特征
    feature_extractor = nn.Sequential(
        model.features,
        model.avgpool,
        nn.Flatten()
    )
    
    # 设置为评估模式
    feature_extractor.eval()
    
    print(f"模型结构: {feature_extractor}")
    
    return feature_extractor

def test_model_output(model):
    """
    测试模型输出
    """
    print("测试模型输出...")
    
    # 创建测试输入 (batch_size=1, channels=3, height=224, width=224)
    test_input = torch.randn(1, 3, 224, 224)
    
    with torch.no_grad():
        output = model(test_input)
        print(f"输入形状: {test_input.shape}")
        print(f"输出形状: {output.shape}")
        print(f"输出特征维度: {output.shape[1]}")
        
    return output.shape[1]

def convert_to_mobile(model, output_path):
    """
    转换模型为PyTorch Mobile格式
    """
    print("转换模型为PyTorch Mobile格式...")
    
    # 创建示例输入
    example_input = torch.randn(1, 3, 224, 224)
    
    # 追踪模型
    print("追踪模型...")
    traced_model = torch.jit.trace(model, example_input)
    
    # 优化模型以适配移动端
    print("优化模型用于移动设备...")
    optimized_model = optimize_for_mobile(traced_model)
    
    # 保存模型
    print(f"保存模型到: {output_path}")
    optimized_model._save_for_lite_interpreter(output_path)
    
    # 获取模型大小
    model_size = os.path.getsize(output_path)
    print(f"模型大小: {model_size / (1024*1024):.2f} MB")
    
    return output_path

def verify_mobile_model(model_path):
    """
    验证Mobile模型
    """
    print(f"验证Mobile模型: {model_path}")
    
    try:
        # 加载Mobile模型
        mobile_model = torch.jit.load(model_path)
        mobile_model.eval()
        
        # 测试推理
        test_input = torch.randn(1, 3, 224, 224)
        with torch.no_grad():
            output = mobile_model(test_input)
            print(f"Mobile模型输出形状: {output.shape}")
            print(f"Mobile模型推理成功!")
            
        return True
    except Exception as e:
        print(f"Mobile模型验证失败: {e}")
        return False

def create_fsl_prototype_model():
    """
    创建FSL原型网络模型
    """
    print("创建FSL原型网络模型...")
    
    class PrototypicalNetwork(nn.Module):
        def __init__(self, feature_dim=576, num_classes=10):
            super().__init__()
            self.feature_dim = feature_dim
            self.num_classes = num_classes
            
            # 特征提取器 (MobileNetV3-Small)
            backbone = models.mobilenet_v3_small(pretrained=True)
            self.feature_extractor = nn.Sequential(
                backbone.features,
                backbone.avgpool,
                nn.Flatten()
            )
            
            # 原型存储 (可学习参数)
            self.prototypes = nn.Parameter(
                torch.randn(num_classes, feature_dim),
                requires_grad=True
            )
            
        def forward(self, x):
            # 提取特征
            features = self.feature_extractor(x)
            
            # 计算到原型的距离
            distances = torch.cdist(features.unsqueeze(0), self.prototypes.unsqueeze(0)).squeeze(0)
            
            # 转换为相似度分数 (距离越小，分数越高)
            scores = -distances
            
            return scores
    
    model = PrototypicalNetwork()
    model.eval()
    
    return model

def main():
    """
    主函数
    """
    print("=== 创建真正的PyTorch Mobile模型 ===")
    
    # 确保输出目录存在
    assets_dir = "../app/src/main/assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    try:
        # 1. 创建特征提取模型
        print("\n1. 创建特征提取模型...")
        feature_model = create_mobilenet_v3_model()
        
        # 2. 测试模型
        print("\n2. 测试模型...")
        feature_dim = test_model_output(feature_model)
        
        # 3. 转换为Mobile格式
        print("\n3. 转换为Mobile格式...")
        mobile_model_path = os.path.join(assets_dir, "mobilenet_v3_small.ptl")
        convert_to_mobile(feature_model, mobile_model_path)
        
        # 4. 验证Mobile模型
        print("\n4. 验证Mobile模型...")
        if verify_mobile_model(mobile_model_path):
            print("✅ Mobile模型创建成功!")
        else:
            print("❌ Mobile模型验证失败!")
            return False
        
        # 5. 创建FSL原型网络模型
        print("\n5. 创建FSL原型网络模型...")
        fsl_model = create_fsl_prototype_model()
        
        # 6. 转换FSL模型为Mobile格式
        print("\n6. 转换FSL模型...")
        fsl_model_path = os.path.join(assets_dir, "fsl_prototypical_network.ptl")
        convert_to_mobile(fsl_model, fsl_model_path)
        
        # 7. 验证FSL模型
        print("\n7. 验证FSL模型...")
        if verify_mobile_model(fsl_model_path):
            print("✅ FSL模型创建成功!")
        else:
            print("❌ FSL模型验证失败!")
            return False
        
        print("\n=== 模型创建完成 ===")
        print(f"特征提取模型: {mobile_model_path}")
        print(f"FSL原型模型: {fsl_model_path}")
        print(f"特征维度: {feature_dim}")
        
        # 8. 创建模型信息文件
        info_content = f"""# PyTorch Mobile模型信息

## 特征提取模型
- 文件: mobilenet_v3_small.ptl
- 架构: MobileNetV3-Small
- 输入: (1, 3, 224, 224)
- 输出: (1, {feature_dim})
- 用途: 图像特征提取

## FSL原型网络模型  
- 文件: fsl_prototypical_network.ptl
- 架构: MobileNetV3-Small + 原型层
- 输入: (1, 3, 224, 224)
- 输出: (1, 10) # 10个类别的分数
- 用途: Few-Shot Learning分类

## 使用说明
1. 将模型文件放在 android/app/src/main/assets/ 目录下
2. Android应用会自动加载这些模型进行推理
3. 特征提取模型用于提取图像特征
4. FSL模型用于基于原型的分类
"""
        
        info_path = os.path.join(assets_dir, "model_info.md")
        with open(info_path, 'w', encoding='utf-8') as f:
            f.write(info_content)
        
        print(f"模型信息已保存到: {info_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
