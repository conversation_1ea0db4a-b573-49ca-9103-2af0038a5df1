/**
 * 相机界面
 *
 * 提供实时图像分类功能
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.camera

import android.Manifest
import androidx.camera.core.ImageProxy
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CameraAlt
import androidx.compose.material.icons.filled.FlipCameraAndroid
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.School
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.ui.components.CameraPreview
import com.fsl.app.ui.components.BoundingBoxOverlay
import com.fsl.app.ui.components.ClassificationOverlay
import com.fsl.app.ui.components.PermissionHandler
import com.fsl.app.presentation.camera.CameraViewModel
import com.fsl.app.presentation.camera.CameraState

@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
fun CameraScreen(
    onNavigateToLearning: () -> Unit = {},
    viewModel: CameraViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val cameraState by viewModel.cameraState.collectAsState()
    val classificationResult by viewModel.classificationResult.collectAsState()
    val trackingResults by viewModel.trackingResults.collectAsState()
    val isRealTimeMode by viewModel.isRealTimeMode.collectAsState()
    val isFrontCamera by viewModel.isFrontCamera.collectAsState()

    // 权限处理
    PermissionHandler(
        permission = Manifest.permission.CAMERA,
        onPermissionGranted = {
            // 权限已授予，显示相机界面
            CameraContent(
                cameraState = cameraState,
                classificationResult = classificationResult,
                trackingResults = trackingResults,
                isRealTimeMode = isRealTimeMode,
                isFrontCamera = isFrontCamera,
                onImageCaptured = viewModel::classifyImage,
                onCaptureClick = viewModel::captureImage,
                onSwitchCamera = viewModel::switchCamera,
                onToggleRealTimeMode = viewModel::toggleRealTimeMode,
                onNavigateToLearning = onNavigateToLearning,
                onCreateTestImages = viewModel::createTestImages
            )
        },
        onPermissionDenied = {
            // 权限被拒绝，显示权限请求界面
            PermissionDeniedContent()
        }
    )
}

@Composable
private fun CameraContent(
    cameraState: CameraState,
    classificationResult: com.fsl.app.domain.model.ClassificationResult?,
    trackingResults: List<com.fsl.app.domain.model.TrackingResult>,
    isRealTimeMode: Boolean,
    isFrontCamera: Boolean,
    onImageCaptured: (ImageProxy?) -> Unit,
    onCaptureClick: () -> Unit,
    onSwitchCamera: () -> Unit,
    onToggleRealTimeMode: () -> Unit,
    onNavigateToLearning: () -> Unit,
    onCreateTestImages: () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 相机预览
        CameraPreview(
            modifier = Modifier.fillMaxSize(),
            onImageCaptured = if (isRealTimeMode) onImageCaptured else null,
            onManualCapture = if (!isRealTimeMode) onImageCaptured else null,
            useMockCamera = false, // 优先使用真实相机，失败时自动回退到模拟相机
            isFrontCamera = isFrontCamera
        )

        // 边界框覆盖 - 使用真实跟踪结果
        BoundingBoxOverlay(
            modifier = Modifier.fillMaxSize(),
            trackingResults = if (isRealTimeMode) trackingResults else emptyList(),
            isRealTimeMode = isRealTimeMode,
            currentClassification = if (!isRealTimeMode) classificationResult else null
        )

        // 顶部控制栏
        TopControlBar(
            isRealTimeMode = isRealTimeMode,
            onToggleRealTimeMode = onToggleRealTimeMode,
            onNavigateToLearning = onNavigateToLearning,
            onCreateTestImages = onCreateTestImages,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(16.dp)
        )

        // 分类结果现在由BoundingBoxOverlay处理

        // 底部控制按钮
        BottomControlBar(
            cameraState = cameraState,
            isRealTimeMode = isRealTimeMode,
            onCaptureClick = onCaptureClick,
            onSwitchCamera = onSwitchCamera,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(32.dp)
        )

        // 加载指示器 - 只在手动模式下显示，避免实时模式闪烁
        if (cameraState == CameraState.Processing && !isRealTimeMode) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(24.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = MaterialTheme.colorScheme.primary,
                            strokeWidth = 3.dp
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                        Text(
                            text = "正在分析...",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        // 错误提示 - 只在手动模式下显示，避免实时模式闪烁
        if (cameraState is CameraState.Error && !isRealTimeMode) {
            Snackbar(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(16.dp),
                action = {
                    TextButton(onClick = { /* 重试逻辑 */ }) {
                        Text("重试")
                    }
                }
            ) {
                Text(cameraState.message)
            }
        }
    }
}

@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
private fun TopControlBar(
    isRealTimeMode: Boolean,
    onToggleRealTimeMode: () -> Unit,
    onNavigateToLearning: () -> Unit,
    onCreateTestImages: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 实时模式切换
        FilterChip(
            onClick = onToggleRealTimeMode,
            label = { Text(if (isRealTimeMode) "实时模式" else "手动模式") },
            selected = isRealTimeMode
        )

        // 中间按钮组
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 创建测试图片按钮
            FloatingActionButton(
                onClick = onCreateTestImages,
                modifier = Modifier.size(40.dp),
                containerColor = MaterialTheme.colorScheme.tertiary
            ) {
                Icon(
                    imageVector = Icons.Default.Image,
                    contentDescription = "创建测试图片",
                    modifier = Modifier.size(20.dp)
                )
            }

            // 学习按钮
            FloatingActionButton(
                onClick = onNavigateToLearning,
                modifier = Modifier.size(48.dp),
                containerColor = MaterialTheme.colorScheme.secondary
            ) {
                Icon(
                    imageVector = Icons.Default.School,
                    contentDescription = "学习模式"
                )
            }
        }
    }
}

@Composable
private fun BottomControlBar(
    cameraState: CameraState,
    isRealTimeMode: Boolean,
    onCaptureClick: () -> Unit,
    onSwitchCamera: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 切换相机按钮
        IconButton(
            onClick = onSwitchCamera,
            modifier = Modifier.size(56.dp)
        ) {
            Icon(
                imageVector = Icons.Default.FlipCameraAndroid,
                contentDescription = "切换相机",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
        }

        // 拍照按钮（仅在手动模式下显示）
        if (!isRealTimeMode) {
            FloatingActionButton(
                onClick = onCaptureClick,
                modifier = Modifier.size(72.dp),
                shape = CircleShape,
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.CameraAlt,
                    contentDescription = "拍照",
                    modifier = Modifier.size(32.dp)
                )
            }
        } else {
            // 实时模式下显示占位符
            Spacer(modifier = Modifier.size(72.dp))
        }

        // 占位符，保持布局平衡
        Spacer(modifier = Modifier.size(56.dp))
    }
}

@Composable
private fun PermissionDeniedContent() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CameraAlt,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "需要相机权限",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "请授予相机权限以使用图像分类功能",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = { /* 重新请求权限 */ }
        ) {
            Text("授予权限")
        }
    }
}
