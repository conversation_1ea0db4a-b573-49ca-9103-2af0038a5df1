/**
 * 样本收集界面
 *
 * 用于收集训练样本的界面
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.learning

import android.graphics.Bitmap
import android.Manifest
import androidx.camera.core.ImageProxy
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.presentation.learning.SampleCollectionViewModel
import com.fsl.app.presentation.learning.CollectedSample
import com.fsl.app.ui.components.CameraPreview
import com.fsl.app.ui.components.PermissionHandler

/**
 * 样本收集界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SampleCollectionScreen(
    className: String,
    onSamplesCollected: (String, List<Bitmap>) -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: SampleCollectionViewModel = hiltViewModel()
) {
    val collectionState by viewModel.collectionState.collectAsState()

    // 设置类别名称
    LaunchedEffect(className) {
        viewModel.setClassName(className)
    }

    // 权限处理
    PermissionHandler(
        permission = Manifest.permission.CAMERA,
        onPermissionGranted = {
            SampleCollectionContent(
                className = className,
                collectionState = collectionState,
                viewModel = viewModel,
                onSamplesCollected = onSamplesCollected,
                onNavigateBack = onNavigateBack
            )
        },
        onPermissionDenied = {
            PermissionDeniedContent(onNavigateBack = onNavigateBack)
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SampleCollectionContent(
    className: String,
    collectionState: com.fsl.app.presentation.learning.SampleCollectionState,
    viewModel: SampleCollectionViewModel,
    onSamplesCollected: (String, List<Bitmap>) -> Unit,
    onNavigateBack: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部栏
        TopAppBar(
            title = { Text("收集样本: $className") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                if (viewModel.canCompleteCollection()) {
                    TextButton(
                        onClick = {
                            val samples = viewModel.getCollectedBitmaps()
                            onSamplesCollected(className, samples)
                            onNavigateBack()
                        }
                    ) {
                        Text("完成", fontWeight = FontWeight.Bold)
                    }
                }
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 进度指示器
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "收集进度",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                LinearProgressIndicator(
                    progress = viewModel.getCollectionProgress(),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "${collectionState.collectedSamples.size} / ${collectionState.targetSampleCount} 样本",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 相机预览和收集按钮
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "拍摄样本",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 真实相机预览区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .clip(RoundedCornerShape(8.dp))
                ) {
                    // 真实相机预览
                    CameraPreview(
                        modifier = Modifier.fillMaxSize(),
                        onImageCaptured = null, // 不使用自动捕获
                        useMockCamera = false,
                        isFrontCamera = false
                    )

                    // 拍摄按钮覆盖在预览上
                    FloatingActionButton(
                        onClick = {
                            viewModel.captureCurrentFrame()
                        },
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(16.dp)
                            .size(64.dp),
                        containerColor = if (!collectionState.isCollecting && !viewModel.isCollectionComplete()) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                        }
                    ) {
                        if (collectionState.isCollecting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(32.dp),
                                color = MaterialTheme.colorScheme.onPrimary,
                                strokeWidth = 3.dp
                            )
                        } else {
                            Icon(
                                Icons.Default.CameraAlt,
                                contentDescription = "拍摄",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    }

                    // 拍摄指导文字
                    Card(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                        )
                    ) {
                        Text(
                            text = "将${className}放在相机前，点击拍摄按钮",
                            modifier = Modifier.padding(8.dp),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = if (collectionState.isCollecting) "正在处理..." else "点击相机按钮拍摄",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 已收集的样本
        if (collectionState.collectedSamples.isNotEmpty()) {
            Text(
                text = "已收集的样本",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(collectionState.collectedSamples) { sample ->
                    SampleCard(
                        sample = sample,
                        onRemove = { viewModel.removeSample(sample) }
                    )
                }
            }
        }

        // 错误提示
        collectionState.error?.let { error ->
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = "错误",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    TextButton(
                        onClick = { viewModel.clearError() }
                    ) {
                        Text("关闭")
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部操作按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedButton(
                onClick = { viewModel.clearSamples() },
                modifier = Modifier.weight(1f),
                enabled = collectionState.collectedSamples.isNotEmpty()
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("清除")
            }

            Button(
                onClick = {
                    android.util.Log.i("SampleCollectionScreen", "开始学习: $className, 样本数: ${collectionState.collectedSamples.size}")
                    // 调用批量学习方法
                    viewModel.confirmAndLearnAllSamples()

                    // 同时调用原有的回调以保持兼容性
                    val samples = viewModel.getCollectedBitmaps()
                    onSamplesCollected(className, samples)
                },
                modifier = Modifier.weight(1f),
                enabled = viewModel.canCompleteCollection()
            ) {
                Icon(Icons.Default.Check, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("完成学习")
            }
        }
    }
}

/**
 * 样本卡片
 */
@Composable
private fun SampleCard(
    sample: CollectedSample,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier.size(80.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Box {
            Image(
                bitmap = sample.bitmap.asImageBitmap(),
                contentDescription = "样本",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )

            IconButton(
                onClick = onRemove,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(24.dp)
                    .background(
                        MaterialTheme.colorScheme.error.copy(alpha = 0.8f),
                        RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "删除",
                    tint = MaterialTheme.colorScheme.onError,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 权限拒绝内容
 */
@Composable
private fun PermissionDeniedContent(
    onNavigateBack: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CameraAlt,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "需要相机权限",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "请授予相机权限以拍摄训练样本",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = onNavigateBack
        ) {
            Text("返回")
        }
    }
}
