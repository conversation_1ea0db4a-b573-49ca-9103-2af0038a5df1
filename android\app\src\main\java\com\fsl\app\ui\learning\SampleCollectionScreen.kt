/**
 * 样本收集界面
 *
 * 用于收集训练样本的界面
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.learning

import android.graphics.Bitmap
import android.Manifest
import androidx.camera.core.ImageProxy
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.presentation.learning.SampleCollectionViewModel
import com.fsl.app.presentation.learning.CollectedSample
import com.fsl.app.ui.components.CameraPreview
import com.fsl.app.ui.components.PermissionHandler

/**
 * 样本收集界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SampleCollectionScreen(
    className: String,
    onSamplesCollected: (String, List<Bitmap>) -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: SampleCollectionViewModel = hiltViewModel()
) {
    val collectionState by viewModel.collectionState.collectAsState()

    // 设置类别名称
    LaunchedEffect(className) {
        viewModel.setClassName(className)
    }

    // 权限处理
    PermissionHandler(
        permission = Manifest.permission.CAMERA,
        onPermissionGranted = {
            SampleCollectionContent(
                className = className,
                collectionState = collectionState,
                viewModel = viewModel,
                onSamplesCollected = onSamplesCollected,
                onNavigateBack = onNavigateBack
            )
        },
        onPermissionDenied = {
            PermissionDeniedContent(onNavigateBack = onNavigateBack)
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SampleCollectionContent(
    className: String,
    collectionState: com.fsl.app.presentation.learning.SampleCollectionState,
    viewModel: SampleCollectionViewModel,
    onSamplesCollected: (String, List<Bitmap>) -> Unit,
    onNavigateBack: () -> Unit
) {
    // 监听相机帧捕获请求
    val shouldCaptureFrame by viewModel.shouldCaptureFrame.collectAsState()

    // 获取当前context
    val context = LocalContext.current

    // 处理相机帧捕获 - 添加详细调试日志
    val onImageCaptured: (ImageProxy?) -> Unit = { imageProxy ->
        if (imageProxy != null) {
            // 总是更新最新的相机帧
            viewModel.setCurrentImageProxy(imageProxy)
            android.util.Log.d("SampleCollectionScreen", "更新相机帧: ${imageProxy.width}x${imageProxy.height}, shouldCaptureFrame=$shouldCaptureFrame")

            // 只有在请求拍摄时才处理
            if (shouldCaptureFrame) {
                android.util.Log.i("SampleCollectionScreen", "=== 开始处理拍摄请求 ===")
                android.util.Log.i("SampleCollectionScreen", "相机帧尺寸: ${imageProxy.width}x${imageProxy.height}")
                android.util.Log.i("SampleCollectionScreen", "相机帧格式: ${imageProxy.format}")

                viewModel.resetCaptureRequest()
                // 立即触发帧处理，传递context进行权限检查
                viewModel.captureCurrentFrame(context)
            }
        } else {
            android.util.Log.w("SampleCollectionScreen", "收到空的相机帧")
        }
    }

    // 监听学习完成状态
    LaunchedEffect(collectionState.isLearningCompleted, collectionState.learningSuccess) {
        if (collectionState.isLearningCompleted && collectionState.learningSuccess) {
            android.util.Log.i("SampleCollectionScreen", "学习完成且成功，准备跳转")
            // 学习成功完成，调用回调并跳转
            val samples = viewModel.getCollectedBitmaps()
            onSamplesCollected(className, samples)
            onNavigateBack()
        } else if (collectionState.isLearningCompleted && !collectionState.learningSuccess) {
            android.util.Log.w("SampleCollectionScreen", "学习完成但失败，不跳转")
            // 学习失败，不跳转，让用户看到错误信息
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部栏
        TopAppBar(
            title = { Text("收集样本: $className") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                if (viewModel.canCompleteCollection()) {
                    TextButton(
                        onClick = {
                            val samples = viewModel.getCollectedBitmaps()
                            onSamplesCollected(className, samples)
                            onNavigateBack()
                        }
                    ) {
                        Text("完成", fontWeight = FontWeight.Bold)
                    }
                }
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 进度指示器
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = if (collectionState.isLearning) "学习进度" else "收集进度",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    // GPU加速开关
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "GPU",
                            style = MaterialTheme.typography.bodySmall
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Switch(
                            checked = collectionState.useGpuAcceleration,
                            onCheckedChange = { viewModel.toggleGpuAcceleration() },
                            modifier = Modifier.size(32.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 显示学习进度或收集进度
                if (collectionState.isLearning) {
                    // 学习进度条
                    LinearProgressIndicator(
                        progress = collectionState.learningProgress,
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.secondary
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 学习阶段和时间信息
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = collectionState.learningStage,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.secondary
                        )

                        if (collectionState.estimatedTimeRemaining > 0) {
                            Text(
                                text = "剩余: ${formatTime(collectionState.estimatedTimeRemaining)}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "${(collectionState.learningProgress * 100).toInt()}% 完成 (${if (collectionState.useGpuAcceleration) "GPU加速" else "标准模式"})",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                } else {
                    // 收集进度条
                    LinearProgressIndicator(
                        progress = viewModel.getCollectionProgress(),
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "${collectionState.collectedSamples.size} / ${collectionState.targetSampleCount} 样本",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 相机预览和收集按钮
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "拍摄样本",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 真实相机预览区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .clip(RoundedCornerShape(8.dp))
                ) {
                    // 真实相机预览，集成帧捕获
                    CameraPreview(
                        modifier = Modifier.fillMaxSize(),
                        onImageCaptured = onImageCaptured, // 使用真实相机帧捕获
                        useMockCamera = false,
                        isFrontCamera = false,
                        captureMode = "manual" // 使用手动模式，避免连续触发
                    )

                    // 拍摄按钮覆盖在预览上
                    FloatingActionButton(
                        onClick = {
                            // 只请求相机帧，处理逻辑在onImageCaptured回调中
                            android.util.Log.i("SampleCollectionScreen", "=== 拍摄按钮点击 ===")
                            android.util.Log.i("SampleCollectionScreen", "当前状态: isCollecting=${collectionState.isCollecting}")
                            android.util.Log.i("SampleCollectionScreen", "当前样本数: ${collectionState.collectedSamples.size}")
                            android.util.Log.i("SampleCollectionScreen", "shouldCaptureFrame当前值: $shouldCaptureFrame")

                            if (!collectionState.isCollecting) {
                                android.util.Log.i("SampleCollectionScreen", "发送相机帧请求...")
                                viewModel.requestCameraFrame()
                                android.util.Log.i("SampleCollectionScreen", "相机帧请求已发送")
                            } else {
                                android.util.Log.w("SampleCollectionScreen", "正在收集中，忽略点击")
                            }
                        },
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(16.dp)
                            .size(64.dp),
                        containerColor = if (!collectionState.isCollecting && !viewModel.isCollectionComplete()) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                        }
                    ) {
                        if (collectionState.isCollecting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(32.dp),
                                color = MaterialTheme.colorScheme.onPrimary,
                                strokeWidth = 3.dp
                            )
                        } else {
                            Icon(
                                Icons.Default.CameraAlt,
                                contentDescription = "拍摄",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    }

                    // 拍摄指导文字
                    Card(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                        )
                    ) {
                        Text(
                            text = "将${className}放在相机前，点击拍摄按钮",
                            modifier = Modifier.padding(8.dp),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = if (collectionState.isCollecting) "正在处理..." else "点击相机按钮拍摄",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 已收集的样本
        if (collectionState.collectedSamples.isNotEmpty()) {
            Text(
                text = "已收集的样本",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(collectionState.collectedSamples) { sample ->
                    SampleCard(
                        sample = sample,
                        onRemove = { viewModel.removeSample(sample) }
                    )
                }
            }
        }

        // 错误提示
        collectionState.error?.let { error ->
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = "错误",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    TextButton(
                        onClick = { viewModel.clearError() }
                    ) {
                        Text("关闭")
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部操作按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedButton(
                onClick = { viewModel.clearSamples() },
                modifier = Modifier.weight(1f),
                enabled = collectionState.collectedSamples.isNotEmpty()
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("清除")
            }

            Button(
                onClick = {
                    android.util.Log.i("SampleCollectionScreen", "开始学习: $className, 样本数: ${collectionState.collectedSamples.size}")
                    // 重置学习完成状态
                    viewModel.resetLearningCompletedState()
                    // 只调用批量学习方法，等待学习完成后再跳转
                    viewModel.confirmAndLearnAllSamples()
                },
                modifier = Modifier.weight(1f),
                enabled = viewModel.canCompleteCollection() && !collectionState.isLearning
            ) {
                if (collectionState.isLearning) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("学习中...")
                } else {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("完成学习")
                }
            }
        }
    }
}

/**
 * 样本卡片
 */
@Composable
private fun SampleCard(
    sample: CollectedSample,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier.size(80.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Box {
            // 改进的图像显示逻辑
            val imageBitmap = remember(sample.bitmap) {
                try {
                    // 验证bitmap有效性
                    if (sample.bitmap.isRecycled) {
                        android.util.Log.e("SampleCard", "Bitmap已被回收")
                        null
                    } else if (sample.bitmap.width <= 0 || sample.bitmap.height <= 0) {
                        android.util.Log.e("SampleCard", "Bitmap尺寸无效: ${sample.bitmap.width}x${sample.bitmap.height}")
                        null
                    } else {
                        android.util.Log.d("SampleCard", "显示样本图像: ${sample.bitmap.width}x${sample.bitmap.height}")
                        sample.bitmap.asImageBitmap()
                    }
                } catch (e: Exception) {
                    android.util.Log.e("SampleCard", "转换ImageBitmap失败", e)
                    null
                }
            }

            if (imageBitmap != null) {
                Image(
                    bitmap = imageBitmap,
                    contentDescription = "样本图像",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
            } else {
                // 显示错误占位符
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(MaterialTheme.colorScheme.errorContainer),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.BrokenImage,
                            contentDescription = "图像错误",
                            tint = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.size(20.dp)
                        )
                        Text(
                            text = "错误",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }

            IconButton(
                onClick = onRemove,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(24.dp)
                    .background(
                        MaterialTheme.colorScheme.error.copy(alpha = 0.8f),
                        RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "删除",
                    tint = MaterialTheme.colorScheme.onError,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 权限拒绝内容
 */
@Composable
private fun PermissionDeniedContent(
    onNavigateBack: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CameraAlt,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "需要相机权限",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "请授予相机权限以拍摄训练样本",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(
            onClick = onNavigateBack
        ) {
            Text("返回")
        }
    }
}

/**
 * 格式化时间显示
 * @param timeMs 时间（毫秒）
 * @return 格式化的时间字符串
 */
private fun formatTime(timeMs: Long): String {
    val seconds = (timeMs / 1000).toInt()
    return when {
        seconds < 60 -> "${seconds}秒"
        seconds < 3600 -> "${seconds / 60}分${seconds % 60}秒"
        else -> "${seconds / 3600}时${(seconds % 3600) / 60}分"
    }
}