/**
 * 样本收集界面
 *
 * 用于收集训练样本的界面
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.learning

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.presentation.learning.SampleCollectionViewModel
import com.fsl.app.presentation.learning.CollectedSample

/**
 * 样本收集界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SampleCollectionScreen(
    className: String,
    onSamplesCollected: (String, List<Bitmap>) -> Unit,
    onNavigateBack: () -> Unit,
    viewModel: SampleCollectionViewModel = hiltViewModel()
) {
    val collectionState by viewModel.collectionState.collectAsState()

    // 设置类别名称
    LaunchedEffect(className) {
        viewModel.setClassName(className)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部栏
        TopAppBar(
            title = { Text("收集样本: $className") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                if (viewModel.canCompleteCollection()) {
                    TextButton(
                        onClick = {
                            val samples = viewModel.getCollectedBitmaps()
                            onSamplesCollected(className, samples)
                            onNavigateBack()
                        }
                    ) {
                        Text("完成", fontWeight = FontWeight.Bold)
                    }
                }
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 进度指示器
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "收集进度",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                LinearProgressIndicator(
                    progress = viewModel.getCollectionProgress(),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "${collectionState.collectedSamples.size} / ${collectionState.targetSampleCount} 样本",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 相机预览和收集按钮
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "拍摄样本",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 相机预览区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                        .background(
                            MaterialTheme.colorScheme.surfaceVariant,
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    // 显示当前要拍摄的物体预览
                    val previewSample = remember { createMockSample() }

                    Image(
                        bitmap = previewSample.asImageBitmap(),
                        contentDescription = "预览",
                        modifier = Modifier
                            .size(150.dp)
                            .clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Crop
                    )

                    // 拍摄按钮覆盖在预览上
                    Button(
                        onClick = {
                            // 创建新的样本
                            val newSample = createMockSample()
                            viewModel.addSample(newSample)
                        },
                        enabled = !collectionState.isCollecting && !viewModel.isCollectionComplete(),
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(8.dp)
                            .size(56.dp),
                        shape = RoundedCornerShape(28.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f)
                        )
                    ) {
                        if (collectionState.isCollecting) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                color = MaterialTheme.colorScheme.onPrimary,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                Icons.Default.CameraAlt,
                                contentDescription = "拍摄",
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = if (collectionState.isCollecting) "正在处理..." else "点击相机按钮拍摄",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 已收集的样本
        if (collectionState.collectedSamples.isNotEmpty()) {
            Text(
                text = "已收集的样本",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(collectionState.collectedSamples) { sample ->
                    SampleCard(
                        sample = sample,
                        onRemove = { viewModel.removeSample(sample) }
                    )
                }
            }
        }

        // 错误提示
        collectionState.error?.let { error ->
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = "错误",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    TextButton(
                        onClick = { viewModel.clearError() }
                    ) {
                        Text("关闭")
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部操作按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedButton(
                onClick = { viewModel.clearSamples() },
                modifier = Modifier.weight(1f),
                enabled = collectionState.collectedSamples.isNotEmpty()
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("清除")
            }

            Button(
                onClick = {
                    val samples = viewModel.getCollectedBitmaps()
                    onSamplesCollected(className, samples)
                    onNavigateBack()
                },
                modifier = Modifier.weight(1f),
                enabled = viewModel.canCompleteCollection()
            ) {
                Icon(Icons.Default.Check, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("完成收集")
            }
        }
    }
}

/**
 * 样本卡片
 */
@Composable
private fun SampleCard(
    sample: CollectedSample,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier.size(80.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Box {
            Image(
                bitmap = sample.bitmap.asImageBitmap(),
                contentDescription = "样本",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )

            IconButton(
                onClick = onRemove,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(24.dp)
                    .background(
                        MaterialTheme.colorScheme.error.copy(alpha = 0.8f),
                        RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "删除",
                    tint = MaterialTheme.colorScheme.onError,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 创建模拟样本（用于测试）
 */
private fun createMockSample(): Bitmap {
    return Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888).apply {
        val canvas = android.graphics.Canvas(this)
        val paint = android.graphics.Paint()

        // 随机背景色
        val colors = arrayOf(
            android.graphics.Color.RED,
            android.graphics.Color.GREEN,
            android.graphics.Color.BLUE,
            android.graphics.Color.YELLOW,
            android.graphics.Color.MAGENTA
        )

        paint.color = colors[(System.currentTimeMillis() % colors.size).toInt()]
        canvas.drawRect(0f, 0f, 224f, 224f, paint)

        // 添加一些随机形状
        paint.color = android.graphics.Color.WHITE
        canvas.drawCircle(112f, 112f, 50f, paint)
    }
}
