#!/usr/bin/env python3
"""
导出PyTorch Mobile模型脚本

这个脚本下载预训练的MobileNetV3模型，优化它用于移动设备，
并保存为.ptl格式文件供Android应用使用。

作者: AI Assistant
日期: 2024
"""

import torch
import torchvision
from torch.utils.mobile_optimizer import optimize_for_mobile
import json
import os

def export_mobilenet_v3():
    """导出MobileNetV3模型"""
    print("正在下载MobileNetV3模型...")
    
    # 下载预训练的MobileNetV3模型
    model = torchvision.models.mobilenet_v3_small(pretrained=True)
    model.eval()
    
    print("正在优化模型用于移动设备...")
    
    # 转换为ScriptModule
    scripted_model = torch.jit.script(model)
    
    # 优化用于移动设备
    optimized_model = optimize_for_mobile(scripted_model)
    
    # 保存为.ptl文件
    output_path = "android/app/src/main/assets/mobilenet_v3_small.ptl"
    optimized_model._save_for_lite_interpreter(output_path)
    
    print(f"模型已保存到: {output_path}")
    
    # 获取模型大小
    model_size = os.path.getsize(output_path)
    print(f"模型大小: {model_size / (1024*1024):.2f} MB")
    
    return output_path

def create_class_names():
    """创建ImageNet类别名称文件"""
    print("正在创建类别名称文件...")
    
    # ImageNet前10个类别（简化版）
    class_names = [
        "tench", "goldfish", "great_white_shark", "tiger_shark", "hammerhead",
        "electric_ray", "stingray", "cock", "hen", "ostrich"
    ]
    
    # 保存类别名称
    class_names_path = "android/app/src/main/assets/class_names.json"
    with open(class_names_path, 'w', encoding='utf-8') as f:
        json.dump(class_names, f, indent=2, ensure_ascii=False)
    
    print(f"类别名称已保存到: {class_names_path}")
    return class_names_path

def create_model_info():
    """创建模型信息文件"""
    print("正在创建模型信息文件...")
    
    model_info = {
        "model_name": "mobilenet_v3_small",
        "version": "1.0.0",
        "framework": "pytorch_mobile",
        "input_size": [224, 224, 3],
        "feature_dim": 1000,  # MobileNetV3的输出维度
        "num_classes": 10,    # 我们只使用前10个类别
        "model_file": "mobilenet_v3_small.ptl",
        "preprocessing": {
            "mean": [0.485, 0.456, 0.406],
            "std": [0.229, 0.224, 0.225],
            "resize": 224,
            "normalize": True
        },
        "description": "MobileNetV3 Small model optimized for mobile inference",
        "architecture": "MobileNetV3",
        "pretrained_dataset": "ImageNet"
    }
    
    # 保存模型信息
    model_info_path = "android/app/src/main/assets/model_info.json"
    with open(model_info_path, 'w', encoding='utf-8') as f:
        json.dump(model_info, f, indent=2, ensure_ascii=False)
    
    print(f"模型信息已保存到: {model_info_path}")
    return model_info_path

def main():
    """主函数"""
    print("=== PyTorch Mobile模型导出工具 ===")
    
    # 确保assets目录存在
    assets_dir = "android/app/src/main/assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    try:
        # 导出模型
        model_path = export_mobilenet_v3()
        
        # 创建类别名称文件
        class_names_path = create_class_names()
        
        # 创建模型信息文件
        model_info_path = create_model_info()
        
        print("\n=== 导出完成 ===")
        print(f"模型文件: {model_path}")
        print(f"类别文件: {class_names_path}")
        print(f"信息文件: {model_info_path}")
        print("\n现在可以重新编译Android应用了！")
        
    except Exception as e:
        print(f"导出失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
