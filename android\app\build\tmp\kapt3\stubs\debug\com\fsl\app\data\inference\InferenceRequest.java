package com.fsl.app.data.inference;

import java.lang.System;

/**
 * 推理请求
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0082\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\'\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u00a8\u0006\u0018"}, d2 = {"Lcom/fsl/app/data/inference/InferenceRequest;", "", "bitmap", "Landroid/graphics/Bitmap;", "timestamp", "", "requestId", "(Landroid/graphics/Bitmap;JJ)V", "getBitmap", "()Landroid/graphics/Bitmap;", "getRequestId", "()J", "getTimestamp", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
final class InferenceRequest {
    @org.jetbrains.annotations.NotNull
    private final android.graphics.Bitmap bitmap = null;
    private final long timestamp = 0L;
    private final long requestId = 0L;
    
    /**
     * 推理请求
     */
    @org.jetbrains.annotations.NotNull
    public final com.fsl.app.data.inference.InferenceRequest copy(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, long timestamp, long requestId) {
        return null;
    }
    
    /**
     * 推理请求
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 推理请求
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 推理请求
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public InferenceRequest(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap, long timestamp, long requestId) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.Bitmap getBitmap() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final long getRequestId() {
        return 0L;
    }
}