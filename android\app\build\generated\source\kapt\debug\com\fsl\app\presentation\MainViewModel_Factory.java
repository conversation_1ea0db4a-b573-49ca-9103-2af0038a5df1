// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.presentation;

import com.fsl.app.domain.usecase.ModelManagementUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<ModelManagementUseCase> modelManagementUseCaseProvider;

  public MainViewModel_Factory(Provider<ModelManagementUseCase> modelManagementUseCaseProvider) {
    this.modelManagementUseCaseProvider = modelManagementUseCaseProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(modelManagementUseCaseProvider.get());
  }

  public static MainViewModel_Factory create(
      Provider<ModelManagementUseCase> modelManagementUseCaseProvider) {
    return new MainViewModel_Factory(modelManagementUseCaseProvider);
  }

  public static MainViewModel newInstance(ModelManagementUseCase modelManagementUseCase) {
    return new MainViewModel(modelManagementUseCase);
  }
}
