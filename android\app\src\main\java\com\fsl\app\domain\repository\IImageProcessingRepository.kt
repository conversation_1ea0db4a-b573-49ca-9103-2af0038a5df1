/**
 * 图像处理仓库接口
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.repository

import android.graphics.Bitmap
import androidx.camera.core.ImageProxy

/**
 * 图像处理仓库接口
 */
interface IImageProcessingRepository {
    
    /**
     * 预处理图像
     */
    fun preprocessImage(bitmap: Bitmap): Bitmap
    
    /**
     * 预处理ImageProxy
     */
    fun preprocessImageProxy(imageProxy: ImageProxy): Bitmap
    
    /**
     * 图像增强
     */
    fun augmentImage(bitmap: Bitmap): List<Bitmap>
    
    /**
     * 调整图像大小
     */
    fun resizeImage(bitmap: Bitmap, width: Int, height: Int): Bitmap
    
    /**
     * 图像归一化
     */
    fun normalizeImage(bitmap: Bitmap): FloatArray
}
