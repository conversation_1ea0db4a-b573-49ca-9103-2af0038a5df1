{"artifacts": [{"path": "F:/geek/fsl/android/app/build/intermediates/cxx/Debug/6w13315g/obj/armeabi-v7a/libfsl_native.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_options", "target_compile_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 35, "parent": 0}, {"command": 1, "file": 0, "line": 42, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}, {"command": 3, "file": 0, "line": 60, "parent": 0}, {"command": 4, "file": 0, "line": 18, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC"}, {"backtrace": 3, "fragment": "-O3"}, {"backtrace": 3, "fragment": "-ffast-math"}, {"backtrace": 3, "fragment": "-DE<PERSON><PERSON>_NO_DEBUG"}, {"backtrace": 3, "fragment": "-DEIGEN_DONT_PARALLELIZE"}, {"backtrace": 3, "fragment": "-DUSE_NNAPI"}, {"fragment": "-std=c++17"}], "defines": [{"backtrace": 4, "define": "ANDROID"}, {"backtrace": 4, "define": "USE_NNAPI"}, {"backtrace": 4, "define": "__ANDROID_API__=29"}, {"define": "fsl_native_EXPORTS"}], "includes": [{"backtrace": 5, "path": "F:/geek/fsl/android/app/src/main/cpp/include"}, {"backtrace": 5, "path": "F:/geek/fsl/android/app/src/main/cpp/eigen"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "fsl_native::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\29\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\29\\libandroid.so", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\androidsdk\\ndk\\27.0.11718014\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\29\\libneuralnetworks.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-ljnigraphics", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "fsl_native", "nameOnDisk": "libfsl_native.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "fsl_inference.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "prototypical_network.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "feature_extractor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "image_processor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "nnapi_engine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "object_tracker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "jni_interface.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}