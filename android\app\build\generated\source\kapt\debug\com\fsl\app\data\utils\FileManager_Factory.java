// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.fsl.app.data.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes"
})
public final class FileManager_Factory implements Factory<FileManager> {
  private final Provider<Context> contextProvider;

  public FileManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FileManager get() {
    return newInstance(contextProvider.get());
  }

  public static FileManager_Factory create(Provider<Context> contextProvider) {
    return new FileManager_Factory(contextProvider);
  }

  public static FileManager newInstance(Context context) {
    return new FileManager(context);
  }
}
