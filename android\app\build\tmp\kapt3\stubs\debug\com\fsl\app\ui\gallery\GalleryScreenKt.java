package com.fsl.app.ui.gallery;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a2\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\"\u0010\t\u001a\u00020\u00012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\b\u0010\r\u001a\u00020\u0001H\u0003\u001a\u0018\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\bH\u0003\u00a8\u0006\u0011"}, d2 = {"GalleryImageCard", "", "image", "Lcom/fsl/app/domain/model/GalleryImage;", "onDelete", "Lkotlin/Function0;", "onMarkAsLearned", "Lkotlin/Function1;", "", "GalleryScreen", "onNavigateToCamera", "viewModel", "Lcom/fsl/app/presentation/gallery/GalleryViewModel;", "ImagePlaceholder", "StatItem", "label", "value", "app_debug"})
public final class GalleryScreenKt {
    
    /**
     * 图库界面
     */
    @androidx.compose.runtime.Composable
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    public static final void GalleryScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCamera, @org.jetbrains.annotations.NotNull
    com.fsl.app.presentation.gallery.GalleryViewModel viewModel) {
    }
    
    /**
     * 统计项组件
     */
    @androidx.compose.runtime.Composable
    private static final void StatItem(java.lang.String label, java.lang.String value) {
    }
    
    /**
     * 图库图像卡片
     */
    @androidx.compose.runtime.Composable
    private static final void GalleryImageCard(com.fsl.app.domain.model.GalleryImage image, kotlin.jvm.functions.Function0<kotlin.Unit> onDelete, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMarkAsLearned) {
    }
    
    /**
     * 图像占位符
     */
    @androidx.compose.runtime.Composable
    private static final void ImagePlaceholder() {
    }
}