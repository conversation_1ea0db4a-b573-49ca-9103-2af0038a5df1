﻿{
  "model_name": "mobilenet_v3_small",
  "version": "1.0.0",
  "framework": "pytorch_mobile",
  "input_size": [224, 224, 3],
  "feature_dim": 1000,
  "num_classes": 1000,
  "model_file": "mobilenet_v3_small.ptl",
  "class_names_file": "imagenet_classes.json",
  "preprocessing": {
    "mean": [0.485, 0.456, 0.406],
    "std": [0.229, 0.224, 0.225],
    "resize": 224,
    "normalize": true
  },
  "description": "MobileNetV3 Small model optimized for mobile inference",
  "architecture": "MobileNetV3",
  "pretrained_dataset": "ImageNet",
  "model_size_mb": 10.2,
  "inference_time_ms": 50
}
