/**
 * 学习界面
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.ui.learning

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fsl.app.R
import com.fsl.app.presentation.learning.LearningViewModel
import com.fsl.app.presentation.learning.LearningState

/**
 * 学习界面
 */
@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
fun LearningScreen(
    onNavigateToCamera: () -> Unit = {},
    onNavigateToSampleCollection: (String) -> Unit = {},
    viewModel: LearningViewModel = hiltViewModel()
) {
    val learningState by viewModel.learningState.collectAsState()
    var showAddClassDialog by remember { mutableStateOf(false) }

    // 页面显示时刷新数据
    LaunchedEffect(Unit) {
        android.util.Log.i("LearningScreen", "页面显示，刷新学习类别")
        viewModel.refreshLearnedClasses()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = stringResource(R.string.learning),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 添加新类别按钮
        Button(
            onClick = { showAddClassDialog = true },
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(Icons.Default.Add, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text(stringResource(R.string.add_class))
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 训练进度
        if (learningState.isTraining) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.training_progress),
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    LinearProgressIndicator(
                        progress = learningState.trainingProgress,
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "${(learningState.trainingProgress * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        // 已学习类别列表
        Text(
            text = stringResource(R.string.learned_classes),
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(8.dp))

        if (learningState.learnedClasses.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "还没有学习任何类别\n点击上方按钮添加新类别",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(learningState.learnedClasses) { learnedClass ->
                    LearnedClassItem(
                        className = learnedClass.name,
                        sampleCount = learnedClass.sampleCount,
                        onDelete = { viewModel.deleteClass(learnedClass.name) }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // 返回相机按钮
        OutlinedButton(
            onClick = onNavigateToCamera,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("返回相机")
        }
    }

    // 添加类别对话框
    if (showAddClassDialog) {
        AddClassDialog(
            onDismiss = { showAddClassDialog = false },
            onConfirm = { className ->
                showAddClassDialog = false
                onNavigateToSampleCollection(className) // 导航到样本收集界面
            }
        )
    }

    // 错误提示
    learningState.error?.let { error ->
        LaunchedEffect(error) {
            // 这里可以显示Snackbar或其他错误提示
        }
    }
}

/**
 * 已学习类别项
 */
@Composable
private fun LearnedClassItem(
    className: String,
    sampleCount: Int,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = className,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "$sampleCount 个样本",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            IconButton(onClick = onDelete) {
                Icon(
                    Icons.Default.Delete,
                    contentDescription = stringResource(R.string.delete_class),
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * 添加类别对话框
 */
@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
private fun AddClassDialog(
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var className by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(stringResource(R.string.add_class))
        },
        text = {
            Column {
                Text("请输入新类别的名称：")
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = className,
                    onValueChange = { className = it },
                    label = { Text(stringResource(R.string.class_name)) },
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (className.isNotBlank()) {
                        onConfirm(className.trim())
                    }
                },
                enabled = className.isNotBlank()
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
