package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.fsl.app.presentation.learning.SampleCollectionViewModel_HiltModules.KeyModule"
)
public class _com_fsl_app_presentation_learning_SampleCollectionViewModel_HiltModules_KeyModule {
}
