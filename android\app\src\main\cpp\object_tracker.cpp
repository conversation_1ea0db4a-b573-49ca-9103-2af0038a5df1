/**
 * 对象跟踪器实现
 * 
 * 实现多对象跟踪算法
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "include/object_tracker.h"
#include <android/log.h>
#include <algorithm>
#include <cmath>
#include <chrono>

#define LOG_TAG "ObjectTracker"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

namespace fsl {

// BoundingBox实现
float BoundingBox::iou(const BoundingBox& other) const {
    float left = std::max(x, other.x);
    float top = std::max(y, other.y);
    float right = std::min(x + width, other.x + other.width);
    float bottom = std::min(y + height, other.y + other.height);
    
    if (right <= left || bottom <= top) {
        return 0.0f;
    }
    
    float intersection = (right - left) * (bottom - top);
    float union_area = area() + other.area() - intersection;
    
    return union_area > 0 ? intersection / union_area : 0.0f;
}

std::pair<float, float> BoundingBox::center() const {
    return {x + width / 2.0f, y + height / 2.0f};
}

float BoundingBox::area() const {
    return width * height;
}

// TrackedObject实现
BoundingBox TrackedObject::predict() const {
    if (history.size() < 2) {
        return bbox;
    }
    
    // 简单的线性预测
    auto velocity = getVelocity();
    BoundingBox predicted = bbox;
    predicted.x += velocity.first;
    predicted.y += velocity.second;
    
    // 确保边界框在有效范围内
    predicted.x = std::max(0.0f, std::min(1.0f - predicted.width, predicted.x));
    predicted.y = std::max(0.0f, std::min(1.0f - predicted.height, predicted.y));
    
    return predicted;
}

void TrackedObject::update(const Detection& detection) {
    bbox = detection.bbox;
    className = detection.className;
    confidence = detection.confidence;
    hitStreak++;
    timeSinceUpdate = 0;
    
    // 保存历史位置（最多保存10帧）
    history.push_back(bbox);
    if (history.size() > 10) {
        history.erase(history.begin());
    }
}

std::pair<float, float> TrackedObject::getVelocity() const {
    if (history.size() < 2) {
        return {0.0f, 0.0f};
    }
    
    auto current = history.back().center();
    auto previous = history[history.size() - 2].center();
    
    return {current.first - previous.first, current.second - previous.second};
}

// KalmanFilter实现
KalmanFilter::KalmanFilter() : m_initialized(false) {
    // 初始化矩阵大小
    m_state.resize(4, std::vector<float>(1, 0));
    m_covariance.resize(4, std::vector<float>(4, 0));
    m_transitionMatrix.resize(4, std::vector<float>(4, 0));
    m_observationMatrix.resize(2, std::vector<float>(4, 0));
    m_processNoise.resize(4, std::vector<float>(4, 0));
    m_measurementNoise.resize(2, std::vector<float>(2, 0));
    
    // 设置状态转移矩阵 (假设dt=1)
    m_transitionMatrix[0][0] = 1; m_transitionMatrix[0][2] = 1;
    m_transitionMatrix[1][1] = 1; m_transitionMatrix[1][3] = 1;
    m_transitionMatrix[2][2] = 1;
    m_transitionMatrix[3][3] = 1;
    
    // 设置观测矩阵
    m_observationMatrix[0][0] = 1;
    m_observationMatrix[1][1] = 1;
    
    // 设置过程噪声
    float processNoiseValue = 0.1f;
    for (int i = 0; i < 4; i++) {
        m_processNoise[i][i] = processNoiseValue;
    }
    
    // 设置测量噪声
    float measurementNoiseValue = 0.1f;
    for (int i = 0; i < 2; i++) {
        m_measurementNoise[i][i] = measurementNoiseValue;
    }
}

KalmanFilter::~KalmanFilter() {}

void KalmanFilter::initialize(const std::vector<float>& initialState) {
    if (initialState.size() != 4) {
        LOGE("初始状态维度错误");
        return;
    }
    
    for (int i = 0; i < 4; i++) {
        m_state[i][0] = initialState[i];
    }
    
    // 初始化协方差矩阵
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 4; j++) {
            m_covariance[i][j] = (i == j) ? 1.0f : 0.0f;
        }
    }
    
    m_initialized = true;
}

std::vector<float> KalmanFilter::predict() {
    if (!m_initialized) {
        return {};
    }
    
    // 预测状态: x = F * x
    auto predictedState = matrixMultiply(m_transitionMatrix, m_state);
    m_state = predictedState;
    
    // 预测协方差: P = F * P * F^T + Q
    auto FP = matrixMultiply(m_transitionMatrix, m_covariance);
    auto FT = matrixTranspose(m_transitionMatrix);
    auto FPFT = matrixMultiply(FP, FT);
    m_covariance = matrixAdd(FPFT, m_processNoise);
    
    return getState();
}

void KalmanFilter::update(const std::vector<float>& measurement) {
    if (!m_initialized || measurement.size() != 2) {
        return;
    }
    
    // 简化的更新实现
    m_state[0][0] = measurement[0];
    m_state[1][0] = measurement[1];
}

std::vector<float> KalmanFilter::getState() const {
    return {m_state[0][0], m_state[1][0], m_state[2][0], m_state[3][0]};
}

// 矩阵运算辅助函数（简化实现）
std::vector<std::vector<float>> KalmanFilter::matrixMultiply(
    const std::vector<std::vector<float>>& a,
    const std::vector<std::vector<float>>& b) const {
    
    int rows_a = a.size();
    int cols_a = a[0].size();
    int cols_b = b[0].size();
    
    std::vector<std::vector<float>> result(rows_a, std::vector<float>(cols_b, 0));
    
    for (int i = 0; i < rows_a; i++) {
        for (int j = 0; j < cols_b; j++) {
            for (int k = 0; k < cols_a; k++) {
                result[i][j] += a[i][k] * b[k][j];
            }
        }
    }
    
    return result;
}

std::vector<std::vector<float>> KalmanFilter::matrixAdd(
    const std::vector<std::vector<float>>& a,
    const std::vector<std::vector<float>>& b) const {
    
    int rows = a.size();
    int cols = a[0].size();
    
    std::vector<std::vector<float>> result(rows, std::vector<float>(cols));
    
    for (int i = 0; i < rows; i++) {
        for (int j = 0; j < cols; j++) {
            result[i][j] = a[i][j] + b[i][j];
        }
    }
    
    return result;
}

std::vector<std::vector<float>> KalmanFilter::matrixTranspose(
    const std::vector<std::vector<float>>& matrix) const {
    
    int rows = matrix.size();
    int cols = matrix[0].size();
    
    std::vector<std::vector<float>> result(cols, std::vector<float>(rows));
    
    for (int i = 0; i < rows; i++) {
        for (int j = 0; j < cols; j++) {
            result[j][i] = matrix[i][j];
        }
    }
    
    return result;
}

std::vector<std::vector<float>> KalmanFilter::matrixInverse(
    const std::vector<std::vector<float>>& matrix) const {
    // 简化实现，返回单位矩阵
    int size = matrix.size();
    std::vector<std::vector<float>> result(size, std::vector<float>(size, 0));
    for (int i = 0; i < size; i++) {
        result[i][i] = 1.0f;
    }
    return result;
}

// ObjectTracker实现
ObjectTracker::ObjectTracker() 
    : m_nextTrackId(1), m_iouThreshold(0.3f), m_maxAge(30), m_minHits(3) {
}

ObjectTracker::~ObjectTracker() {}

bool ObjectTracker::initialize() {
    LOGI("初始化对象跟踪器");
    clear();
    return true;
}

std::vector<TrackedObject> ObjectTracker::update(const std::vector<Detection>& detections) {
    // 预测所有跟踪对象的位置
    for (auto& track : m_trackedObjects) {
        track.age++;
        track.timeSinceUpdate++;
    }
    
    // 数据关联
    auto associations = associateDetectionsToTracks(detections, m_trackedObjects);
    
    // 更新匹配的跟踪
    std::vector<bool> detectionMatched(detections.size(), false);
    std::vector<bool> trackMatched(m_trackedObjects.size(), false);
    
    for (const auto& assoc : associations) {
        int detIdx = assoc.first;
        int trackIdx = assoc.second;
        
        m_trackedObjects[trackIdx].update(detections[detIdx]);
        detectionMatched[detIdx] = true;
        trackMatched[trackIdx] = true;
    }
    
    // 为未匹配的检测创建新跟踪
    for (size_t i = 0; i < detections.size(); i++) {
        if (!detectionMatched[i]) {
            auto newTrack = createNewTrack(detections[i]);
            m_trackedObjects.push_back(newTrack);
        }
    }
    
    // 移除过期的跟踪
    removeExpiredTracks();
    
    // 更新统计信息
    updateTrackingStats();
    
    // 返回活跃的跟踪对象
    std::vector<TrackedObject> activeTracks;
    for (const auto& track : m_trackedObjects) {
        if (track.hitStreak >= m_minHits || track.timeSinceUpdate <= 1) {
            activeTracks.push_back(track);
        }
    }
    
    LOGD("跟踪更新: %zu检测, %zu活跃跟踪", detections.size(), activeTracks.size());
    return activeTracks;
}

std::vector<TrackedObject> ObjectTracker::getTrackedObjects() const {
    return m_trackedObjects;
}

void ObjectTracker::clear() {
    m_trackedObjects.clear();
    m_nextTrackId = 1;
    m_stats = TrackingStats();
    m_trackLengths.clear();
}

void ObjectTracker::setParameters(float iouThreshold, int maxAge, int minHits) {
    m_iouThreshold = iouThreshold;
    m_maxAge = maxAge;
    m_minHits = minHits;
    
    LOGI("跟踪参数设置: IoU=%.2f, MaxAge=%d, MinHits=%d", 
         iouThreshold, maxAge, minHits);
}

ObjectTracker::TrackingStats ObjectTracker::getTrackingStats() const {
    return m_stats;
}

std::vector<std::pair<int, int>> ObjectTracker::associateDetectionsToTracks(
    const std::vector<Detection>& detections,
    const std::vector<TrackedObject>& tracks) {
    
    if (detections.empty() || tracks.empty()) {
        return {};
    }
    
    // 计算IoU矩阵
    auto iouMatrix = computeIoUMatrix(detections, tracks);
    
    // 简化的贪心匹配算法
    std::vector<std::pair<int, int>> associations;
    std::vector<bool> detectionUsed(detections.size(), false);
    std::vector<bool> trackUsed(tracks.size(), false);
    
    for (size_t d = 0; d < detections.size(); d++) {
        if (detectionUsed[d]) continue;
        
        int bestTrack = -1;
        float bestIoU = m_iouThreshold;
        
        for (size_t t = 0; t < tracks.size(); t++) {
            if (trackUsed[t]) continue;
            
            if (iouMatrix[d][t] > bestIoU) {
                bestIoU = iouMatrix[d][t];
                bestTrack = t;
            }
        }
        
        if (bestTrack >= 0) {
            associations.push_back({d, bestTrack});
            detectionUsed[d] = true;
            trackUsed[bestTrack] = true;
        }
    }
    
    return associations;
}

std::vector<std::vector<float>> ObjectTracker::computeIoUMatrix(
    const std::vector<Detection>& detections,
    const std::vector<TrackedObject>& tracks) const {
    
    std::vector<std::vector<float>> iouMatrix(detections.size(), 
                                             std::vector<float>(tracks.size(), 0));
    
    for (size_t d = 0; d < detections.size(); d++) {
        for (size_t t = 0; t < tracks.size(); t++) {
            iouMatrix[d][t] = detections[d].bbox.iou(tracks[t].bbox);
        }
    }
    
    return iouMatrix;
}

TrackedObject ObjectTracker::createNewTrack(const Detection& detection) {
    TrackedObject track;
    track.trackId = m_nextTrackId++;
    track.bbox = detection.bbox;
    track.className = detection.className;
    track.confidence = detection.confidence;
    track.age = 1;
    track.hitStreak = 1;
    track.timeSinceUpdate = 0;
    track.history.push_back(detection.bbox);
    
    LOGD("创建新跟踪: ID=%d, 类别=%s", track.trackId, track.className.c_str());
    return track;
}

void ObjectTracker::updateTrackingStats() {
    m_stats.activeTracks = 0;
    m_stats.totalTracks = m_trackedObjects.size();
    
    for (const auto& track : m_trackedObjects) {
        if (track.hitStreak >= m_minHits) {
            m_stats.activeTracks++;
        }
    }
    
    // 计算平均跟踪长度
    if (!m_trackLengths.empty()) {
        double sum = 0;
        for (int length : m_trackLengths) {
            sum += length;
        }
        m_stats.avgTrackLength = sum / m_trackLengths.size();
    }
}

void ObjectTracker::removeExpiredTracks() {
    auto it = m_trackedObjects.begin();
    while (it != m_trackedObjects.end()) {
        if (it->timeSinceUpdate > m_maxAge) {
            m_trackLengths.push_back(it->age);
            m_stats.lostTracks++;
            LOGD("移除过期跟踪: ID=%d, 年龄=%d", it->trackId, it->age);
            it = m_trackedObjects.erase(it);
        } else {
            ++it;
        }
    }
}

} // namespace fsl
