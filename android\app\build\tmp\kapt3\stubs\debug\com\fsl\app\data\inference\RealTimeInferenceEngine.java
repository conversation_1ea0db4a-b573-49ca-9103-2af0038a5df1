package com.fsl.app.data.inference;

import java.lang.System;

@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\u0014\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\b\u0007\u0018\u0000 82\u00020\u0001:\u000289B\u0019\b\u0007\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0002J\u0006\u0010#\u001a\u00020$J\u0018\u0010%\u001a\u00020\t2\u0006\u0010&\u001a\u00020\u001b2\u0006\u0010\'\u001a\u00020\u001bH\u0002J \u0010(\u001a\b\u0012\u0004\u0012\u00020*0)2\b\u0010+\u001a\u0004\u0018\u00010,2\u0006\u0010!\u001a\u00020\"H\u0002J\u0011\u0010-\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010.J\u001b\u0010/\u001a\u0004\u0018\u00010,2\u0006\u0010!\u001a\u00020\"H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00100J\u0019\u00101\u001a\u00020\t2\u0006\u00102\u001a\u00020\u0010H\u0082@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00103J\u0006\u00104\u001a\u00020$J\b\u00105\u001a\u00020$H\u0002J\u0006\u00106\u001a\u00020$J\u000e\u00107\u001a\u00020\u000b2\u0006\u0010!\u001a\u00020\"R\u0016\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0014R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0002\u001a\u0004\u0018\u00010\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\r0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0014R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006:"}, d2 = {"Lcom/fsl/app/data/inference/RealTimeInferenceEngine;", "", "nativeEngine", "Lcom/fsl/app/data/inference/NativeInferenceEngine;", "pytorchEngine", "Lcom/fsl/app/data/inference/PyTorchInferenceEngine;", "(Lcom/fsl/app/data/inference/NativeInferenceEngine;Lcom/fsl/app/data/inference/PyTorchInferenceEngine;)V", "_inferenceResults", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/fsl/app/data/inference/InferenceResult;", "_isRunning", "", "_performanceStats", "Lcom/fsl/app/data/inference/RealTimeInferenceEngine$PerformanceStats;", "inferenceQueue", "Lkotlinx/coroutines/channels/Channel;", "Lcom/fsl/app/data/inference/InferenceRequest;", "inferenceResults", "Lkotlinx/coroutines/flow/StateFlow;", "getInferenceResults", "()Lkotlinx/coroutines/flow/StateFlow;", "inferenceScope", "Lkotlinx/coroutines/CoroutineScope;", "isInitialized", "Ljava/util/concurrent/atomic/AtomicBoolean;", "isRunning", "lastFrameTime", "", "performanceStats", "getPerformanceStats", "requestIdCounter", "bitmapToFloatArray", "", "bitmap", "Landroid/graphics/Bitmap;", "cleanup", "", "createEmptyResult", "requestId", "timestamp", "generateTrackingResults", "", "Lcom/fsl/app/domain/model/TrackingResult;", "classificationResult", "Lcom/fsl/app/domain/model/ClassificationResult;", "initialize", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "performFewShotClassification", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "performInference", "request", "(Lcom/fsl/app/data/inference/InferenceRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "start", "startInferenceWorker", "stop", "submitInference", "Companion", "PerformanceStats", "app_debug"})
@javax.inject.Singleton
public final class RealTimeInferenceEngine {
    private final com.fsl.app.data.inference.NativeInferenceEngine nativeEngine = null;
    private final com.fsl.app.data.inference.PyTorchInferenceEngine pytorchEngine = null;
    @org.jetbrains.annotations.NotNull
    public static final com.fsl.app.data.inference.RealTimeInferenceEngine.Companion Companion = null;
    private static final java.lang.String TAG = "RealTimeInferenceEngine";
    private static final int MAX_QUEUE_SIZE = 1;
    private static final long INFERENCE_TIMEOUT_MS = 50L;
    private static final int TARGET_FPS = 15;
    private static final long FRAME_INTERVAL_MS = 66L;
    private final kotlinx.coroutines.CoroutineScope inferenceScope = null;
    private final kotlinx.coroutines.channels.Channel<com.fsl.app.data.inference.InferenceRequest> inferenceQueue = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.data.inference.InferenceResult> _inferenceResults = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.data.inference.InferenceResult> inferenceResults = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<com.fsl.app.data.inference.RealTimeInferenceEngine.PerformanceStats> _performanceStats = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.fsl.app.data.inference.RealTimeInferenceEngine.PerformanceStats> performanceStats = null;
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isRunning = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRunning = null;
    private final java.util.concurrent.atomic.AtomicBoolean isInitialized = null;
    private long requestIdCounter = 0L;
    private long lastFrameTime = 0L;
    
    @javax.inject.Inject
    public RealTimeInferenceEngine(@org.jetbrains.annotations.Nullable
    com.fsl.app.data.inference.NativeInferenceEngine nativeEngine, @org.jetbrains.annotations.NotNull
    com.fsl.app.data.inference.PyTorchInferenceEngine pytorchEngine) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.data.inference.InferenceResult> getInferenceResults() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.fsl.app.data.inference.RealTimeInferenceEngine.PerformanceStats> getPerformanceStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRunning() {
        return null;
    }
    
    /**
     * 初始化推理引擎
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object initialize(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> continuation) {
        return null;
    }
    
    /**
     * 启动推理引擎
     */
    public final void start() {
    }
    
    /**
     * 停止推理引擎
     */
    public final void stop() {
    }
    
    /**
     * 提交推理请求（非阻塞，带帧率控制）
     */
    public final boolean submitInference(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
        return false;
    }
    
    /**
     * 启动推理工作线程
     */
    private final void startInferenceWorker() {
    }
    
    /**
     * 执行实际推理 - 完全按照easyfsl pipeline
     */
    private final java.lang.Object performInference(com.fsl.app.data.inference.InferenceRequest request, kotlin.coroutines.Continuation<? super com.fsl.app.data.inference.InferenceResult> continuation) {
        return null;
    }
    
    /**
     * 执行Few-Shot分类 - 严格按照easyfsl流程
     * 1. compute_features(query_images)
     * 2. l2_distance_to_prototypes(query_features)
     * 3. softmax_if_specified(scores)
     */
    private final java.lang.Object performFewShotClassification(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super com.fsl.app.domain.model.ClassificationResult> continuation) {
        return null;
    }
    
    /**
     * 生成跟踪结果 - 基于分类结果创建边界框
     */
    private final java.util.List<com.fsl.app.domain.model.TrackingResult> generateTrackingResults(com.fsl.app.domain.model.ClassificationResult classificationResult, android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 创建空结果
     */
    private final com.fsl.app.data.inference.InferenceResult createEmptyResult(long requestId, long timestamp) {
        return null;
    }
    
    /**
     * 将Bitmap转换为FloatArray
     */
    private final float[] bitmapToFloatArray(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0006\u0012\b\b\u0002\u0010\b\u001a\u00020\u0006\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\nH\u00c6\u0003JE\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001b\u001a\u00020\n2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u0006H\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000f\u00a8\u0006 "}, d2 = {"Lcom/fsl/app/data/inference/RealTimeInferenceEngine$PerformanceStats;", "", "avgInferenceTime", "", "fps", "totalInferences", "", "droppedFrames", "queueSize", "isNativeEngineAvailable", "", "(DDIIIZ)V", "getAvgInferenceTime", "()D", "getDroppedFrames", "()I", "getFps", "()Z", "getQueueSize", "getTotalInferences", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
    public static final class PerformanceStats {
        private final double avgInferenceTime = 0.0;
        private final double fps = 0.0;
        private final int totalInferences = 0;
        private final int droppedFrames = 0;
        private final int queueSize = 0;
        private final boolean isNativeEngineAvailable = false;
        
        @org.jetbrains.annotations.NotNull
        public final com.fsl.app.data.inference.RealTimeInferenceEngine.PerformanceStats copy(double avgInferenceTime, double fps, int totalInferences, int droppedFrames, int queueSize, boolean isNativeEngineAvailable) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public PerformanceStats() {
            super();
        }
        
        public PerformanceStats(double avgInferenceTime, double fps, int totalInferences, int droppedFrames, int queueSize, boolean isNativeEngineAvailable) {
            super();
        }
        
        public final double component1() {
            return 0.0;
        }
        
        public final double getAvgInferenceTime() {
            return 0.0;
        }
        
        public final double component2() {
            return 0.0;
        }
        
        public final double getFps() {
            return 0.0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int getTotalInferences() {
            return 0;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int getDroppedFrames() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int getQueueSize() {
            return 0;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final boolean isNativeEngineAvailable() {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/fsl/app/data/inference/RealTimeInferenceEngine$Companion;", "", "()V", "FRAME_INTERVAL_MS", "", "INFERENCE_TIMEOUT_MS", "MAX_QUEUE_SIZE", "", "TAG", "", "TARGET_FPS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}