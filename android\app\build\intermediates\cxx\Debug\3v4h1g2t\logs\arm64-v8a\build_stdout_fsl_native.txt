ninja: Entering directory `F:\geek\fsl\android\app\.cxx\Debug\3v4h1g2t\arm64-v8a'
[1/8] Building CXX object CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp:175:57: warning: implicit conversion from 'int' to 'float' changes value from 2147483647 to 2147483648 [-Wimplicit-const-int-float-conversion]
  175 |         conv1Weights[i] = (static_cast<float>(rand()) / RAND_MAX - 0.5f) * 0.1f;
      |                                                       ~ ^~~~~~~~
D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/stdlib.h:132:18: note: expanded from macro 'RAND_MAX'
  132 | #define RAND_MAX 0x7fffffff
      |                  ^~~~~~~~~~
2 warnings generated.
[2/8] Building CXX object CMakeFiles/fsl_native.dir/image_processor.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[3/8] Building CXX object CMakeFiles/fsl_native.dir/jni_interface.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[4/8] Building CXX object CMakeFiles/fsl_native.dir/feature_extractor.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[5/8] Building CXX object CMakeFiles/fsl_native.dir/prototypical_network.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[6/8] Building CXX object CMakeFiles/fsl_native.dir/object_tracker.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
1 warning generated.
[7/8] Building CXX object CMakeFiles/fsl_native.dir/fsl_inference.cpp.o
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
F:/geek/fsl/android/app/src/main/cpp/fsl_inference.cpp:106:57: warning: format specifies type 'long' but the argument has type 'rep' (aka 'long long') [-Wformat]
  105 |         LOGI("Classification result: %s (%.2f%%) in %ldms",
      |                                                     ~~~
      |                                                     %lld
  106 |              result.first.c_str(), result.second * 100, duration.count());
      |                                                         ^~~~~~~~~~~~~~~~
F:/geek/fsl/android/app/src/main/cpp/fsl_inference.cpp:23:66: note: expanded from macro 'LOGI'
   23 | #define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
      |                                                                  ^~~~~~~~~~~
2 warnings generated.
[8/8] Linking CXX shared library F:\geek\fsl\android\app\build\intermediates\cxx\Debug\3v4h1g2t\obj\arm64-v8a\libfsl_native.so
FAILED: F:/geek/fsl/android/app/build/intermediates/cxx/Debug/3v4h1g2t/obj/arm64-v8a/libfsl_native.so 
cmd.exe /C "cd . && D:\androidsdk\ndk\27.0.11718014\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -fPIC -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -shared -Wl,-soname,libfsl_native.so -o F:\geek\fsl\android\app\build\intermediates\cxx\Debug\3v4h1g2t\obj\arm64-v8a\libfsl_native.so CMakeFiles/fsl_native.dir/fsl_inference.cpp.o CMakeFiles/fsl_native.dir/prototypical_network.cpp.o CMakeFiles/fsl_native.dir/feature_extractor.cpp.o CMakeFiles/fsl_native.dir/image_processor.cpp.o CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o CMakeFiles/fsl_native.dir/object_tracker.cpp.o CMakeFiles/fsl_native.dir/jni_interface.cpp.o  D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/27/liblog.so  D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/27/libandroid.so  D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/27/libneuralnetworks.so  -ljnigraphics  -latomic -lm && cd ."
ld.lld: error: undefined symbol: fsl::NNAPIEngine::compileModel()
>>> referenced by nnapi_engine.cpp:71 (F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp:71)
>>>               CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o:(fsl::NNAPIEngine::initialize())
clang++: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.
