ninja: Entering directory `F:\geek\fsl\android\app\.cxx\Debug\3v4h1g2t\arm64-v8a'
[1/3] Building CXX object CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o
FAILED: CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o 
D:\androidsdk\ndk\27.0.11718014\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -MD -MT CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o -MF CMakeFiles\fsl_native.dir\nnapi_engine.cpp.o.d -o CMakeFiles/fsl_native.dir/nnapi_engine.cpp.o -c F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp:175:57: warning: implicit conversion from 'int' to 'float' changes value from 2147483647 to 2147483648 [-Wimplicit-const-int-float-conversion]
  175 |         conv1Weights[i] = (static_cast<float>(rand()) / RAND_MAX - 0.5f) * 0.1f;
      |                                                       ~ ^~~~~~~~
D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/stdlib.h:132:18: note: expanded from macro 'RAND_MAX'
  132 | #define RAND_MAX 0x7fffffff
      |                  ^~~~~~~~~~
F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp:416:64: error: use of undeclared identifier 'PROT_READ'
  416 |     int result = ANeuralNetworksMemory_createFromFd(modelSize, PROT_READ, 0, 0, &m_modelMemory);
      |                                                                ^
F:/geek/fsl/android/app/src/main/cpp/nnapi_engine.cpp:457:14: error: 'ANeuralNetworksExecution_compute' is unavailable: introduced in Android 29
  457 |     result = ANeuralNetworksExecution_compute(m_execution);
      |              ^
D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/android/NeuralNetworks.h:600:5: note: 'ANeuralNetworksExecution_compute' has been explicitly marked unavailable here
  600 | int ANeuralNetworksExecution_compute(ANeuralNetworksExecution* execution) __NNAPI_INTRODUCED_IN(29);
      |     ^
2 warnings and 2 errors generated.
[2/3] Building CXX object CMakeFiles/fsl_native.dir/jni_interface.cpp.o
FAILED: CMakeFiles/fsl_native.dir/jni_interface.cpp.o 
D:\androidsdk\ndk\27.0.11718014\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android27 --sysroot=D:/androidsdk/ndk/27.0.11718014/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID -DUSE_NNAPI -D__ANDROID_API__=27 -Dfsl_native_EXPORTS -IF:/geek/fsl/android/app/src/main/cpp/include -IF:/geek/fsl/android/app/src/main/cpp/eigen -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fexceptions -frtti -DUSE_NNAPI -fno-limit-debug-info  -fPIC -O3 -ffast-math -DEIGEN_NO_DEBUG -DEIGEN_DONT_PARALLELIZE -DUSE_NNAPI -std=c++17 -MD -MT CMakeFiles/fsl_native.dir/jni_interface.cpp.o -MF CMakeFiles\fsl_native.dir\jni_interface.cpp.o.d -o CMakeFiles/fsl_native.dir/jni_interface.cpp.o -c F:/geek/fsl/android/app/src/main/cpp/jni_interface.cpp
In file included from <built-in>:463:
<command line>:3:9: warning: '__ANDROID_API__' macro redefined [-Wmacro-redefined]
    3 | #define __ANDROID_API__ 27
      |         ^
<built-in>:454:9: note: previous definition is here
  454 | #define __ANDROID_API__ __ANDROID_MIN_SDK_VERSION__
      |         ^
F:/geek/fsl/android/app/src/main/cpp/jni_interface.cpp:526:17: error: incomplete type 'fsl::NNAPIEngine' named in nested name specifier
  526 |     return fsl::NNAPIEngine::isNNAPIAvailable();
      |            ~~~~~^~~~~~~~~~~~~
F:/geek/fsl/android/app/src/main/cpp/include/fsl_inference.h:24:7: note: forward declaration of 'fsl::NNAPIEngine'
   24 | class NNAPIEngine;
      |       ^
1 warning and 1 error generated.
ninja: build stopped: subcommand failed.
