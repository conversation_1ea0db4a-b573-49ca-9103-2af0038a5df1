/**
 * 文件管理器
 * 
 * 负责文件的保存、加载和管理
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.data.utils

import android.content.Context
import android.graphics.Bitmap
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val IMAGES_DIR = "images"
        private const val MODELS_DIR = "models"
        private const val EXPORTS_DIR = "exports"
    }
    
    private val imagesDir: File by lazy {
        File(context.filesDir, IMAGES_DIR).apply { mkdirs() }
    }
    
    private val modelsDir: File by lazy {
        File(context.filesDir, MODELS_DIR).apply { mkdirs() }
    }
    
    private val exportsDir: File by lazy {
        File(context.filesDir, EXPORTS_DIR).apply { mkdirs() }
    }
    
    /**
     * 保存Bitmap到文件
     */
    fun saveBitmap(bitmap: Bitmap, fileName: String): String {
        val file = File(imagesDir, fileName)
        try {
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }
        } catch (e: IOException) {
            throw IOException("保存图像失败: ${e.message}", e)
        }
        return file.absolutePath
    }
    
    /**
     * 删除类别相关的所有文件
     */
    fun deleteClassFiles(className: String) {
        try {
            imagesDir.listFiles()?.forEach { file ->
                if (file.name.startsWith("${className}_")) {
                    file.delete()
                }
            }
        } catch (e: Exception) {
            // 记录错误但不抛出异常
            android.util.Log.e("FileManager", "删除类别文件失败: $className", e)
        }
    }
    
    /**
     * 导出模型
     */
    fun exportModel(): String {
        val exportFile = File(exportsDir, "model_${System.currentTimeMillis()}.zip")
        
        try {
            // 这里应该实现真正的模型导出逻辑
            // 目前创建一个空文件作为占位符
            exportFile.createNewFile()
            
            return exportFile.absolutePath
        } catch (e: IOException) {
            throw IOException("导出模型失败: ${e.message}", e)
        }
    }
    
    /**
     * 导入模型
     */
    fun importModel(modelPath: String) {
        try {
            val sourceFile = File(modelPath)
            if (!sourceFile.exists()) {
                throw IllegalArgumentException("模型文件不存在: $modelPath")
            }
            
            val targetFile = File(modelsDir, "imported_model.zip")
            sourceFile.copyTo(targetFile, overwrite = true)
            
        } catch (e: Exception) {
            throw IOException("导入模型失败: ${e.message}", e)
        }
    }
    
    /**
     * 获取模型大小
     */
    fun getModelSize(): Long {
        return try {
            modelsDir.listFiles()?.sumOf { it.length() } ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * 获取模型最后修改时间
     */
    fun getModelLastModified(): Long {
        return try {
            modelsDir.listFiles()?.maxOfOrNull { it.lastModified() } ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * 清理临时文件
     */
    fun cleanupTempFiles() {
        try {
            val tempDir = File(context.cacheDir, "temp")
            if (tempDir.exists()) {
                tempDir.deleteRecursively()
            }
        } catch (e: Exception) {
            android.util.Log.e("FileManager", "清理临时文件失败", e)
        }
    }
    
    /**
     * 获取存储使用情况
     */
    fun getStorageUsage(): StorageInfo {
        return try {
            val imagesSize = imagesDir.listFiles()?.sumOf { it.length() } ?: 0L
            val modelsSize = modelsDir.listFiles()?.sumOf { it.length() } ?: 0L
            val exportsSize = exportsDir.listFiles()?.sumOf { it.length() } ?: 0L
            
            StorageInfo(
                imagesSize = imagesSize,
                modelsSize = modelsSize,
                exportsSize = exportsSize,
                totalSize = imagesSize + modelsSize + exportsSize
            )
        } catch (e: Exception) {
            StorageInfo(0L, 0L, 0L, 0L)
        }
    }
    
    /**
     * 检查存储空间是否足够
     */
    fun hasEnoughSpace(requiredBytes: Long): Boolean {
        return try {
            val availableBytes = context.filesDir.freeSpace
            availableBytes >= requiredBytes
        } catch (e: Exception) {
            false
        }
    }
}

/**
 * 存储信息数据类
 */
data class StorageInfo(
    val imagesSize: Long,
    val modelsSize: Long,
    val exportsSize: Long,
    val totalSize: Long
) {
    
    /**
     * 格式化大小显示
     */
    fun formatSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "%.1f GB".format(bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> "%.1f MB".format(bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> "%.1f KB".format(bytes / 1024.0)
            else -> "$bytes B"
        }
    }
    
    fun getFormattedTotalSize(): String = formatSize(totalSize)
    fun getFormattedImagesSize(): String = formatSize(imagesSize)
    fun getFormattedModelsSize(): String = formatSize(modelsSize)
    fun getFormattedExportsSize(): String = formatSize(exportsSize)
}
