/**
 * 增量学习用例
 *
 * 处理增量学习相关的业务逻辑
 *
 * <AUTHOR> Assistant
 * @date 2024
 */

package com.fsl.app.domain.usecase

import android.graphics.Bitmap
import com.fsl.app.domain.model.LearnedClass
import com.fsl.app.domain.repository.IImageProcessingRepository
import com.fsl.app.domain.repository.IInferenceRepository
import com.fsl.app.domain.repository.IModelRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IncrementalLearningUseCase @Inject constructor(
    private val modelRepository: IModelRepository,
    private val inferenceRepository: IInferenceRepository,
    private val imageProcessingRepository: IImageProcessingRepository
) {

    /**
     * 添加新类别
     */
    suspend fun addClass(className: String, samples: List<Bitmap>): Result<Unit> {
        return withContext(Dispatchers.Default) {
            try {
                // 验证输入
                if (className.isBlank()) {
                    return@withContext Result.failure(IllegalArgumentException("类别名称不能为空"))
                }

                if (samples.isEmpty()) {
                    return@withContext Result.failure(IllegalArgumentException("样本列表不能为空"))
                }

                // 检查类别是否已存在
                if (modelRepository.classExists(className)) {
                    return@withContext Result.failure(IllegalArgumentException("类别已存在: $className"))
                }

                // 数据增强
                val augmentedSamples = samples.flatMap { bitmap ->
                    imageProcessingRepository.augmentImage(bitmap)
                }

                // 添加到仓库
                val result = modelRepository.addClass(className, augmentedSamples)

                if (result.isSuccess) {
                    // 保存模型
                    inferenceRepository.saveModel()
                }

                result
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 添加样本到现有类别
     */
    suspend fun addSamples(className: String, samples: List<Bitmap>): Result<Unit> {
        return withContext(Dispatchers.Default) {
            try {
                // 验证输入
                if (className.isBlank()) {
                    return@withContext Result.failure(IllegalArgumentException("类别名称不能为空"))
                }

                if (samples.isEmpty()) {
                    return@withContext Result.failure(IllegalArgumentException("样本列表不能为空"))
                }

                // 检查类别是否存在
                if (!modelRepository.classExists(className)) {
                    return@withContext Result.failure(IllegalArgumentException("类别不存在: $className"))
                }

                // 数据增强
                val augmentedSamples = samples.flatMap { bitmap ->
                    imageProcessingRepository.augmentImage(bitmap)
                }

                // 添加样本到现有类别
                val result = modelRepository.addSamplesToClass(className, augmentedSamples)

                if (result.isSuccess) {
                    // 保存模型
                    inferenceRepository.saveModel()
                }

                result
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 删除类别
     */
    suspend fun deleteClass(className: String): Result<Unit> {
        return withContext(Dispatchers.Default) {
            try {
                // 验证输入
                if (className.isBlank()) {
                    return@withContext Result.failure(IllegalArgumentException("类别名称不能为空"))
                }

                // 检查类别是否存在
                if (!modelRepository.classExists(className)) {
                    return@withContext Result.failure(IllegalArgumentException("类别不存在: $className"))
                }

                // 删除类别
                val result = modelRepository.deleteClass(className)

                if (result.isSuccess) {
                    // 保存模型
                    inferenceRepository.saveModel()
                }

                result
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 获取已学习的类别
     */
    fun getLearnedClasses(): Flow<List<LearnedClass>> {
        return modelRepository.getLearnedClasses()
    }

    /**
     * 获取学习统计信息
     */
    suspend fun getLearningStats(): Result<LearningStats> {
        return withContext(Dispatchers.Default) {
            try {
                val classCount = modelRepository.getClassCount()
                val totalSamples = modelRepository.getTotalSampleCount()
                val learnedClasses = modelRepository.getLearnedClasses().first()

                val averageAccuracy = if (learnedClasses.isNotEmpty()) {
                    // 模拟准确率计算，基于样本数量
                    val accuracies = learnedClasses.map {
                        0.8f + (it.sampleCount * 0.01f).coerceAtMost(0.15f)
                    }
                    accuracies.average().toFloat()
                } else {
                    0f
                }

                val averageSamplesPerClass = if (classCount > 0) {
                    totalSamples.toFloat() / classCount
                } else {
                    0f
                }

                val stats = LearningStats(
                    totalClasses = classCount,
                    totalSamples = totalSamples,
                    averageAccuracy = averageAccuracy,
                    averageSamplesPerClass = averageSamplesPerClass,
                    modelSize = getModelSize()
                )

                Result.success(stats)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 验证类别名称
     */
    fun validateClassName(className: String): ValidationResult {
        return when {
            className.isBlank() -> ValidationResult.Invalid("类别名称不能为空")
            className.length < 2 -> ValidationResult.Invalid("类别名称至少需要2个字符")
            className.length > 50 -> ValidationResult.Invalid("类别名称不能超过50个字符")
            !className.matches(Regex("^[\\u4e00-\\u9fa5a-zA-Z0-9_\\s]+$")) ->
                ValidationResult.Invalid("类别名称只能包含中文、英文、数字、下划线和空格")
            else -> ValidationResult.Valid
        }
    }

    /**
     * 验证样本数量
     */
    fun validateSampleCount(count: Int): ValidationResult {
        return when {
            count <= 0 -> ValidationResult.Invalid("样本数量必须大于0")
            count < 3 -> ValidationResult.Warning("建议至少提供3个样本以获得更好的学习效果")
            count > 100 -> ValidationResult.Warning("样本数量过多可能影响性能")
            else -> ValidationResult.Valid
        }
    }

    /**
     * 获取模型大小（模拟实现）
     */
    private suspend fun getModelSize(): Long {
        return try {
            // 这里应该获取实际的模型大小
            // 目前返回模拟值
            val classCount = modelRepository.getClassCount()
            val baseSize = 10 * 1024 * 1024L // 10MB基础大小
            val classSize = classCount * 1024 * 1024L // 每个类别1MB
            baseSize + classSize
        } catch (e: Exception) {
            0L
        }
    }
}

/**
 * 学习统计信息
 */
data class LearningStats(
    val totalClasses: Int,
    val totalSamples: Int,
    val averageAccuracy: Float,
    val averageSamplesPerClass: Float,
    val modelSize: Long
) {

    /**
     * 格式化模型大小
     */
    fun getFormattedModelSize(): String {
        return when {
            modelSize >= 1024 * 1024 * 1024 -> "%.1f GB".format(modelSize / (1024.0 * 1024.0 * 1024.0))
            modelSize >= 1024 * 1024 -> "%.1f MB".format(modelSize / (1024.0 * 1024.0))
            modelSize >= 1024 -> "%.1f KB".format(modelSize / 1024.0)
            else -> "$modelSize B"
        }
    }

    /**
     * 获取准确率百分比
     */
    fun getAccuracyPercentage(): String {
        return "${(averageAccuracy * 100).toInt()}%"
    }
}

/**
 * 验证结果
 */
sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Invalid(val message: String) : ValidationResult()
    data class Warning(val message: String) : ValidationResult()
}
