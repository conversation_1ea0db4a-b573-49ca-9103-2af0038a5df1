// init.gradle - 强制使用腾讯云镜像加速下载

allprojects {
    repositories {
        // 移除所有默认仓库
        all { ArtifactRepository repo ->
            if (repo instanceof MavenArtifactRepository) {
                def url = repo.url.toString()
                if (url.startsWith('https://repo1.maven.org/maven2') || 
                    url.startsWith('https://repo.maven.apache.org/maven2') ||
                    url.startsWith('https://jcenter.bintray.com') ||
                    url.startsWith('https://dl.google.com/dl/android/maven2')) {
                    remove repo
                }
            }
        }
        
        // 添加腾讯云镜像，优先级最高
        maven { 
            name 'TencentMavenPublic'
            url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' 
        }
        maven { 
            name 'TencentGoogle'
            url 'https://mirrors.cloud.tencent.com/repository/maven/google/' 
        }
        maven { 
            name 'TencentJCenter'
            url 'https://mirrors.cloud.tencent.com/repository/maven/jcenter/' 
        }
        
        // 备用镜像
        maven { 
            name 'AliMavenPublic'
            url 'https://maven.aliyun.com/repository/public/' 
        }
        maven { 
            name 'AliGoogle'
            url 'https://maven.aliyun.com/repository/google/' 
        }
        
        // 最后添加官方仓库作为备用
        google()
        mavenCentral()
    }
}

// 强制替换buildscript仓库
gradle.allprojects { project ->
    project.buildscript {
        repositories {
            // 清除默认仓库
            clear()
            
            // 添加腾讯云镜像
            maven { 
                name 'TencentMavenPublic'
                url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' 
            }
            maven { 
                name 'TencentGoogle'
                url 'https://mirrors.cloud.tencent.com/repository/maven/google/' 
            }
            
            // 备用
            google()
            mavenCentral()
        }
    }
}

// 设置下载超时
gradle.allprojects { project ->
    project.repositories.all { repo ->
        if (repo instanceof MavenArtifactRepository) {
            repo.metadataSources {
                mavenPom()
                artifact()
            }
        }
    }
}
